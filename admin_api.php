<?php
require_once("auth_functions.php");

if($_GET['f'] == 'servers'){
	$user_id = auth_user();

	$sql = "SELECT orders.assigned_server_type AS assigned_server_type, orders.assigned_server_id AS assigned_server_id, orders.id AS id, orders.order_type AS order_type, orders.recurring_price AS recurring_price, orders.expiration_date AS expiration_date, users.first_name AS first_name, users.last_name AS last_name, orders.owner_id AS owner_id, orders.config_type AS config_type, orders.config_id AS config_id, orders.assigned_server_id AS assigned_server_id, orders.status AS order_status  FROM `orders` JOIN users ON orders.owner_id = users.id WHERE `status` != 'cancelled' ";
	$sth = $pdo->prepare($sql);
	$sth->execute();

	while($row = $sth->fetch()){
		if($row['order_type'] == "dedicated" && $row['config_type'] == "predefined"){
			$sql = "SELECT countries.long_name AS country_long_name, countries.short_name AS country_short_name FROM dedicated_configs JOIN countries ON dedicated_configs.country_id = countries.id WHERE dedicated_configs.id='".$row['config_id']."'";
			$sth2 = $pdo->prepare($sql);
			$sth2->execute();
			$row2 = $sth2->fetch();
		}

		$sql = "SELECT subnet from subnets WHERE assigned_server_id = '".$row['assigned_server_id']."'";
		$sth3 = $pdo->prepare($sql);
		$sth3->execute();
		$row3 = $sth3->fetch();

		if($row['assigned_server_type'] == "blade_node"){
			$sql = "SELECT * FROM bladeserver_nodes WHERE id=".$row['assigned_server_id'];
			$sth5 = $pdo->prepare($sql);
			$sth5->execute();
			$row5 = $sth5->fetch();
		}else{
			$sql = "SELECT * FROM servers WHERE id=".$row['assigned_server_id'];
			$sth5 = $pdo->prepare($sql);
			$sth5->execute();
			$row5 = $sth5->fetch();
		}

		$array[] = array("id" => $row['id'],"type" => ucfirst($row['order_type']),"price" => $row['recurring_price'],"expires" => explode(" ",$row['expiration_date'])[0],"customer" => $row['first_name']." ".$row['last_name'],"status" => $row5['status'], "location" => $row2['country_short_name'], "locationname" => $row2['country_long_name'], "subnet" => $row3['subnet']);
	}

	$json = json_encode($array);
	echo $json;
	die();

}elseif($_GET['f'] == 'dedicatedorder'){
?>[
  {
    "models": [{
      "id": "1",
      "cpu": "Dual E5-2680v4",
      "ram": "128GB DDR4 RAM",
      "price": "0",
      "checked": <?php if($_GET['model_id'] == "" OR $_GET['model_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "cpu": "Dual E5-2699v4",
      "ram": "256GB DDR4 RAM",
      "price": "250",
      "checked": <?php if($_GET['model_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "cpu": "Dual Gold 6148",
      "ram": "256GB DDR4 RAM",
      "price": "250",
      "checked": <?php if($_GET['model_id'] == "3") echo "true"; else echo "false"; ?>
    }]
    ,
    <?php if($_GET['model_id'] == '1' OR $_GET['model_id'] == ''){ ?>"storage": [{
      "id": "1",
      "name": "1 x 240GB SSD",
      "price": "0",
      "checked": <?php if($_GET['storage_id'] == "" OR $_GET['storage_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "8 x 4TB SSD",
      "price": "250",
      "checked": <?php if($_GET['storage_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "8 x 8TB SSD",
      "price": "250",
      "checked": <?php if($_GET['storage_id'] == "3") echo "true"; else echo "false"; ?>
    }]<?php }else{ ?>"storage": [{
      "id": "1",
      "name": "1 x 240GB SSD",
      "price": "0",
      "checked": <?php if($_GET['storage_id'] == "" OR $_GET['storage_id'] == "1") echo "true"; else echo "false"; ?>
    }]<?php } ?>,
    "bandwidth": [{
      "id": "1",
      "name": "5Gbps Unmetered",
      "price": "0",
      "checked": <?php if($_GET['bandwidth_id'] == "" OR $_GET['bandwidth_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "10Gbps Unmetered",
      "price": "250",
      "checked": <?php if($_GET['bandwidth_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "25Gbps Unmetered",
      "price": "250",
      "checked": <?php if($_GET['bandwidth_id'] == "3") echo "true"; else echo "false"; ?>
    }],
    "location": [{
      "id": "1",
      "name": "Bucharest",
      "stock": ">5 in stock",
      "stockcolor": "green",
      "flag": "ro",
      "price": "0",
      "checked": <?php if($_GET['location_id'] == "" OR $_GET['location_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "Amsterdam",
      "stock": "<5 in stock",
      "stockcolor": "green",
      "flag": "nl",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "Frankfurt",
      "stock": ">5 in 1 week",
      "stockcolor": "red",
      "flag": "de",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "3") echo "true"; else echo "false"; ?>
    },
    {
      "id": "4",
      "name": "London",
      "stock": ">5 in 2 weeks",
      "stockcolor": "red",
      "flag": "gb",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "4") echo "true"; else echo "false"; ?>
    },
    {
      "id": "5",
      "name": "Marseille",
      "stock": ">5 in 2 weeks",
      "stockcolor": "red",
      "flag": "fr",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "5") echo "true"; else echo "false"; ?>
    },
    {
      "id": "6",
      "name": "Vienna",
      "stock": ">5 in 2 weeks",
      "stockcolor": "red",
      "flag": "at",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "6") echo "true"; else echo "false"; ?>
    },
    {
      "id": "7",
      "name": "Madrid",
      "stock": ">5 in 1 week",
      "stockcolor": "red",
      "flag": "es",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "7") echo "true"; else echo "false"; ?>
    }],
    "subnet": [{
      "id": "1",
      "name": "/30 (1 usable IP)",
      "price": "0",
      "checked": <?php if($_GET['subnet_id'] == "" OR $_GET['subnet_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "/29 (5 usable IPs)",
      "price": "250",
      "checked": <?php if($_GET['subnet_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "/28 (13 usable IPs)",
      "price": "250",
      "checked": <?php if($_GET['subnet_id'] == "3") echo "true"; else echo "false"; ?>
    }],
    "os": [{
      "id": "1",
      "name": "Ubuntu 20.04 LTS",
      "icon": "ubuntu.png",
      "price": "0",
      "checked": <?php if($_GET['os_id'] == "" OR $_GET['os_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "Ubuntu 22.04 LTS",
      "icon": "ubuntu.png",
      "price": "250",
      "checked": <?php if($_GET['os_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "CentOS 8",
      "icon": "centos.png",
      "price": "250",
      "checked": <?php if($_GET['os_id'] == "3") echo "true"; else echo "false"; ?>
    }],
    "price": <?php if($_GET['model_id'] == 2) echo "499"; else echo "1000"; ?>,
    "delivery": <?php if($_GET['model_id'] == 2) echo "\"INSTANT\""; else echo "\"in 2 weeks\""; ?>

  }]
<?php
}elseif($_GET['f'] == 'cloudorder'){
?>[
  {
    "models": [{
      "id": "1",
      "cpu": "2 Dedicated vCPU",
      "ram": "4 GB RAM",
      "disk": "40 GB NVMe",
      "price": "0",
      "checked": <?php if($_GET['model_id'] == "" OR $_GET['model_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "cpu": "4 Dedicated vCPU",
      "ram": "8 GB RAM",
      "disk": "80 GB NVMe",
      "price": "250",
      "checked": <?php if($_GET['model_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "cpu": "8 Dedicated vCPU",
      "ram": "16 GB RAM",
      "disk": "160 GB NVMe",
      "price": "250",
      "checked": <?php if($_GET['model_id'] == "3") echo "true"; else echo "false"; ?>
    }]
    ,
    "os": [{
      "id": "1",
      "name": "Ubuntu 20.04 LTS",
      "icon": "ubuntu.png",
      "price": "0",
      "checked": <?php if($_GET['os_id'] == "" OR $_GET['os_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "Ubuntu 22.04 LTS",
      "icon": "ubuntu.png",
      "price": "250",
      "checked": <?php if($_GET['os_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "CentOS 8",
      "icon": "centos.png",
      "price": "250",
      "checked": <?php if($_GET['os_id'] == "3") echo "true"; else echo "false"; ?>
    }],
    "location": [{
      "id": "1",
      "name": "Bucharest",
      "flag": "ro",
      "price": "0",
      "checked": <?php if($_GET['location_id'] == "" OR $_GET['location_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "Amsterdam",
      "flag": "nl",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "Frankfurt",
      "flag": "de",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "3") echo "true"; else echo "false"; ?>
    },
    {
      "id": "4",
      "name": "London",
      "flag": "gb",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "4") echo "true"; else echo "false"; ?>
    },
    {
      "id": "5",
      "name": "Marseille",
      "flag": "fr",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "5") echo "true"; else echo "false"; ?>
    },
    {
      "id": "6",
      "name": "Vienna",
      "flag": "at",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "6") echo "true"; else echo "false"; ?>
    },
    {
      "id": "7",
      "name": "Madrid",
      "flag": "es",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "7") echo "true"; else echo "false"; ?>
    }],
    "ip": [{
      "id": "1",
      "name": "1 x IPv4",
      "price": "0",
      "checked": <?php if($_GET['ip_id'] == "" OR $_GET['ip_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "2 x IPv4",
      "price": "2",
      "checked": <?php if($_GET['ip_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "3 x IPv4",
      "price": "4",
      "checked": <?php if($_GET['ip_id'] == "3") echo "true"; else echo "false"; ?>
    },
    {
      "id": "4",
      "name": "4 x IPv4",
      "price": "6",
      "checked": <?php if($_GET['ip_id'] == "4") echo "true"; else echo "false"; ?>
    },
    {
      "id": "5",
      "name": "5 x IPv4",
      "price": "8",
      "checked": <?php if($_GET['ip_id'] == "5") echo "true"; else echo "false"; ?>
    }],
    "price": <?php if($_GET['model_id'] == 2) echo "79"; else echo "49"; ?>,
    "delivery": "INSTANT"

  }]
<?php
}elseif($_GET['f'] == 'colocationorder'){
?>[
  {
    "rack": [{
      "id": "1",
      "size": "1 Rack Unit",
      "price": "0",
      "checked": <?php if($_GET['rack_id'] == "" OR $_GET['rack_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "size": "2 Rack Units",
      "price": "250",
      "checked": <?php if($_GET['rack_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "size": "10 Rack Units",
      "ram": "256GB DDR4 RAM",
      "price": "250",
      "checked": <?php if($_GET['rack_id'] == "3") echo "true"; else echo "false"; ?>
    },
    {
      "id": "4",
      "size": "20 Rack Units",
      "ram": "256GB DDR4 RAM",
      "price": "250",
      "checked": <?php if($_GET['rack_id'] == "4") echo "true"; else echo "false"; ?>
    },
    {
      "id": "5",
      "size": "Fuck Rack (42U)",
      "ram": "256GB DDR4 RAM",
      "price": "250",
      "checked": <?php if($_GET['rack_id'] == "5") echo "true"; else echo "false"; ?>
    }]
    ,
    <?php if($_GET['rack_id'] == '1' OR $_GET['rack_id'] == ''){ ?>

    "power": [{
      "id": "1",
      "name": "0.25kW (Redundand)",
      "price": "0",
      "checked": <?php if($_GET['power_id'] == "" OR $_GET['power_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "0.5kW (Redundand)",
      "price": "250",
      "checked": <?php if($_GET['power_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "1kW (Redundand)",
      "price": "250",
      "checked": <?php if($_GET['power_id'] == "3") echo "true"; else echo "false"; ?>
    }]<?php }else{ ?>"power": [{
      "id": "1",
      "name": "2kW (Redundand)",
      "price": "0",
      "checked": <?php if($_GET['power_id'] == "" OR $_GET['power_id'] == "1") echo "true"; else echo "false"; ?>
    }]<?php } ?>,
    "bandwidth": [{
      "id": "1",
      "name": "10Gbps Unmetered",
      "price": "0",
      "checked": <?php if($_GET['bandwidth_id'] == "" OR $_GET['bandwidth_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "25Gbps Unmetered",
      "price": "250",
      "checked": <?php if($_GET['bandwidth_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "50Gbps Unmetered",
      "price": "250",
      "checked": <?php if($_GET['bandwidth_id'] == "3") echo "true"; else echo "false"; ?>
    }],
    "location": [{
      "id": "1",
      "name": "Bucharest",
      "stock": "in 24 hours",
      "stockcolor": "green",
      "flag": "ro",
      "price": "0",
      "checked": <?php if($_GET['location_id'] == "" OR $_GET['location_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "Amsterdam",
      "stock": "in 24 hours",
      "stockcolor": "green",
      "flag": "nl",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "Frankfurt",
      "stock": "in 24 hours",
      "stockcolor": "green",
      "flag": "de",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "3") echo "true"; else echo "false"; ?>
    },
    {
      "id": "4",
      "name": "London",
      "stock": "in 4 weeks",
      "stockcolor": "red",
      "flag": "gb",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "4") echo "true"; else echo "false"; ?>
    },
    {
      "id": "5",
      "name": "Marseille",
      "stock": "in 4 weeks",
      "stockcolor": "red",
      "flag": "fr",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "5") echo "true"; else echo "false"; ?>
    },
    {
      "id": "6",
      "name": "Vienna",
      "stock": "in 4 weeks",
      "stockcolor": "red",
      "flag": "at",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "6") echo "true"; else echo "false"; ?>
    },
    {
      "id": "7",
      "name": "Madrid",
      "stock": "in 4 weeks",
      "stockcolor": "red",
      "flag": "es",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "7") echo "true"; else echo "false"; ?>
    }],
    "subnet": [{
      "id": "1",
      "name": "/30 (1 usable IP)",
      "price": "0",
      "checked": <?php if($_GET['subnet_id'] == "" OR $_GET['subnet_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "/29 (5 usable IPs)",
      "price": "250",
      "checked": <?php if($_GET['subnet_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "/28 (13 usable IPs)",
      "price": "250",
      "checked": <?php if($_GET['subnet_id'] == "3") echo "true"; else echo "false"; ?>
    }],
    "contract": [{
      "id": "1",
      "name": "12 Months",
      "price": "0",
      "checked": <?php if($_GET['contract_id'] == "" OR $_GET['contract_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "24 Months",
      "price": "250",
      "checked": <?php if($_GET['contract_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "36 Months",
      "price": "250",
      "checked": <?php if($_GET['contract_id'] == "3") echo "true"; else echo "false"; ?>
    }],
    "price": <?php if($_GET['rack_id'] == 2) echo "499"; else echo "1000"; ?>,
    "delivery": <?php if($_GET['rack_id'] == 2) echo "\"in 24 hours\""; else echo "\"in 4 weeks\""; ?>

  }]
<?php
}elseif($_GET['f'] == 'colocationassetorder'){
?>[
  {
    "cat": [{
      "id": "1",
      "name": "Rackable Server",
      "price": "0",
      "checked": <?php if($_GET['cat_id'] == "" OR $_GET['cat_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "Blade Server",
      "price": "250",
      "checked": <?php if($_GET['cat_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "Switch",
      "ram": "256GB DDR4 RAM",
      "price": "250",
      "checked": <?php if($_GET['cat_id'] == "3") echo "true"; else echo "false"; ?>
    }]
    ,
    <?php if($_GET['cat_id'] == '1' OR $_GET['cat_id'] == ''){ ?>

    "asset": [{
      "id": "1",
      "name": "Dual E5-2680v4",
      "price": "0",
      "checked": <?php if($_GET['asset_id'] == "" OR $_GET['asset_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "Dual E5-2699v4",
      "price": "250",
      "checked": <?php if($_GET['asset_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "Dual Gold 6148",
      "price": "250",
      "checked": <?php if($_GET['asset_id'] == "3") echo "true"; else echo "false"; ?>
    }]<?php }else{ ?>"asset": [{
      "id": "2",
      "name": "4 x Dual E5-2699v4",
      "price": "250",
      "checked": <?php if($_GET['asset_id'] == "1") echo "true"; else echo "false"; ?>
    }]<?php } ?>,
    "colo": [{
      "id": "1",
      "name": "Rack #105 AM6",
      "price": "0",
      "checked": <?php if($_GET['colo_id'] == "" OR $_GET['colo_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "Rack #55 BC1",
      "price": "250",
      "checked": <?php if($_GET['colo_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "Rack #142 FR5",
      "price": "250",
      "checked": <?php if($_GET['colo_id'] == "3") echo "true"; else echo "false"; ?>
    }],
    "units": [{
      "id": "1",
      "name": "RU 1-2",
      "stock": "in 24 hours",
      "stockcolor": "green",
      "flag": "ro",
      "price": "0",
      "checked": <?php if($_GET['units_id'] == "" OR $_GET['units_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "RU 3-4",
      "stock": "in 24 hours",
      "stockcolor": "green",
      "flag": "nl",
      "price": "250",
      "checked": <?php if($_GET['units_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "RU 5-6",
      "stock": "in 24 hours",
      "stockcolor": "green",
      "flag": "de",
      "price": "250",
      "checked": <?php if($_GET['units_id'] == "3") echo "true"; else echo "false"; ?>
    },
    {
      "id": "4",
      "name": "RU 7-8",
      "stock": "in 24 hours",
      "stockcolor": "green",
      "flag": "de",
      "price": "250",
      "checked": <?php if($_GET['units_id'] == "4") echo "true"; else echo "false"; ?>
    },
    {
      "id": "5",
      "name": "RU 9-10",
      "stock": "in 4 weeks",
      "stockcolor": "red",
      "flag": "es",
      "price": "250",
      "checked": <?php if($_GET['units_id'] == "5") echo "true"; else echo "false"; ?>
    }],
    "renting": [{
      "id": "1",
      "name": "Regular Rental",
      "price": "0",
      "checked": <?php if($_GET['renting_id'] == "" OR $_GET['renting_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "Lease to Own",
      "price": "250",
      "checked": <?php if($_GET['renting_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "Buy Now",
      "price": "250",
      "checked": <?php if($_GET['renting_id'] == "3") echo "true"; else echo "false"; ?>
    }],
    "contract": [{
      "id": "1",
      "name": "12 Months",
      "price": "0",
      "checked": <?php if($_GET['contract_id'] == "" OR $_GET['contract_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "24 Months",
      "price": "250",
      "checked": <?php if($_GET['contract_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "36 Months",
      "price": "250",
      "checked": <?php if($_GET['contract_id'] == "3") echo "true"; else echo "false"; ?>
    }],
    "price": <?php if($_GET['rack_id'] == 2) echo "499"; else echo "1000"; ?>,
    "delivery": <?php if($_GET['rack_id'] == 2) echo "\"in 24 hours\""; else echo "\"in 4 weeks\""; ?>

  }]
<?php
}elseif($_GET['f'] == 'iptransitorder'){
?>[
  {
    "porttype": [{
      "id": "1",
      "size": "Flat Port",
      "price": "0",
      "checked": <?php if($_GET['porttype_id'] == "" OR $_GET['porttype_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "size": "Burstable Port",
      "price": "250",
      "checked": <?php if($_GET['porttype_id'] == "2") echo "true"; else echo "false"; ?>
    }]
    ,
    <?php if($_GET['porttype_id'] == '1' OR $_GET['porttype_id'] == ''){ ?>

    "portsize": [{
      "id": "1",
      "name": "10Gbps SFP+",
      "price": "0",
      "checked": <?php if($_GET['portsize_id'] == "" OR $_GET['portsize_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "100Gbps QSFP28 LR4",
      "price": "250",
      "checked": <?php if($_GET['portsize_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "2 x 100Gbps QSFP28 LR4",
      "price": "250",
      "checked": <?php if($_GET['portsize_id'] == "3") echo "true"; else echo "false"; ?>
    }]<?php }else{ ?>"portsize": [{
      "id": "1",
      "name": "100Gbps QSFP28 LR4",
      "price": "0",
      "checked": <?php if($_GET['portsize_id'] == "" OR $_GET['portsize_id'] == "1") echo "true"; else echo "false"; ?>
    }]<?php } ?>,
    "bandwidth": [{
      "id": "1",
      "name": "10Gbps",
      "price": "0",
      "checked": <?php if($_GET['bandwidth_id'] == "" OR $_GET['bandwidth_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "25Gbps",
      "price": "250",
      "checked": <?php if($_GET['bandwidth_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "50Gbps",
      "price": "250",
      "checked": <?php if($_GET['bandwidth_id'] == "3") echo "true"; else echo "false"; ?>
    }],
    "location": [{
      "id": "1",
      "name": "Bucharest",
      "stock": "in 24 hours",
      "stockcolor": "green",
      "flag": "ro",
      "price": "0",
      "checked": <?php if($_GET['location_id'] == "" OR $_GET['location_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "Amsterdam",
      "stock": "in 24 hours",
      "stockcolor": "green",
      "flag": "nl",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "Frankfurt",
      "stock": "in 24 hours",
      "stockcolor": "green",
      "flag": "de",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "3") echo "true"; else echo "false"; ?>
    },
    {
      "id": "4",
      "name": "London",
      "stock": "in 4 weeks",
      "stockcolor": "red",
      "flag": "gb",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "4") echo "true"; else echo "false"; ?>
    },
    {
      "id": "5",
      "name": "Marseille",
      "stock": "in 4 weeks",
      "stockcolor": "red",
      "flag": "fr",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "5") echo "true"; else echo "false"; ?>
    },
    {
      "id": "6",
      "name": "Vienna",
      "stock": "in 4 weeks",
      "stockcolor": "red",
      "flag": "at",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "6") echo "true"; else echo "false"; ?>
    },
    {
      "id": "7",
      "name": "Madrid",
      "stock": "in 4 weeks",
      "stockcolor": "red",
      "flag": "es",
      "price": "250",
      "checked": <?php if($_GET['location_id'] == "7") echo "true"; else echo "false"; ?>
    }],
    "subnet": [{
      "id": "1",
      "name": "No IPs (BGP Only)",
      "price": "0",
      "checked": <?php if($_GET['subnet_id'] == "" OR $_GET['subnet_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "/30 (1 usable IP)",
      "price": "0",
      "checked": <?php if($_GET['subnet_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "/29 (5 usable IPs)",
      "price": "250",
      "checked": <?php if($_GET['subnet_id'] == "3") echo "true"; else echo "false"; ?>
    },
    {
      "id": "4",
      "name": "/28 (13 usable IPs)",
      "price": "250",
      "checked": <?php if($_GET['subnet_id'] == "4") echo "true"; else echo "false"; ?>
    }],
    "contract": [{
      "id": "1",
      "name": "12 Months",
      "price": "0",
      "checked": <?php if($_GET['contract_id'] == "" OR $_GET['contract_id'] == "1") echo "true"; else echo "false"; ?>
    },
    {
      "id": "2",
      "name": "24 Months",
      "price": "250",
      "checked": <?php if($_GET['contract_id'] == "2") echo "true"; else echo "false"; ?>
    },
    {
      "id": "3",
      "name": "36 Months",
      "price": "250",
      "checked": <?php if($_GET['contract_id'] == "3") echo "true"; else echo "false"; ?>
    }],
    "price": <?php if($_GET['porttype_id'] == 2) echo "499"; else echo "1000"; ?>,
    "delivery": <?php if($_GET['porttype_id'] == 2) echo "\"in 24 hours\""; else echo "\"in 4 weeks\""; ?>

  }]
<?php
}elseif($_GET['f'] == 'bills'){
?>
[
  {
      "id": "1",
      "type": "Proforma",
      "total": "449",
      "date": "2023-01-09",
      "duedate": "2023-01-09",
      "status": "Unpaid"
    },
  {
      "id": "2",
      "type": "Proforma",
      "total": "249",
      "date": "2023-01-19",
      "duedate": "2023-01-19",
      "status": "Unpaid"
    }
]

<?php
}elseif($_GET['f'] == 'history'){
?>
[
    {
      "id": "2",
      "type": "Issued",
      "total": "599",
      "date": "2023-01-19",
      "method": "Credit Card",
      "status": "Paid"
    },
    {
      "id": "3",
      "type": "Issued",
      "total": "1492",
      "date": "2023-01-29",
      "method": "Cryptocurrency",
      "status": "Paid"
    }
]

<?php
}elseif($_GET['f'] == 'tickets'){
?>
[
    {
      "id": "29921",
      "subject": "My server not work",
      "department": "24/7 NOC",
      "last_reply": "05:34 05.06.2023",
      "status": "Open"
    },
    {
      "id": "29922",
      "subject": "My server is bullshit",
      "department": "24/7 NOC",
      "last_reply": "05:34 05.06.2023",
      "status": "Open"
    },
    {
      "id": "29923",
      "subject": "Ho ho ho",
      "department": "Sales",
      "last_reply": "05:39 05.06.2023",
      "status": "Open"
    }
]

<?php
}elseif($_GET['f'] == 'messages'){
?>
[
    {
      "id": "2",
      "type": "zet",
      "time": "10:12 am",
      "message": "Paid"
    },
    {
      "id": "3",
      "type": "customer",
      "time": "10:15 am",
      "message": "aaaa"
    }
,
    {
      "id": "4",
      "type": "zet",
      "time": "10:15 am",
      "message": "The ticket ID is: <?php echo $_GET['id']; ?>"
    }
,
    {
      "id": "5",
      "type": "customer",
      "time": "10:15 am",
      "message": "You just wrote: <?php echo $_GET['msg']; ?>"
    }

]

<?php
}elseif($_GET['f'] == 'cards'){
?>
[
    {
      "id": "1",
      "type": "Visa",
      "last_digits": "1599",
      "expiration": "2024-01-19",
      "status": "Active"
    },
    {
      "id": "2",
      "type": "Mastercard",
      "last_digits": "2763",
      "expiration": "2024-05-21",
      "status": "Active"
    }
]
<?php
}else{
	$reply['error'] = 1;
	die(json_encode($reply));
}
?>