<?php
require_once("auth_functions.php");




if($_GET['f'] == 'get_dashboard_tasks'){
  try {
    // Authenticate user
    $admin_id = auth_admin();
    
    // Get sorting parameters
    $sortField = isset($_GET['sortField']) ? $_GET['sortField'] : 'id';
    $sortDirection = isset($_GET['sortDirection']) ? $_GET['sortDirection'] : 'desc';
    
    // Get filter parameters
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $category = isset($_GET['category']) ? $_GET['category'] : '';
    $search = isset($_GET['search']) ? $_GET['search'] : '';
    
    // Prepare the base query
    $sql = "SELECT t.*, 
           d.department_name,
           a.id as assigned_admin_id,
           a.first_name as assigned_first_name,
           a.last_name as assigned_last_name
           FROM tasks t
           LEFT JOIN departments d ON t.department_id = d.id
           LEFT JOIN admins a ON t.admin_id = a.id
           WHERE 1=1";
    
    // Add filters
    if ($status && $status != 'All') {
      $sql .= " AND t.status = " . $pdo->quote($status);
    }
    
    if ($category && $category != 'All') {
      $sql .= " AND t.category = " . $pdo->quote($category);
    }
    
    if ($search) {
      $sql .= " AND (t.name LIKE " . $pdo->quote("%$search%") . 
              " OR t.content LIKE " . $pdo->quote("%$search%") . 
              " OR t.id LIKE " . $pdo->quote("%$search%") . 
              " OR d.department_name LIKE " . $pdo->quote("%$search%") . ")";
    }
    
    // Add sorting
    $allowedSortFields = ['id', 'name', 'status', 'priority', 'datetime'];
    if (in_array($sortField, $allowedSortFields)) {
      $sql .= " ORDER BY t.$sortField " . ($sortDirection === 'asc' ? 'ASC' : 'DESC');
    } else {
      $sql .= " ORDER BY t.datetime DESC";
    }
    
    // Execute query
    $sth = $pdo->prepare($sql);
    $sth->execute();
    
    // Fetch results
    $tasks = array();
    while($row = $sth->fetch(PDO::FETCH_ASSOC)){
      // Format data for frontend
      $tasks[] = array(
        'id' => '#' . $row['id'],
        'description' => $row['name'],
        'department' => $row['department_name'] ?? 'General',
        'department_id' => $row['department_id'],
        'category' => $row['category'],
        'status' => $row['status'],
        'priority' => $row['priority'],
        'createdDate' => $row['datetime'],
        'admin_id' => $row['assigned_admin_id'],
        'assignedTo' => trim($row['assigned_first_name'] . ' ' . $row['assigned_last_name']) ?: 'Unassigned',
        'content' => $row['content']
      );
    }
    
    // Return tasks as JSON
    header('Content-Type: application/json');
    echo json_encode($tasks);
    
  } catch (Exception $e) {
    error_log("Error in get_dashboard_tasks: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['error' => 'Failed to fetch tasks: ' . $e->getMessage()]);
  }
}

elseif($_GET['f'] == 'add_task'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();
    
    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);
    
    // Validate required fields
    if (!isset($data['description']) || trim($data['description']) === '') {
      throw new Exception("Task description is required");
    }
    
    // Prepare data
    $name = $data['description'];
    $content = $data['content'] ?? '';
    $category = $data['category'] ?? 'Network Configuration';
    $department_id = isset($data['department_id']) && !empty($data['department_id']) ? $data['department_id'] : null;
    
    // Get assigned admin ID
    $assigned_admin_id = isset($data['admin_id']) && !empty($data['admin_id']) ? $data['admin_id'] : null;
    
    // Set default priority and status
    $priority = $data['priority'] ?? 'Low';
    $status = $data['status'] ?? 'Pending Approval';
    
    // Insert task
    $sth = $pdo->prepare("
      INSERT INTO tasks 
      (name, content, category, admin_id, department_id, priority, datetime, status) 
      VALUES 
      (:name, :content, :category, :admin_id, :department_id, :priority, NOW(), :status)
    ");
    
    $sth->bindValue(':name', $name);
    $sth->bindValue(':content', $content);
    $sth->bindValue(':category', $category);
    $sth->bindValue(':admin_id', $assigned_admin_id);
    $sth->bindValue(':department_id', $department_id);
    $sth->bindValue(':priority', $priority);
    $sth->bindValue(':status', $status);
    
    $sth->execute();
    $task_id = $pdo->lastInsertId();
    
    // Add to activity log
    $sql = "INSERT INTO activity_log 
            (user_id, action, description, user_name, timestamp) 
            VALUES 
            (:user_id, :action, :description, :user_name, NOW())";

    $stmt = $pdo->prepare($sql);
    $stmt->bindValue(':user_id', $admin_id);
    $stmt->bindValue(':action', 'New Task Created');
    $stmt->bindValue(':description', $name);

    // Get admin name
    $admin_sth = $pdo->prepare("SELECT first_name, last_name FROM admins WHERE id = :admin_id");
    $admin_sth->bindValue(':admin_id', $admin_id);
    $admin_sth->execute();
    $admin = $admin_sth->fetch(PDO::FETCH_ASSOC);
    $admin_name = ($admin['first_name'] ?? '') . ' ' . ($admin['last_name'] ?? '');

    $stmt->bindValue(':user_name', $admin_name);
    $stmt->execute();
    
    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'task_id' => $task_id,
      'message' => 'Task created successfully'
    ]);
    
  } catch (Exception $e) {
    error_log("Error in add_task: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['error' => 'Failed to create task: ' . $e->getMessage()]);
  }
}

// UPDATE TASK - Updated to use IDs
elseif($_GET['f'] == 'update_task'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();
    
    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);
    
    // Validate required fields
    if (!isset($data['id']) || !is_numeric(str_replace('#', '', $data['id']))) {
      throw new Exception("Valid task ID is required");
    }
    
    // Get original task ID (remove # if present)
    $task_id = (int)str_replace('#', '', $data['id']);
    
    // Check if task exists
    $check_sth = $pdo->prepare("SELECT * FROM tasks WHERE id = :id");
    $check_sth->bindValue(':id', $task_id);
    $check_sth->execute();
    
    if ($check_sth->rowCount() == 0) {
      throw new Exception("Task not found");
    }
    
    // Prepare update fields
    $updates = [];
    $params = [];
    
    // Only update provided fields
    if (isset($data['description']) && !empty($data['description'])) {
      $updates[] = "name = :name";
      $params[':name'] = $data['description'];
    }
    
    if (isset($data['content'])) {
      $updates[] = "content = :content";
      $params[':content'] = $data['content'];
    }
    
    if (isset($data['category'])) {
      $updates[] = "category = :category";
      $params[':category'] = $data['category'];
    }
    
    // Use department_id directly
    if (isset($data['department_id'])) {
      $updates[] = "department_id = :department_id";
      $params[':department_id'] = $data['department_id'];
    }
    
    // Use admin_id directly
    if (isset($data['admin_id'])) {
      $updates[] = "admin_id = :admin_id";
      $params[':admin_id'] = $data['admin_id'];
    }
    
    if (isset($data['priority'])) {
      $updates[] = "priority = :priority";
      $params[':priority'] = $data['priority'];
    }
    
    if (isset($data['status'])) {
      $updates[] = "status = :status";
      $params[':status'] = $data['status'];
    }
    
    // Only proceed if there are fields to update
    if (empty($updates)) {
      throw new Exception("No fields to update");
    }
    
    // Build update query
    $sql = "UPDATE tasks SET " . implode(", ", $updates) . " WHERE id = :id";
    $params[':id'] = $task_id;
    
    // Execute update
    $sth = $pdo->prepare($sql);
    
    foreach ($params as $key => $value) {
      $sth->bindValue($key, $value);
    }
    
    $sth->execute();
    
    // Add to activity log
    $sql = "INSERT INTO activity_log 
            (user_id, action, description, user_name, timestamp) 
            VALUES 
            (:user_id, :action, :description, :user_name, NOW())";

    $stmt = $pdo->prepare($sql);
    $stmt->bindValue(':user_id', $admin_id);
    $stmt->bindValue(':action', 'Task Updated');
    $stmt->bindValue(':description', "Task #$task_id updated");

    // Get admin name
    $admin_sth = $pdo->prepare("SELECT first_name, last_name FROM admins WHERE id = :admin_id");
    $admin_sth->bindValue(':admin_id', $admin_id);
    $admin_sth->execute();
    $admin = $admin_sth->fetch(PDO::FETCH_ASSOC);
    $admin_name = ($admin['first_name'] ?? '') . ' ' . ($admin['last_name'] ?? '');

    $stmt->bindValue(':user_name', $admin_name);
    $stmt->execute();
    
    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Task updated successfully'
    ]);
    
  } catch (Exception $e) {
    error_log("Error in update_task: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['error' => 'Failed to update task: ' . $e->getMessage()]);
  }
}
  

  // DELETE TASK
  elseif($_GET['f'] == 'delete_task'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();
      
      // Get request data
      $data = json_decode(file_get_contents('php://input'), true);
      
      // Validate required fields
      if (!isset($data['id']) || !is_numeric(str_replace('#', '', $data['id']))) {
        throw new Exception("Valid task ID is required");
      }
      
      // Get original task ID (remove # if present)
      $task_id = (int)str_replace('#', '', $data['id']);
      
      // Check if task exists
      $check_sth = $pdo->prepare("SELECT name FROM tasks WHERE id = :id");
      $check_sth->bindValue(':id', $task_id);
      $check_sth->execute();
      
      if ($check_sth->rowCount() == 0) {
        throw new Exception("Task not found");
      }
      
      $task = $check_sth->fetch(PDO::FETCH_ASSOC);
      $task_name = $task['name'];
      
      // Delete task
      $sth = $pdo->prepare("DELETE FROM tasks WHERE id = :id");
      $sth->bindValue(':id', $task_id);
      $sth->execute();
      
      // Add to activity log
      $sql = "INSERT INTO activity_log 
              (user_id, action, description, user_name, timestamp) 
              VALUES 
              (:user_id, :action, :description, :user_name, NOW())";
  
      $stmt = $pdo->prepare($sql);
      $stmt->bindValue(':user_id', $admin_id);
      $stmt->bindValue(':action', 'Task Deleted');
      $stmt->bindValue(':description', "Deleted: $task_name (#$task_id)");
  
      // Get admin name
      $admin_sth = $pdo->prepare("SELECT first_name, last_name FROM admins WHERE id = :admin_id");
      $admin_sth->bindValue(':admin_id', $admin_id);
      $admin_sth->execute();
      $admin = $admin_sth->fetch(PDO::FETCH_ASSOC);
      $admin_name = ($admin['first_name'] ?? '') . ' ' . ($admin['last_name'] ?? '');
  
      $stmt->bindValue(':user_name', $admin_name);
      $stmt->execute();
      
      // Return success response
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true,
        'message' => 'Task deleted successfully'
      ]);
      
    } catch (Exception $e) {
      error_log("Error in delete_task: " . $e->getMessage());
      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode(['error' => 'Failed to delete task: ' . $e->getMessage()]);
    }
  }
  
  // GET RECENT ORDERS
  elseif($_GET['f'] == 'get_recent_orders'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();
      
      // Get the most recent orders
      $sql = "SELECT 
              o.id, 
              o.label, 
              o.order_type, 
              o.status, 
              o.recurring_price as amount,
              o.order_date as date,
              u.first_name, 
              u.last_name,
              u.company_name as customerName
              FROM orders o
              LEFT JOIN users u ON o.owner_id = u.id
              ORDER BY o.order_date DESC
              LIMIT 5";
      
      $sth = $pdo->prepare($sql);
      $sth->execute();
      
      $orders = array();
      while($row = $sth->fetch(PDO::FETCH_ASSOC)){
        $orders[] = array(
          'id' => $row['id'],
          'label' => $row['label'],
          'customerName' => !empty($row['company_name']) ? $row['company_name'] : trim($row['first_name'] . ' ' . $row['last_name']),
          'amount' => '€' . number_format($row['amount'], 2),
          'date' => $row['date'],
          'status' => $row['status'],
          'type' => $row['order_type']
        );
      }
      
      // Return orders as JSON
      header('Content-Type: application/json');
      echo json_encode($orders);
      
    } catch (Exception $e) {
      error_log("Error in get_recent_orders: " . $e->getMessage());
      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode(['error' => 'Failed to fetch recent orders: ' . $e->getMessage()]);
    }
  }
  
  // Create an activity_log table if it doesn't exist
  // This would normally be part of the database schema, but adding here for completeness
  elseif($_GET['f'] == 'init_activity_log'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();
      
      // Create activity_log table if it doesn't exist
      $sql = "CREATE TABLE IF NOT EXISTS `activity_log` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `action` varchar(64) NOT NULL,
        `description` text NOT NULL,
        `user_name` varchar(128) NOT NULL,
        `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
      
      $pdo->exec($sql);
      
      echo json_encode(['success' => true, 'message' => 'Activity log table initialized']);
      
    } catch (Exception $e) {
      echo json_encode(['error' => 'Failed to initialize activity log: ' . $e->getMessage()]);
    }
  }
  
  // GET RECENT ACTIVITY
  elseif($_GET['f'] == 'get_recent_activity'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();
      
      // Check if activity_log table exists, create it if not
      $tableCheck = $pdo->query("SHOW TABLES LIKE 'activity_log'");
      if ($tableCheck->rowCount() == 0) {
        // Create activity_log table
        $sql = "CREATE TABLE IF NOT EXISTS `activity_log` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `user_id` int(11) NOT NULL,
          `action` varchar(64) NOT NULL,
          `description` text NOT NULL,
          `user_name` varchar(128) NOT NULL,
          `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
        
        $pdo->exec($sql);
        
        // Add sample initial activity if no activities exist
        $initialActivity = $pdo->prepare("INSERT INTO activity_log 
                                         (user_id, action, description, user_name, timestamp) 
                                         VALUES 
                                         (:user_id, 'System Initialized', 'Activity tracking started', 'System', NOW())");
        $initialActivity->bindValue(':user_id', $admin_id);
        $initialActivity->execute();
      }
      
      // Get filter parameter (today, yesterday, this week, etc.)
      $timeFilter = isset($_GET['timeFilter']) ? $_GET['timeFilter'] : 'today';
      
      // Build time filter condition
      $timeCondition = "1=1"; // Default: all time
      switch($timeFilter) {
        case 'today':
          $timeCondition = "DATE(timestamp) = CURDATE()";
          break;
        case 'yesterday':
          $timeCondition = "DATE(timestamp) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)";
          break;
        case 'thisWeek':
          $timeCondition = "YEARWEEK(timestamp) = YEARWEEK(NOW())";
          break;
        case 'thisMonth':
          $timeCondition = "MONTH(timestamp) = MONTH(NOW()) AND YEAR(timestamp) = YEAR(NOW())";
          break;
      }
      
      // Get recent activity
      $sql = "SELECT * FROM activity_log 
              WHERE $timeCondition
              ORDER BY timestamp DESC
              LIMIT 10";
      
      $sth = $pdo->prepare($sql);
      $sth->execute();
      
      $activities = array();
      while($row = $sth->fetch(PDO::FETCH_ASSOC)){
        $activities[] = array(
          'id' => $row['id'],
          'action' => $row['action'],
          'description' => $row['description'],
          'user' => $row['user_name'],
          'timestamp' => date('H:i', strtotime($row['timestamp']))
        );
      }
      
      // Return activities as JSON
      header('Content-Type: application/json');
      echo json_encode($activities);
      
    } catch (Exception $e) {
      error_log("Error in get_recent_activity: " . $e->getMessage());
      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode(['error' => 'Failed to fetch recent activity: ' . $e->getMessage()]);
    }
  }
  
  // GET TASK STATS
  elseif($_GET['f'] == 'get_task_stats'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();
      
      // Get counts of tasks by status
      $sql = "SELECT 
              COUNT(*) as total,
              SUM(CASE WHEN status = 'In Progress' OR status = 'Open' THEN 1 ELSE 0 END) as inProgress,
              SUM(CASE WHEN status = 'Pending Approval' THEN 1 ELSE 0 END) as pending,
              SUM(CASE WHEN status = 'Completed' OR status = 'Closed' OR status = 'Resolved' THEN 1 ELSE 0 END) as completed
              FROM tasks";
      
      $sth = $pdo->prepare($sql);
      $sth->execute();
      $stats = $sth->fetch(PDO::FETCH_ASSOC);
      
      // Convert null values to 0
      $stats['total'] = $stats['total'] ?? 0;
      $stats['inProgress'] = $stats['inProgress'] ?? 0;
      $stats['pending'] = $stats['pending'] ?? 0;
      $stats['completed'] = $stats['completed'] ?? 0;
      
      // Return stats as JSON
      header('Content-Type: application/json');
      echo json_encode($stats);
      
    } catch (Exception $e) {
      error_log("Error in get_task_stats: " . $e->getMessage());
      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode(['error' => 'Failed to fetch task stats: ' . $e->getMessage()]);
    }
  }
  
  
  
  
  elseif($_GET['f'] == 'get_task_messages'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();
      
      // Get request data
      $data = json_decode(file_get_contents('php://input'), true);
      
      // Validate required fields
      if (!isset($data['task_id']) || empty($data['task_id'])) {
        throw new Exception("Task ID is required");
      }
      
      // Remove # from task_id if present
      $task_id = (int)str_replace('#', '', $data['task_id']);
      
      // Create task_messages table if it doesn't exist
      $sql = "CREATE TABLE IF NOT EXISTS `task_messages` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `task_id` int(11) NOT NULL,
        `admin_id` int(11) NOT NULL,
        `message` text NOT NULL,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `task_id` (`task_id`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
      
      $pdo->exec($sql);
      
      // Get messages for the task
      $sth = $pdo->prepare("
        SELECT m.*, a.first_name, a.last_name
        FROM task_messages m
        LEFT JOIN admins a ON m.admin_id = a.id
        WHERE m.task_id = :task_id
        ORDER BY m.created_at ASC
      ");
      
      $sth->bindValue(':task_id', $task_id);
      $sth->execute();
      
      $messages = [];
      while ($row = $sth->fetch(PDO::FETCH_ASSOC)) {
        $messages[] = [
          'id' => $row['id'],
          'message' => $row['message'],
          'admin_name' => trim($row['first_name'] . ' ' . $row['last_name']),
          'created_at' => $row['created_at'],
          'time' => date('H:i', strtotime($row['created_at'])),
          'date' => date('d M Y', strtotime($row['created_at']))
        ];
      }
      
      // Return messages as JSON
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true,
        'messages' => $messages
      ]);
      
    } catch (Exception $e) {
      error_log("Error in get_task_messages: " . $e->getMessage());
      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode(['error' => 'Failed to fetch task messages: ' . $e->getMessage()]);
    }
  }
  
  elseif($_GET['f'] == 'add_task_message'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();
      
      // Get request data
      $data = json_decode(file_get_contents('php://input'), true);
      
      // Validate required fields
      if (!isset($data['task_id']) || empty($data['task_id'])) {
        throw new Exception("Task ID is required");
      }
      
      if (!isset($data['message']) || trim($data['message']) === '') {
        throw new Exception("Message is required");
      }
      
      // Remove # from task_id if present
      $task_id = (int)str_replace('#', '', $data['task_id']);
      $message = trim($data['message']);
      
      // Insert the message
      $sth = $pdo->prepare("
        INSERT INTO task_messages 
        (task_id, admin_id, message)
        VALUES
        (:task_id, :admin_id, :message)
      ");
      
      $sth->bindValue(':task_id', $task_id);
      $sth->bindValue(':admin_id', $admin_id);
      $sth->bindValue(':message', $message);
      $sth->execute();
      
      $message_id = $pdo->lastInsertId();
      
      // Get the admin name for the response
      $admin_sth = $pdo->prepare("SELECT first_name, last_name FROM admins WHERE id = :admin_id");
      $admin_sth->bindValue(':admin_id', $admin_id);
      $admin_sth->execute();
      $admin = $admin_sth->fetch(PDO::FETCH_ASSOC);
      $admin_name = trim($admin['first_name'] . ' ' . $admin['last_name']);
      
      // Log activity
      $sql = "INSERT INTO activity_log 
              (user_id, action, description, user_name, timestamp) 
              VALUES 
              (:user_id, :action, :description, :user_name, NOW())";
  
      $stmt = $pdo->prepare($sql);
      $stmt->bindValue(':user_id', $admin_id);
      $stmt->bindValue(':action', 'Task Message Added');
      $stmt->bindValue(':description', "Added message to task #$task_id");
      $stmt->bindValue(':user_name', $admin_name);
      $stmt->execute();
      
      // Return success response
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true,
        'message_id' => $message_id,
        'admin_name' => $admin_name,
        'created_at' => date('Y-m-d H:i:s'),
        'time' => date('H:i'),
        'date' => date('d M Y')
      ]);
      
    } catch (Exception $e) {
      error_log("Error in add_task_message: " . $e->getMessage());
      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode(['error' => 'Failed to add task message: ' . $e->getMessage()]);
    }
  }



// GET STAFF MEMBERS - Database-driven with fallback
elseif($_GET['f'] == 'get_staff_members'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();
    
    // Query the actual admins table
    $sql = "SELECT id, first_name, last_name FROM admins ORDER BY first_name, last_name";
    $sth = $pdo->prepare($sql);
    $sth->execute();
    
    if($sth->rowCount() > 0) {
      $staff = array();
      while($row = $sth->fetch(PDO::FETCH_ASSOC)){
        $staff[] = array(
          'id' => $row['id'],
          'name' => trim($row['first_name'] . ' ' . $row['last_name']),
          'first_name' => $row['first_name']
        );
      }
      
      // Return staff as JSON
      header('Content-Type: application/json');
      echo json_encode($staff);
    } else {
      // No data found, return hardcoded fallback
      throw new Exception("No staff found in database");
    }
    
  } catch (Exception $e) {
    error_log("Error in get_staff_members: " . $e->getMessage());
    
    // Return hardcoded fallback only if database query fails
    // This ensures we always return something usable
    header('Content-Type: application/json');
    echo json_encode([
      array('id' => 1, 'name' => 'Alexandru', 'first_name' => 'Alexandru'),
      array('id' => 2, 'name' => 'Mario', 'first_name' => 'Mario'),
      array('id' => 3, 'name' => 'Raul', 'first_name' => 'Raul'),
      array('id' => 4, 'name' => 'Sorin', 'first_name' => 'Sorin'),
      array('id' => 5, 'name' => 'Mihai', 'first_name' => 'Mihai'),
      array('id' => 6, 'name' => 'Andrei', 'first_name' => 'Andrei'),
      array('id' => 7, 'name' => 'Michael', 'first_name' => 'Michael')
    ]);
  }
}

// GET DEPARTMENTS - Database-driven with fallback
elseif($_GET['f'] == 'get_departments'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();
    
    // Query the actual departments table
    $sql = "SELECT id, department_name FROM departments ORDER BY department_name";
    $sth = $pdo->prepare($sql);
    $sth->execute();
    
    if($sth->rowCount() > 0) {
      $departments = array();
      while($row = $sth->fetch(PDO::FETCH_ASSOC)){
        $departments[] = array(
          'id' => $row['id'],
          'name' => $row['department_name']
        );
      }
      
      // Return departments as JSON
      header('Content-Type: application/json');
      echo json_encode($departments);
    } else {
      // No data found, return hardcoded fallback
      throw new Exception("No departments found in database");
    }
    
  } catch (Exception $e) {
    error_log("Error in get_departments: " . $e->getMessage());
    
   
  }
}





?>