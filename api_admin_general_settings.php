<?php
require_once("auth_functions.php");
require_once("mysql.php");

// Get all general settings
if ($_GET['f'] == 'get_general_settings') {
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Ensure the table exists
    createGeneralSettingsTable();

    // Get settings from database
    $stmt = $pdo->query("SELECT * FROM general_settings ORDER BY setting_key");
    $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Return settings as JSON
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'settings' => $settings
    ]);

  } catch (Exception $e) {
    error_log("Error in get_general_settings: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to fetch general settings: ' . $e->getMessage()
    ]);
  }
}

// Save general settings
if ($_GET['f'] == 'save_general_settings') {
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get request body
    $requestBody = json_decode(file_get_contents('php://input'), true);
    $settings = $requestBody['settings'] ?? null;

    if (!$settings || !is_array($settings)) {
      throw new Exception('No settings provided or invalid format');
    }

    // Ensure the table exists
    createGeneralSettingsTable();

    // Begin transaction
    $pdo->beginTransaction();

    // Prepare statements for update and insert
    $updateStmt = $pdo->prepare("
      UPDATE general_settings 
      SET setting_value = :value 
      WHERE setting_key = :key
    ");

    $insertStmt = $pdo->prepare("
      INSERT INTO general_settings (setting_key, setting_value, description)
      VALUES (:key, :value, :description)
    ");

    // Process each setting
    foreach ($settings as $setting) {
      if (!isset($setting['setting_key']) || !isset($setting['setting_value'])) {
        continue; // Skip invalid entries
      }

      // Check if setting exists
      $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM general_settings WHERE setting_key = :key");
      $checkStmt->bindValue(':key', $setting['setting_key']);
      $checkStmt->execute();
      $exists = $checkStmt->fetchColumn() > 0;

      if ($exists) {
        // Update existing setting
        $updateStmt->bindValue(':key', $setting['setting_key']);
        $updateStmt->bindValue(':value', $setting['setting_value']);
        $updateStmt->execute();
      } else {
        // Insert new setting
        $insertStmt->bindValue(':key', $setting['setting_key']);
        $insertStmt->bindValue(':value', $setting['setting_value']);
        $insertStmt->bindValue(':description', $setting['description'] ?? '');
        $insertStmt->execute();
      }
    }

    // Commit transaction
    $pdo->commit();

    // Log the success
    error_log("General settings saved successfully by admin ID: $admin_id");

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'General settings saved successfully'
    ]);

  } catch (Exception $e) {
    // Rollback transaction if active
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
    }

    error_log("Error in save_general_settings: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to save general settings: ' . $e->getMessage()
    ]);
  }
}

// Helper function to create the general_settings table if it doesn't exist
function createGeneralSettingsTable() {
  global $pdo;

  try {
    // Check if the table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'general_settings'");
    if ($stmt->rowCount() == 0) {
      // Create the general_settings table
      $pdo->exec("CREATE TABLE `general_settings` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `setting_key` varchar(255) NOT NULL,
        `setting_value` text NOT NULL,
        `description` text,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `setting_key` (`setting_key`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");

      // Insert default settings
      $settings = [
        [
          'setting_key' => 'company_name',
          'setting_value' => 'Your Company Name',
          'description' => 'The name of your company'
        ],
        [
          'setting_key' => 'company_website',
          'setting_value' => 'https://example.com',
          'description' => 'Your company website URL'
        ],
        [
          'setting_key' => 'client_portal_url',
          'setting_value' => 'https://example.com/client',
          'description' => 'The URL to your client portal'
        ],
        [
          'setting_key' => 'invoice_page_url',
          'setting_value' => 'https://example.com/client/invoices',
          'description' => 'The URL to the invoices page in your client portal'
        ]
      ];

      $insertStmt = $pdo->prepare("
        INSERT INTO general_settings (setting_key, setting_value, description)
        VALUES (:key, :value, :description)
      ");

      foreach ($settings as $setting) {
        $insertStmt->bindValue(':key', $setting['setting_key']);
        $insertStmt->bindValue(':value', $setting['setting_value']);
        $insertStmt->bindValue(':description', $setting['description']);
        $insertStmt->execute();
      }
    }
  } catch (Exception $e) {
    error_log("Error creating general_settings table: " . $e->getMessage());
    throw $e;
  }
}
?>
