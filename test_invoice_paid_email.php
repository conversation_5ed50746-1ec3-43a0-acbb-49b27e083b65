<?php
/**
 * Test script for sending invoice paid emails
 *
 * Usage: php test_invoice_paid_email.php [invoice_id]
 */

// Include necessary files
require_once("mysql.php");
require_once("invoice_email_functions.php");

// Disable headers for CLI mode
if (!function_exists('getallheaders')) {
    function getallheaders() {
        return [];
    }
}

// Get invoice ID from command line argument or use a default
$invoice_id = isset($argv[1]) ? $argv[1] : null;

if (!$invoice_id) {
    echo "Usage: php test_invoice_paid_email.php [invoice_id]\n";
    echo "Please provide an invoice ID to test.\n";

    // Try to find a valid invoice ID
    $findInvoice = $pdo->query("SELECT id FROM invoices ORDER BY id DESC LIMIT 1");
    if ($findInvoice && $invoice = $findInvoice->fetch(PDO::FETCH_ASSOC)) {
        echo "Suggestion: Try using invoice ID: " . $invoice['id'] . "\n";
    }

    exit(1);
}

echo "Testing invoice paid email for invoice #$invoice_id\n";

// Create email_templates table and insert the invoice_paid template if it doesn't exist
try {
    echo "Checking for invoice_paid email template...\n";

    // Check if email_templates table exists
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'email_templates'");
    if ($tableCheck->rowCount() == 0) {
        echo "Email templates table does not exist. Creating it...\n";

        // Create the email_templates table
        $pdo->exec("CREATE TABLE `email_templates` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `type` enum('password_reset', 'server_details', 'invoice_generated', 'invoice_paid') NOT NULL,
            `subject` varchar(255) NOT NULL,
            `content` text NOT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `type` (`type`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");

        echo "Email templates table created successfully.\n";
    }

    // Check if invoice_paid template exists
    $templateCheck = $pdo->prepare("SELECT COUNT(*) FROM email_templates WHERE type = 'invoice_paid'");
    $templateCheck->execute();
    $templateExists = $templateCheck->fetchColumn() > 0;

    if (!$templateExists) {
        echo "Invoice paid template does not exist. Creating it...\n";

        // Insert the invoice_paid template
        $stmt = $pdo->prepare("INSERT INTO email_templates (type, subject, content) VALUES (:type, :subject, :content)");
        $stmt->bindValue(':type', 'invoice_paid');
        $stmt->bindValue(':subject', 'Payment Confirmation - Invoice #{$invoice_num}');
        $stmt->bindValue(':content', '<p>Dear {$client_name},</p>
<p>We have received your payment for invoice #{$invoice_num}. Thank you!</p>
<p><strong>Invoice Number:</strong> {$invoice_num}<br>
<strong>Amount Paid:</strong> {$invoice_total}<br>
<strong>Payment Date:</strong> {$payment_date}</p>
<p><strong>Invoice Items</strong></p>
<div>{$invoice_html_contents}</div>
<hr>
<p>If you have any questions about your payment, please contact our support team.</p>');
        $stmt->execute();

        echo "Invoice paid template created successfully.\n";
    } else {
        echo "Invoice paid template already exists.\n";
    }
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}

// Check if the invoice exists
$checkStmt = $pdo->prepare("SELECT i.*, u.email FROM invoices i JOIN users u ON i.user_id = u.id WHERE i.id = :id");
$checkStmt->bindValue(':id', $invoice_id);
$checkStmt->execute();
$invoice = $checkStmt->fetch(PDO::FETCH_ASSOC);

if (!$invoice) {
    echo "Invoice #$invoice_id not found.\n";
    exit(1);
}

echo "Found invoice #$invoice_id for user: " . $invoice['email'] . "\n";

// Check if the invoice is already marked as paid
if ($invoice['paid'] == 0) {
    echo "Note: This invoice is not marked as paid. Marking it as paid for testing...\n";

    // Mark the invoice as paid for testing
    $updateStmt = $pdo->prepare("UPDATE invoices SET paid = 1, paid_date = NOW() WHERE id = :id");
    $updateStmt->bindValue(':id', $invoice_id);
    $updateStmt->execute();

    echo "Invoice marked as paid.\n";
}

// Send the invoice paid email
echo "Sending invoice paid email...\n";
$result = sendInvoicePaidEmail($invoice_id);

if ($result) {
    echo "Success! Invoice paid email sent successfully.\n";
} else {
    echo "Error: Failed to send invoice paid email. Check the error logs for details.\n";
}
?>
