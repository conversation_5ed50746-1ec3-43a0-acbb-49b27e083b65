<?php
// Save this as idrac-diagnostic.php on your server

// Enable detailed error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set header to plain text for easy reading
header('Content-Type: text/plain');

// Check server environment
echo "=== SERVER ENVIRONMENT ===\n";
echo "PHP Version: " . phpversion() . "\n";
echo "Operating System: " . php_uname() . "\n";

// Check for required extensions
echo "\n=== PHP EXTENSIONS ===\n";
$required_extensions = ['ssh2', 'curl', 'json'];
foreach ($required_extensions as $ext) {
    echo "$ext: " . (extension_loaded($ext) ? "Loaded" : "Not loaded") . "\n";
}

// Check if sshpass is installed
echo "\n=== EXTERNAL DEPENDENCIES ===\n";
exec('which sshpass', $output, $return_var);
echo "sshpass: " . ($return_var === 0 ? "Installed at " . $output[0] : "Not installed or not in path") . "\n";

exec('which ssh', $output_ssh, $return_var_ssh);
echo "ssh: " . ($return_var_ssh === 0 ? "Installed at " . $output_ssh[0] : "Not installed or not in path") . "\n";

// Check permissions
echo "\n=== PERMISSIONS ===\n";
echo "Can create temp files: " . (is_writable(sys_get_temp_dir()) ? "Yes" : "No") . "\n";
echo "Can execute commands: ";
try {
    exec('echo "Test command execution"', $output_test, $return_var_test);
    echo $return_var_test === 0 ? "Yes\n" : "No\n";
} catch (Exception $e) {
    echo "No - " . $e->getMessage() . "\n";
}

// Check SSH connectivity to test iDRAC (user must provide details)
echo "\n=== SSH CONNECTIVITY TEST ===\n";
echo "To test connectivity to iDRAC, please fill in the parameters in the URL:\n";
echo "idrac-diagnostic.php?ip=IDRAC_IP&user=root&pass=PASSWORD\n\n";

if (isset($_GET['ip']) && isset($_GET['user']) && isset($_GET['pass'])) {
    $ip = $_GET['ip'];
    $user = $_GET['user'];
    $pass = $_GET['pass'];
    
    echo "Testing connection to iDRAC at $ip...\n";
    
    // Method 1: Using SSH module if available
    if (extension_loaded('ssh2')) {
        echo "Trying SSH2 extension...\n";
        try {
            $connection = @ssh2_connect($ip, 22, array(), array('debug' => true));
            if ($connection) {
                echo "SSH connection established!\n";
                $auth = @ssh2_auth_password($connection, $user, $pass);
                echo "Authentication: " . ($auth ? "Success" : "Failed") . "\n";
                
                if ($auth) {
                    $stream = ssh2_exec($connection, 'racadm version');
                    stream_set_blocking($stream, true);
                    $output = stream_get_contents($stream);
                    echo "Command output:\n$output\n";
                }
            } else {
                echo "SSH connection failed\n";
            }
        } catch (Exception $e) {
            echo "SSH2 error: " . $e->getMessage() . "\n";
        }
    } else {
        echo "SSH2 extension not available\n";
    }
    
    // Method 2: Using sshpass with exec
    echo "\nTrying sshpass method...\n";
    try {
        // Create temporary password file
        $temp_file = tempnam(sys_get_temp_dir(), 'ssh_');
        file_put_contents($temp_file, $pass);
        chmod($temp_file, 0600);
        
        // Execute simple command with timeout
        $cmd = "sshpass -f " . escapeshellarg($temp_file) . 
               " ssh -o StrictHostKeyChecking=no -o ConnectTimeout=5 " .
               escapeshellarg($user . '@' . $ip) . " 'racadm version'";
               
        echo "Executing: " . preg_replace('/sshpass -f .*? ssh/', 'sshpass -f [PASSWORD_FILE] ssh', $cmd) . "\n";
        
        exec($cmd, $output_cmd, $return_var_cmd);
        echo "Return code: $return_var_cmd\n";
        echo "Output:\n" . implode("\n", $output_cmd) . "\n";
        
        // Clean up
        @unlink($temp_file);
        
    } catch (Exception $e) {
        echo "Exec error: " . $e->getMessage() . "\n";
        @unlink($temp_file);
    }
    
    // Method 3: Try a simpler connectivity test
    echo "\nTrying simple connection test...\n";
    $connection = @fsockopen($ip, 22, $errno, $errstr, 5);
    echo "TCP connection to port 22: " . ($connection ? "Success" : "Failed") . "\n";
    if ($connection) {
        fclose($connection);
    } else {
        echo "Error ($errno): $errstr\n";
    }
} else {
    echo "No test parameters provided.\n";
}

echo "\n=== RECOMMENDED NEXT STEPS ===\n";
echo "1. Ensure sshpass is installed on the server\n";
echo "2. Check network connectivity from web server to iDRAC\n";
echo "3. Verify iDRAC credentials\n";
echo "4. Check firewall settings\n";
echo "5. Try using alternative methods if SSH is not available\n";
?>