import React, { useState, useEffect } from 'react';
import {
  X as XIcon,
  User,
  Server,
  Package,
  Cpu,
  Map,
  Network,
  HardDrive,
  Globe,
  DollarSign,
  Activity,
  Plus,
  XCircle,
  RefreshCw,
  Trash2,
  Edit2,
  List,
  ChevronDown,
  ChevronUp,
  Wifi
} from 'lucide-react';
import { API_URL } from '../config';
const MultiDeviceOrderModal = ({ isOpen, onClose, onSave, searchResults, isSearching, onClientSearch }) => {
  // State for active tab
  const [activeTab, setActiveTab] = useState('dedicated');
  const [showAddOsModal, setShowAddOsModal] = useState(false);
  const [newOsData, setNewOsData] = useState({
    os_name: '',
    os_template: '',
    logo_url: ''
  });

  // State for price breakdown expansion (collapsed by default)
  const [isPriceBreakdownExpanded, setIsPriceBreakdownExpanded] = useState(false);

  // State for device list and multi-order mode
  const [deviceList, setDeviceList] = useState([]);
  const [editingDeviceIndex, setEditingDeviceIndex] = useState(null);
  const [totalPrice, setTotalPrice] = useState({ initial: 0, recurring: 0 });
  const [multiItemMode, setMultiItemMode] = useState(false);

  // State for form data
  const [formData, setFormData] = useState({
    customerName: '',
    customer_id: '',
    clientEmail: '',
    orderNote: 'New Server Order',
    initialPrice: '0.00',
    recurringPrice: '0.00',
    orderType: 'Dedicated Server',

    // Dedicated server specific fields
    cpuModel: '',
    bandwidth: '',
    location: '',
    storage: '',
    subnetSize: '',
    operatingSystem: '',

    // VPS specific fields
    vpsConfiguration: '',
    vpsIpPrice: '',
    vpsLocation: '',
    vpsApplication: '',

    // IP Transit specific fields
    transitConfiguration: '',
    transitLocation: '',
    transitAdditionalIps: '',

    // Colocation specific fields
    rackUnits: '1', // Can be '1' through '9', 'half', or 'full'
    kw: '1',
    colocationBandwidth: '',
    colocationAdditionalIps: '',
    colocationLocation: '',

    // Other configuration data
    serverConfig: {}
  });

  // State for price override
  const [priceOverride, setPriceOverride] = useState(false);

  // State for server configuration options
  const [serverOptions, setServerOptions] = useState({
    cpus: [],
    cities: [],
    bandwidths: [],
    storages: [],
    subnets: [],
    operating_systems: [],
    configurations: []
  });

  // State for VPS configuration options
  const [vpsOptions, setVpsOptions] = useState({
    configurations: [],
    ipPrices: [],
    applications: [],
    cities: []
  });

  // State for IP Transit configuration options
  const [transitOptions, setTransitOptions] = useState({
    configurations: [],
    cities: [],
    additionalIps: []
  });

  // State for Colocation configuration options
  const [colocationOptions, setColocationOptions] = useState({
    rackUnitOptions: [
      { id: '1', label: '1U', price: '50.00' },
      { id: '2', label: '2U', price: '100.00' },
      { id: '3', label: '3U', price: '150.00' },
      { id: '4', label: '4U', price: '200.00' },
      { id: '5', label: '5U', price: '250.00' },
      { id: '6', label: '6U', price: '300.00' },
      { id: '7', label: '7U', price: '350.00' },
      { id: '8', label: '8U', price: '400.00' },
      { id: '9', label: '9U', price: '450.00' },
      { id: 'half', label: 'Half Rack', price: '1000.00' },
      { id: 'full', label: 'Full Rack', price: '2000.00' }
    ],
    bandwidthOptions: [],
    additionalIps: [],
    cities: []
  });

  // State for filtered options based on selected CPU
  const [filteredOptions, setFilteredOptions] = useState({
    cities: [],
    bandwidths: [],
    storages: [],
    subnets: []
  });

  // State for search & validation
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [customerSelected, setCustomerSelected] = useState(false);

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      resetForm();
      fetchServerOptions();
      fetchVpsOptions();
      fetchTransitOptions();
      fetchColocationOptions();
      setDeviceList([]); // Clear device list
      setTotalPrice({ initial: 0, recurring: 0 });
      setMultiItemMode(false); // Reset to single item mode
      setIsPriceBreakdownExpanded(false); // Make sure price breakdown is collapsed by default
    }
  }, [isOpen]);

  // Handle client search
  useEffect(() => {
    // Only search if query is at least 2 characters and customer hasn't been selected
    if (searchQuery.length >= 2 && !customerSelected) {
      // Use a debounce mechanism to prevent too many API calls
      const handler = setTimeout(() => {
        onClientSearch(searchQuery);
        setShowSearchResults(true);
      }, 300); // 300ms debounce

      // Clear timeout on cleanup
      return () => {
        clearTimeout(handler);
      };
    } else {
      setShowSearchResults(false);
    }
  }, [searchQuery, onClientSearch, customerSelected]);

  // Update total price calculation when device list changes
  useEffect(() => {
    const newTotal = deviceList.reduce(
      (acc, device) => {
        acc.initial += parseFloat(device.initialPrice);
        acc.recurring += parseFloat(device.recurringPrice);
        return acc;
      },
      { initial: 0, recurring: 0 }
    );

    setTotalPrice(newTotal);
  }, [deviceList]);

  // Add device to the list (for multi-item mode)
  const addDeviceToList = () => {
    if (!validateDeviceForm()) {
      return;
    }

    // Create a new device object from current form data with base properties
    let deviceConfig = {
      ...formData,
      id: Date.now().toString() // Unique ID for device
    };

    // Add tab-specific properties
    if (activeTab === 'dedicated') {
      deviceConfig = {
        ...deviceConfig,
        cpuName: serverOptions.cpus.find(cpu => cpu.id.toString() === formData.cpuModel)?.cpu || 'CPU',
        locationName: filteredOptions.cities.find(city => city.id.toString() === formData.location)?.name || 'Location',
        osName: serverOptions.operating_systems.find(os => os.id.toString() === formData.operatingSystem)?.name || 'OS'
      };
    } else if (activeTab === 'vps') {
      deviceConfig = {
        ...deviceConfig,
        vpsConfigName: vpsOptions.configurations.find(c => c.id.toString() === formData.vpsConfiguration)?.label || 'Configuration',
        locationName: vpsOptions.cities.find(city => city.id.toString() === formData.vpsLocation)?.name || 'Location',
        vpsIpName: vpsOptions.ipPrices.find(ip => ip.id.toString() === formData.vpsIpPrice)?.label || 'IP Option',
        vpsAppName: formData.vpsApplication ?
          vpsOptions.applications.find(app => app.id.toString() === formData.vpsApplication)?.label : 'No Application'
      };
    } else if (activeTab === 'transit') {
      deviceConfig = {
        ...deviceConfig,
        transitConfigName: transitOptions.configurations.find(c => c.id.toString() === formData.transitConfiguration)?.label || 'Configuration',
        locationName: transitOptions.cities.find(city => city.id.toString() === formData.transitLocation)?.name || 'Location'
      };
    } else if (activeTab === 'colocation') {
      const selectedBandwidth = colocationOptions.bandwidthOptions.find(
        option => option.id === formData.colocationBandwidth
      );

      deviceConfig = {
        ...deviceConfig,
        rackUnitsName: colocationOptions.rackUnitOptions.find(option => option.id === formData.rackUnits)?.label || formData.rackUnits + 'U',
        kwValue: formData.kw,
        bandwidthName: selectedBandwidth?.label || `${formData.colocationBandwidth} Gbps`,
        colocationBandwidth: formData.colocationBandwidth,
        locationName: colocationOptions.cities.find(city => city.id.toString() === formData.colocationLocation)?.name || 'Location'
      };
    }

    // If editing existing device, update it
    if (editingDeviceIndex !== null) {
      const newList = [...deviceList];
      newList[editingDeviceIndex] = deviceConfig;
      setDeviceList(newList);
      setEditingDeviceIndex(null);
    } else {
      // Otherwise add new device
      setDeviceList([...deviceList, deviceConfig]);
    }

    // Reset form for the next device
    resetDeviceForm();
  };

  // Toggle multi-item mode
  const enableMultiItemMode = () => {
    if (!validateDeviceForm()) {
      return;
    }

    // Create a device from current form data with base properties
    let deviceConfig = {
      ...formData,
      id: Date.now().toString() // Unique ID for device
    };

    // Add tab-specific properties
    if (activeTab === 'dedicated') {
      deviceConfig = {
        ...deviceConfig,
        cpuName: serverOptions.cpus.find(cpu => cpu.id.toString() === formData.cpuModel)?.cpu || 'CPU',
        locationName: filteredOptions.cities.find(city => city.id.toString() === formData.location)?.name || 'Location',
        osName: serverOptions.operating_systems.find(os => os.id.toString() === formData.operatingSystem)?.name || 'OS'
      };
    } else if (activeTab === 'vps') {
      deviceConfig = {
        ...deviceConfig,
        vpsConfigName: vpsOptions.configurations.find(c => c.id.toString() === formData.vpsConfiguration)?.label || 'Configuration',
        locationName: vpsOptions.cities.find(city => city.id.toString() === formData.vpsLocation)?.name || 'Location',
        vpsIpName: vpsOptions.ipPrices.find(ip => ip.id.toString() === formData.vpsIpPrice)?.label || 'IP Option',
        vpsAppName: formData.vpsApplication ?
          vpsOptions.applications.find(app => app.id.toString() === formData.vpsApplication)?.label : 'No Application'
      };
    } else if (activeTab === 'transit') {
      const selectedAdditionalIps = transitOptions.additionalIps?.find(
        ips => ips.id.toString() === formData.transitAdditionalIps
      );

      deviceConfig = {
        ...deviceConfig,
        transitConfigName: transitOptions.configurations.find(c => c.id.toString() === formData.transitConfiguration)?.label || 'Configuration',
        locationName: transitOptions.cities.find(city => city.id.toString() === formData.transitLocation)?.name || 'Location',
        transitAdditionalIpsName: selectedAdditionalIps?.label || 'No Additional IPs'
      };
    } else if (activeTab === 'colocation') {
      const selectedBandwidth = colocationOptions.bandwidthOptions.find(
        option => option.id === formData.colocationBandwidth
      );

      const selectedAdditionalIps = colocationOptions.additionalIps?.find(
        ips => ips.id.toString() === formData.colocationAdditionalIps
      );

      deviceConfig = {
        ...deviceConfig,
        rackUnitsName: colocationOptions.rackUnitOptions.find(option => option.id === formData.rackUnits)?.label || formData.rackUnits + 'U',
        kwValue: formData.kw,
        bandwidthName: selectedBandwidth?.label || `${formData.colocationBandwidth} Gbps`,
        colocationBandwidth: formData.colocationBandwidth,
        colocationAdditionalIpsName: selectedAdditionalIps?.label || 'No Additional IPs',
        locationName: colocationOptions.cities.find(city => city.id.toString() === formData.colocationLocation)?.name || 'Location'
      };
    }

    // Add the current device to the list
    setDeviceList([deviceConfig]);

    // Enable multi-item mode
    setMultiItemMode(true);

    // Reset form for the next device
    resetDeviceForm();
  };



  // Remove device from list
  const removeDevice = (index) => {
    const newList = [...deviceList];
    newList.splice(index, 1);
    setDeviceList(newList);

    // If removing the device being edited, clear edit mode
    if (editingDeviceIndex === index) {
      setEditingDeviceIndex(null);
      resetDeviceForm();
    }
  };

  // Edit existing device
  const editDevice = (index) => {
    const device = deviceList[index];
    setFormData(device);
    setEditingDeviceIndex(index);

    // Ensure filtered options are set for this CPU
    updateFilteredOptions(device.cpuModel, serverOptions.configurations, serverOptions);
  };

  const handleAddOs = async () => {
    try {
      if (!newOsData.os_name.trim() || !newOsData.os_template.trim()) {
        alert('OS name and template are required');
        return;
      }

      setIsLoading(true);
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_server_config.php?f=add_operating_system`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          os_name: newOsData.os_name,
          os_template: newOsData.os_template,
          logo_url: newOsData.logo_url
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        setShowAddOsModal(false);
        setNewOsData({ os_name: '', os_template: '', logo_url: '' });

        // Refresh server options to get the new OS
        fetchServerOptions();

        alert('Operating system added successfully');
      } else {
        alert(result.error || 'Failed to add operating system');
      }

      setIsLoading(false);
    } catch (err) {
      setIsLoading(false);
      console.error("Error adding operating system:", err);
      alert('Failed to add operating system: ' + err.message);
    }
  };

  // Fetch server configuration options
  const fetchServerOptions = async () => {
    try {
      setIsLoading(true);

      const response = await fetch(`${API_URL}/api_admin_server_config.php?f=get_server_options`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.options) {
        // Store the full options set
        setServerOptions(data.options);

        // If we have configurations and CPUs, set the first CPU as the default
        if (data.options.configurations && data.options.configurations.length > 0 &&
            data.options.cpus && data.options.cpus.length > 0) {

          // Get CPUs that have configurations
          const configuredCpuIds = data.options.configurations.map(config => config.cpu_id);
          const availableCpus = data.options.cpus.filter(cpu => configuredCpuIds.includes(cpu.id));

          if (availableCpus.length > 0) {
            const defaultCpuId = availableCpus[0].id.toString();
            setFormData(prev => ({...prev, cpuModel: defaultCpuId}));

            // Update filtered options for this CPU
            updateFilteredOptions(defaultCpuId, data.options.configurations, data.options);
          }
        }

        // Set default OS if available
        if (data.options.operating_systems && data.options.operating_systems.length > 0) {
          setFormData(prev => ({...prev, operatingSystem: data.options.operating_systems[0].id.toString()}));
        }

        // Ensure prices are calculated after initial setup
        setTimeout(() => {
          // Find the available CPUs again to get the default
          const configuredCpuIds = data.options.configurations.map(config => config.cpu_id);
          const availableCpus = data.options.cpus.filter(cpu => configuredCpuIds.includes(cpu.id));

          // Get the default CPU ID
          const defaultCpuId = availableCpus?.[0]?.id?.toString();
          if (defaultCpuId) {
            // Explicitly calculate prices based on default options
            calculateAndSetInitialPrices(defaultCpuId, data.options);
          }
        }, 100);
      }
    } catch (err) {
      console.error("Error fetching server options:", err);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch cities data from the database
  const fetchCitiesData = async () => {
    try {
      const token = localStorage.getItem('admin_token');
      console.log('Fetching cities data with token:', token ? 'Token exists' : 'No token');

      // Try to fetch from the VPS options endpoint first, which already includes cities
      try {
        const vpsOptionsResponse = await fetch(`${API_URL}/api_admin_vps.php?f=get_vps_options`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            token: token
          })
        });

        if (vpsOptionsResponse.ok) {
          const responseText = await vpsOptionsResponse.text();
          try {
            const vpsData = JSON.parse(responseText);
            console.log('VPS options data for cities:', vpsData);

            if (vpsData && vpsData.success && vpsData.options &&
                vpsData.options.cities && Array.isArray(vpsData.options.cities) &&
                vpsData.options.cities.length > 0) {
              return vpsData.options.cities;
            }
          } catch (jsonError) {
            console.error('Error parsing VPS options JSON:', jsonError);
            // Continue to next method if JSON parsing fails
          }
        }
      } catch (vpsError) {
        console.error('Error fetching cities from VPS options:', vpsError);
        // Continue to next method if VPS options fetch fails
      }

      // Fallback to regular cities endpoint
      console.log('Trying regular cities endpoint');
      try {
        const citiesResponse = await fetch(`${API_URL}/api_admin_inventory.php?f=get_cities`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            token: token
          })
        });

        if (citiesResponse.ok) {
          const responseText = await citiesResponse.text();
          try {
            const citiesData = JSON.parse(responseText);
            console.log('Cities data response:', citiesData);

            if (Array.isArray(citiesData) && citiesData.length > 0) {
              return citiesData.map(city => ({
                id: city.id,
                name: city.city || city.name,
                datacenter: city.datacenter || ''
              }));
            }
          } catch (jsonError) {
            console.error('Error parsing cities JSON:', jsonError);
            // Continue to next method if JSON parsing fails
          }
        }
      } catch (citiesError) {
        console.error('Error fetching from cities endpoint:', citiesError);
        // Continue to next method if cities fetch fails
      }

      // Try server options endpoint as a last resort
      console.log('Trying server options endpoint');
      try {
        const serverOptionsResponse = await fetch(`${API_URL}/api_admin_server_config.php?f=get_server_options`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            token: token
          })
        });

        if (serverOptionsResponse.ok) {
          const responseText = await serverOptionsResponse.text();
          try {
            const serverData = JSON.parse(responseText);
            console.log('Server options data for cities:', serverData);

            if (serverData && serverData.success && serverData.options &&
                serverData.options.cities && Array.isArray(serverData.options.cities) &&
                serverData.options.cities.length > 0) {
              return serverData.options.cities;
            }
          } catch (jsonError) {
            console.error('Error parsing server options JSON:', jsonError);
          }
        }
      } catch (serverError) {
        console.error('Error fetching from server options endpoint:', serverError);
      }

      // Return default city if all else fails
      console.log('All city fetch attempts failed, using default city');
      return [{ id: '1', name: 'Default Location', datacenter: 'Default DC' }];
    } catch (err) {
      console.error('Error in fetchCitiesData:', err);
      return [{ id: '1', name: 'Default Location', datacenter: 'Default DC' }];
    }
  };

  // Fetch IP Transit configuration options
  const fetchTransitOptions = async () => {
    try {
      setIsLoading(true);

      // For debugging, log the token
      const token = localStorage.getItem('admin_token');
      console.log('Fetching IP Transit options with token:', token ? 'Token exists' : 'No token');

      // Fetch IP Transit options
      const response = await fetch(`${API_URL}/api_admin_ip_transit.php?f=get_ip_transit_options`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      // Log the response status
      console.log('IP Transit options API response status:', response.status);

      if (response.ok) {
        const responseText = await response.text();
        try {
          const data = JSON.parse(responseText);
          console.log('IP Transit options API response data:', data);

          if (data && data.success && data.options) {
            setTransitOptions(data.options);
            console.log('Setting IP Transit options:', data.options);

            // Fetch additional IPs
            try {
              const additionalIpsResponse = await fetch(`${API_URL}/api_admin_ip_transit.php?f=get_additional_ips`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  token: token
                })
              });

              if (additionalIpsResponse.ok) {
                const additionalIpsText = await additionalIpsResponse.text();
                const additionalIpsData = JSON.parse(additionalIpsText);

                if (additionalIpsData && additionalIpsData.success && additionalIpsData.options) {
                  // Update transit options with additional IPs
                  setTransitOptions(prev => ({
                    ...prev,
                    additionalIps: additionalIpsData.options.additionalIps || []
                  }));
                  console.log('Setting IP Transit additional IPs:', additionalIpsData.options.additionalIps);
                }
              } else {
                console.error('Additional IPs API error response:', additionalIpsResponse.status);
              }
            } catch (additionalIpsError) {
              console.error('Error fetching additional IPs:', additionalIpsError);
            }

            // Set default values
            const formUpdates = {};

            if (data.options.configurations && data.options.configurations.length > 0) {
              formUpdates.transitConfiguration = data.options.configurations[0].id.toString();
            }

            if (data.options.cities && data.options.cities.length > 0) {
              formUpdates.transitLocation = data.options.cities[0].id.toString();
            }

            // Set default additional IPs if available
            if (data.options.additionalIps && data.options.additionalIps.length > 0) {
              formUpdates.transitAdditionalIps = data.options.additionalIps[0].id.toString();
            }

            // Update form data with all changes at once
            if (Object.keys(formUpdates).length > 0) {
              setFormData(prev => ({
                ...prev,
                ...formUpdates
              }));
              console.log('Updated form data with IP Transit defaults:', formUpdates);
            }

            // Calculate initial IP Transit price
            setTimeout(() => {
              updateTransitPrices();
            }, 100);
          }
        } catch (jsonError) {
          console.error('Error parsing IP Transit options JSON:', jsonError);
        }
      } else {
        console.error('IP Transit options API error response:', response.status);
      }
    } catch (err) {
      console.error("Error in fetchTransitOptions:", err);

      // Use default options with cities data as a fallback
      try {
        // Try to fetch just the cities data
        const citiesData = await fetchCitiesData();
        console.log('Fetched cities data in error handler:', citiesData);

        // Set default mock data in case of error, but use real cities data if available
        const mockOptions = {
          configurations: [{ id: '1', label: '1 Gbps', price: '100.00' }, { id: '2', label: '2 Gbps', price: '180.00' }, { id: '5', label: '5 Gbps', price: '400.00' }, { id: '10', label: '10 Gbps', price: '750.00' }],
          cities: citiesData
        };

        setTransitOptions(mockOptions);
        console.log('Setting fallback IP Transit options with real cities:', mockOptions);

        // Set default form values
        setFormData(prev => ({
          ...prev,
          transitConfiguration: '1',
          transitLocation: citiesData[0]?.id?.toString() || '1'
        }));

        // Calculate price with mock data
        setTimeout(() => {
          updateTransitPrices();
        }, 100);
      } catch (citiesErr) {
        console.error("Error fetching cities data in error handler:", citiesErr);

        // Use completely mock data if everything fails
        const mockOptions = {
          configurations: [{ id: '1', label: '1 Gbps', price: '100.00' }, { id: '2', label: '2 Gbps', price: '180.00' }, { id: '5', label: '5 Gbps', price: '400.00' }, { id: '10', label: '10 Gbps', price: '750.00' }],
          cities: [{ id: '1', name: 'Default Location', datacenter: 'Default DC' }]
        };

        setTransitOptions(mockOptions);
        console.log('Setting completely mock IP Transit options:', mockOptions);

        // Set default form values
        setFormData(prev => ({
          ...prev,
          transitConfiguration: '1',
          transitLocation: '1'
        }));

        // Calculate price with mock data
        setTimeout(() => {
          updateTransitPrices();
        }, 100);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate and update IP Transit prices
  const updateTransitPrices = () => {
    if (activeTab !== 'transit' || priceOverride) return;

    try {
      // Check if transitOptions is available
      if (!transitOptions || !transitOptions.configurations) {
        console.log("IP Transit options not available for price calculation");
        // Set default prices
        setFormData(prev => ({
          ...prev,
          initialPrice: "100.00",
          recurringPrice: "100.00"
        }));
        return;
      }

      // Get the selected IP Transit configuration price
      const selectedConfig = transitOptions.configurations.find(
        config => config.id.toString() === formData.transitConfiguration
      );

      // Get the selected additional IPs price
      const selectedAdditionalIps = transitOptions.additionalIps?.find(
        ips => ips.id.toString() === formData.transitAdditionalIps
      );

      // Calculate total price
      const configPrice = selectedConfig ? parseFloat(selectedConfig.price) : 0;
      const additionalIpsPrice = selectedAdditionalIps ? parseFloat(selectedAdditionalIps.price) : 0;
      const totalPrice = configPrice + additionalIpsPrice;

      console.log("IP Transit price calculation:", {
        configPrice,
        additionalIpsPrice,
        totalPrice,
        selectedConfig,
        selectedAdditionalIps
      });

      // Update form data with calculated prices
      setFormData(prev => ({
        ...prev,
        initialPrice: totalPrice.toFixed(2),
        recurringPrice: totalPrice.toFixed(2)
      }));
    } catch (err) {
      console.error("Error calculating IP Transit price:", err);
      // Set default prices in case of error
      setFormData(prev => ({
        ...prev,
        initialPrice: "100.00",
        recurringPrice: "100.00"
      }));
    }
  };

  // Fetch colocation options (bandwidth and cities)
  const fetchColocationOptions = async () => {
    try {
      setIsLoading(true);
      console.log('Fetching Colocation options');

      // Fetch cities data
      const citiesData = await fetchCitiesData();
      console.log('Fetched cities data for colocation:', citiesData);

      // Fetch IP Transit options to use for bandwidth
      const token = localStorage.getItem('admin_token');
      console.log('Fetching IP Transit options for colocation bandwidth with token:', token ? 'Token exists' : 'No token');

      // Fetch IP Transit options
      const response = await fetch(`${API_URL}/api_admin_ip_transit.php?f=get_ip_transit_options`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      // Log the response status
      console.log('IP Transit options API response status for colocation:', response.status);

      let bandwidthOptions = [];

      if (response.ok) {
        const responseText = await response.text();
        try {
          const data = JSON.parse(responseText);
          console.log('IP Transit options API response data for colocation:', data);

          if (data && data.success && data.options && data.options.configurations) {
            // Use IP Transit configurations as bandwidth options
            bandwidthOptions = data.options.configurations;
            console.log('Using IP Transit configurations as bandwidth options:', bandwidthOptions);
          } else {
            // Use default bandwidth options if API response doesn't have configurations
            bandwidthOptions = [
              { id: '1', label: '1 Gbps', price: '100.00' },
              { id: '10', label: '10 Gbps', price: '500.00' },
              { id: '100', label: '100 Gbps', price: '2000.00' }
            ];
            console.log('Using default bandwidth options:', bandwidthOptions);
          }
        } catch (jsonError) {
          console.error('Error parsing IP Transit options JSON for colocation:', jsonError);
          // Use default bandwidth options if JSON parsing fails
          bandwidthOptions = [
            { id: '1', label: '1 Gbps', price: '100.00' },
            { id: '10', label: '10 Gbps', price: '500.00' },
            { id: '100', label: '100 Gbps', price: '2000.00' }
          ];
          console.log('Using default bandwidth options due to JSON error:', bandwidthOptions);
        }
      } else {
        console.error('IP Transit options API error response for colocation:', response.status);
        // Use default bandwidth options if API call fails
        bandwidthOptions = [
          { id: '1', label: '1 Gbps', price: '100.00' },
          { id: '10', label: '10 Gbps', price: '500.00' },
          { id: '100', label: '100 Gbps', price: '2000.00' }
        ];
        console.log('Using default bandwidth options due to API error:', bandwidthOptions);
      }

      // Fetch additional IPs
      let additionalIps = [];
      try {
        const additionalIpsResponse = await fetch(`${API_URL}/api_admin_ip_transit.php?f=get_additional_ips`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            token: token
          })
        });

        if (additionalIpsResponse.ok) {
          const additionalIpsText = await additionalIpsResponse.text();
          const additionalIpsData = JSON.parse(additionalIpsText);

          if (additionalIpsData && additionalIpsData.success && additionalIpsData.options) {
            additionalIps = additionalIpsData.options.additionalIps || [];
            console.log('Setting Colocation additional IPs:', additionalIps);
          }
        } else {
          console.error('Additional IPs API error response for colocation:', additionalIpsResponse.status);
        }
      } catch (additionalIpsError) {
        console.error('Error fetching additional IPs for colocation:', additionalIpsError);
      }

      // Update colocation options with cities data, bandwidth options, and additional IPs
      setColocationOptions(prev => ({
        ...prev,
        bandwidthOptions: bandwidthOptions,
        additionalIps: additionalIps,
        cities: citiesData
      }));

      // Set default values
      const formUpdates = {};

      if (bandwidthOptions && bandwidthOptions.length > 0) {
        formUpdates.colocationBandwidth = bandwidthOptions[0].id.toString();
      } else {
        formUpdates.colocationBandwidth = '1'; // Default to 1 Gbps if no options
      }

      if (additionalIps && additionalIps.length > 0) {
        formUpdates.colocationAdditionalIps = additionalIps[0].id.toString();
      }

      if (citiesData && citiesData.length > 0) {
        formUpdates.colocationLocation = citiesData[0].id.toString();
      } else {
        formUpdates.colocationLocation = '1'; // Default location if no cities
      }

      // Update form data with all changes at once
      setFormData(prev => ({
        ...prev,
        ...formUpdates
      }));
      console.log('Updated form data with Colocation defaults:', formUpdates);

      // Calculate initial colocation price
      setTimeout(() => {
        updateColocationPrices();
      }, 100);

    } catch (err) {
      console.error("Error in fetchColocationOptions:", err);
      handleColocationFetchError();
    } finally {
      setIsLoading(false);
    }
  };

  // Handle errors in colocation options fetching
  const handleColocationFetchError = async () => {
    try {
      console.log('Using fallback options for colocation');

      // Use default city, bandwidth options, and additional IPs if all else fails
      const defaultCity = [{ id: '1', name: 'Default Location', datacenter: 'Default DC' }];
      const defaultBandwidth = [
        { id: '1', label: '1 Gbps', price: '100.00' },
        { id: '10', label: '10 Gbps', price: '500.00' },
        { id: '100', label: '100 Gbps', price: '2000.00' }
      ];
      const defaultAdditionalIps = [
        { id: '1', label: '1 Additional IP', price: '5.00' },
        { id: '4', label: '4 Additional IPs', price: '15.00' },
        { id: '8', label: '8 Additional IPs', price: '25.00' }
      ];

      setColocationOptions(prev => ({
        ...prev,
        bandwidthOptions: defaultBandwidth,
        additionalIps: defaultAdditionalIps,
        cities: defaultCity
      }));

      setFormData(prev => ({
        ...prev,
        colocationLocation: '1',
        colocationBandwidth: '1',
        colocationAdditionalIps: '1'
      }));

      // Calculate price with default data
      setTimeout(() => {
        updateColocationPrices();
      }, 100);
    } catch (err) {
      console.error("Error in handleColocationFetchError:", err);
    }
  };

  // Calculate and update Colocation prices
  const updateColocationPrices = () => {
    if (activeTab !== 'colocation' || priceOverride) return;

    try {
      // Get the selected rack unit option price
      const selectedRackUnit = colocationOptions.rackUnitOptions.find(
        option => option.id === formData.rackUnits
      );

      // Get the selected bandwidth option price
      const selectedBandwidth = colocationOptions.bandwidthOptions.find(
        option => option.id === formData.colocationBandwidth
      );

      // Get the selected additional IPs price
      const selectedAdditionalIps = colocationOptions.additionalIps?.find(
        ips => ips.id.toString() === formData.colocationAdditionalIps
      );

      // Calculate base price from rack units
      const rackUnitPrice = selectedRackUnit ? parseFloat(selectedRackUnit.price) : 0;

      // Calculate bandwidth price
      const bandwidthPrice = selectedBandwidth ? parseFloat(selectedBandwidth.price) : 0;

      // Calculate additional IPs price
      const additionalIpsPrice = selectedAdditionalIps ? parseFloat(selectedAdditionalIps.price) : 0;

      // Calculate additional price for kW
      const kwPrice = parseFloat(formData.kw);

      // Calculate total price
      const totalPrice = rackUnitPrice + bandwidthPrice + additionalIpsPrice + kwPrice;

      console.log("Colocation price calculation:", {
        rackUnitPrice,
        bandwidthPrice,
        additionalIpsPrice,
        kwPrice,
        totalPrice,
        selectedRackUnit,
        selectedBandwidth,
        selectedAdditionalIps
      });

      // Update form data with calculated prices
      setFormData(prev => ({
        ...prev,
        initialPrice: totalPrice.toFixed(2),
        recurringPrice: totalPrice.toFixed(2)
      }));
    } catch (err) {
      console.error("Error calculating Colocation price:", err);
      // Set default prices in case of error
      setFormData(prev => ({
        ...prev,
        initialPrice: "450.00",
        recurringPrice: "450.00"
      }));
    }
  };

  // Fetch VPS configuration options
  const fetchVpsOptions = async () => {
    try {
      setIsLoading(true);

      // For debugging, log the token
      const token = localStorage.getItem('admin_token');
      console.log('Fetching VPS options with token:', token ? 'Token exists' : 'No token');

      // Fetch VPS options first
      let vpsOptionsData = null;
      let vpsOptionsSuccess = false;

      try {
        const response = await fetch(`${API_URL}/api_admin_vps.php?f=get_vps_options`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            token: token
          })
        });

        // Log the response status
        console.log('VPS options API response status:', response.status);

        if (response.ok) {
          const responseText = await response.text();
          try {
            const data = JSON.parse(responseText);
            console.log('VPS options API response data:', data);

            if (data && data.success && data.options) {
              vpsOptionsData = data.options;
              vpsOptionsSuccess = true;
            }
          } catch (jsonError) {
            console.error('Error parsing VPS options JSON:', jsonError);
          }
        } else {
          console.error('VPS options API error response:', response.status);
        }
      } catch (vpsError) {
        console.error('Error fetching VPS options:', vpsError);
      }

      // Fetch cities data separately to ensure we have it
      const citiesData = await fetchCitiesData();
      console.log('Fetched cities data:', citiesData);

      // Prepare options object
      let options;

      if (vpsOptionsSuccess) {
        // Use real VPS options data but override cities with our fetched data
        options = {
          ...vpsOptionsData,
          cities: citiesData
        };
      } else {
        // Use default options with our fetched cities data
        options = {
          configurations: [{ id: '1', label: 'Basic VPS', price: '10.00' }],
          ipPrices: [{ id: '1', label: 'Single IP', price: '5.00' }],
          applications: [{ id: '1', label: 'None' }],
          cities: citiesData
        };
      }

      // Store the options
      setVpsOptions(options);
      console.log('Setting VPS options:', options);

      // Set default values
      const formUpdates = {};

      if (options.configurations && options.configurations.length > 0) {
        formUpdates.vpsConfiguration = options.configurations[0].id.toString();
      }

      if (options.ipPrices && options.ipPrices.length > 0) {
        formUpdates.vpsIpPrice = options.ipPrices[0].id.toString();
      }

      if (options.cities && options.cities.length > 0) {
        formUpdates.vpsLocation = options.cities[0].id.toString();
      }

      // Update form data with all changes at once
      if (Object.keys(formUpdates).length > 0) {
        setFormData(prev => ({
          ...prev,
          ...formUpdates
        }));
        console.log('Updated form data with defaults:', formUpdates);
      }

      // Calculate initial VPS price
      setTimeout(() => {
        updateVpsPrices();
      }, 100);
    } catch (err) {
      console.error("Error in fetchVpsOptions:", err);

      // Use default options with cities data as a fallback
      try {
        // Try to fetch just the cities data
        const citiesData = await fetchCitiesData();
        console.log('Fetched cities data in error handler:', citiesData);

        // Set default mock data in case of error, but use real cities data if available
        const mockOptions = {
          configurations: [{ id: '1', label: 'Basic VPS', price: '10.00' }],
          ipPrices: [{ id: '1', label: 'Single IP', price: '5.00' }],
          applications: [{ id: '1', label: 'None' }],
          cities: citiesData
        };

        setVpsOptions(mockOptions);
        console.log('Setting fallback VPS options with real cities:', mockOptions);

        // Set default form values
        setFormData(prev => ({
          ...prev,
          vpsConfiguration: '1',
          vpsIpPrice: '1',
          vpsLocation: citiesData[0]?.id?.toString() || '1'
        }));
      } catch (citiesErr) {
        console.error("Error fetching cities data in error handler:", citiesErr);

        // Use completely mock data if everything fails
        const mockOptions = {
          configurations: [{ id: '1', label: 'Basic VPS', price: '10.00' }],
          ipPrices: [{ id: '1', label: 'Single IP', price: '5.00' }],
          applications: [{ id: '1', label: 'None' }],
          cities: [{ id: '1', name: 'Default Location', datacenter: 'Default DC' }]
        };

        setVpsOptions(mockOptions);
        console.log('Setting completely mock VPS options:', mockOptions);

        // Set default form values
        setFormData(prev => ({
          ...prev,
          vpsConfiguration: '1',
          vpsIpPrice: '1',
          vpsLocation: '1'
        }));
      }

      // Calculate price with mock data
      setTimeout(() => {
        updateVpsPrices();
      }, 100);
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to calculate and set initial prices
  const calculateAndSetInitialPrices = (cpuId, options) => {
    if (!cpuId || !options) return;

    // Find the CPU configuration
    const cpuConfig = options.configurations.find(config => config.cpu_id.toString() === cpuId);
    if (!cpuConfig) return;

    // Get CPU price
    const cpuPrice = parseFloat(cpuConfig.price || 0);

    // Find default bandwidth, storage, and subnet
    let bandwidthPrice = 0;
    let storagePrice = 0;
    let subnetPrice = 0;

    // Parse configuration options
    const bandwidthIds = cpuConfig.bandwidths ? cpuConfig.bandwidths.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id)) : [];
    const storageIds = cpuConfig.storages ? cpuConfig.storages.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id)) : [];
    const subnetIds = cpuConfig.subnets ? cpuConfig.subnets.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id)) : [];

    // Get first bandwidth option if available
    if (bandwidthIds.length > 0) {
      const defaultBandwidth = options.bandwidths.find(b => b.id === bandwidthIds[0]);
      if (defaultBandwidth && defaultBandwidth.price) {
        bandwidthPrice = parseFloat(defaultBandwidth.price);
      }
    }

    // Get first storage option if available
    if (storageIds.length > 0) {
      const defaultStorage = options.storages.find(s => s.id === storageIds[0]);
      if (defaultStorage && defaultStorage.price) {
        storagePrice = parseFloat(defaultStorage.price);
      }
    }

    // Get first subnet option if available
    if (subnetIds.length > 0) {
      const defaultSubnet = options.subnets.find(s => s.id === subnetIds[0]);
      if (defaultSubnet && defaultSubnet.price) {
        subnetPrice = parseFloat(defaultSubnet.price);
      }
    }

    // Calculate total price
    const totalPrice = cpuPrice + bandwidthPrice + storagePrice + subnetPrice;

    // Update the form data with calculated prices
    setFormData(prev => ({
      ...prev,
      initialPrice: totalPrice.toFixed(2),
      recurringPrice: totalPrice.toFixed(2),
      priceComponents: {
        cpuPrice: cpuPrice,
        bandwidthPrice: bandwidthPrice,
        storagePrice: storagePrice,
        subnetPrice: subnetPrice
      }
    }));
  };

  // Update filtered options when CPU changes
  const updateFilteredOptions = (cpuId, configurations, options) => {
    // Find configuration for this CPU
    const cpuConfig = configurations.find(config => config.cpu_id.toString() === cpuId);

    if (!cpuConfig) {
      setFilteredOptions({
        cities: [],
        bandwidths: [],
        storages: [],
        subnets: []
      });
      return;
    }

    // Safely parse the comma-separated lists into arrays of IDs with error handling
    let cityIds = [];
    if (cpuConfig.cities && typeof cpuConfig.cities === 'string') {
      cityIds = cpuConfig.cities.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
    }

    let bandwidthIds = [];
    if (cpuConfig.bandwidths && typeof cpuConfig.bandwidths === 'string') {
      bandwidthIds = cpuConfig.bandwidths.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
    }

    let storageIds = [];
    if (cpuConfig.storages && typeof cpuConfig.storages === 'string') {
      storageIds = cpuConfig.storages.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
    }

    let subnetIds = [];
    if (cpuConfig.subnets && typeof cpuConfig.subnets === 'string') {
      subnetIds = cpuConfig.subnets.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
    }

    // Make sure we have options to filter
    if (!options.cities || !options.bandwidths || !options.storages || !options.subnets) {
      console.error("Missing options data:", options);
      return;
    }

    // Filter the options to only include those in the arrays
    const filteredCities = options.cities.filter(city => cityIds.includes(parseInt(city.id)));
    const filteredBandwidths = options.bandwidths.filter(bandwidth => bandwidthIds.includes(parseInt(bandwidth.id)));
    const filteredStorages = options.storages.filter(storage => storageIds.includes(parseInt(storage.id)));
    const filteredSubnets = options.subnets.filter(subnet => subnetIds.includes(parseInt(subnet.id)));

    // Update the filtered options
    setFilteredOptions({
      cities: filteredCities,
      bandwidths: filteredBandwidths,
      storages: filteredStorages,
      subnets: filteredSubnets
    });

    // Set default values for these options if available
    const defaultLocation = filteredCities.length > 0 ? filteredCities[0].id.toString() : '';
    const defaultBandwidth = filteredBandwidths.length > 0 ? filteredBandwidths[0].id.toString() : '';
    const defaultStorage = filteredStorages.length > 0 ? filteredStorages[0].id.toString() : '';
    const defaultSubnet = filteredSubnets.length > 0 ? filteredSubnets[0].id.toString() : '';

    setFormData(prev => ({
      ...prev,
      location: defaultLocation,
      bandwidth: defaultBandwidth,
      storage: defaultStorage,
      subnetSize: defaultSubnet
    }));

    // Always update the pricing with the defaults
    setTimeout(() => {
      updatePriceFromConfiguration(
        cpuId,
        defaultLocation,
        defaultBandwidth,
        defaultStorage,
        defaultSubnet
      );
    }, 0);
  };

  // Reset the entire form
  const resetForm = () => {
    setFormData({
      customerName: '',
      customer_id: '',
      clientEmail: '',
      orderNote: 'New Server Order',
      initialPrice: '0.00',
      recurringPrice: '0.00',
      orderType: 'Dedicated Server',

      // Dedicated server specific fields
      cpuModel: '',
      bandwidth: '',
      location: '',
      storage: '',
      subnetSize: '',
      operatingSystem: '',

      // VPS specific fields
      vpsConfiguration: '',
      vpsIpPrice: '',
      vpsLocation: '',
      vpsApplication: '',

      // IP Transit specific fields
      transitConfiguration: '',
      transitLocation: '',
      transitAdditionalIps: '',

      // Colocation specific fields
      rackUnits: '1',
      kw: '1',
      colocationBandwidth: '',
      colocationAdditionalIps: '',
      colocationLocation: '',

      // Other configuration data
      serverConfig: {}
    });
    setSearchQuery('');
    setValidationErrors({});
    setActiveTab('dedicated');
    setPriceOverride(false);
    setFilteredOptions({
      cities: [],
      bandwidths: [],
      storages: [],
      subnets: []
    });
    setDeviceList([]);
    setEditingDeviceIndex(null);
    setIsPriceBreakdownExpanded(false);
  };

  // Reset just the device form fields (keep customer info)
  const resetDeviceForm = () => {
    // Preserve customer information
    const customerInfo = {
      customerName: formData.customerName,
      customer_id: formData.customer_id,
      clientEmail: formData.clientEmail,
      orderNote: formData.orderNote
    };

    // Reset all other fields
    setFormData({
      ...customerInfo,
      initialPrice: '0.00',
      recurringPrice: '0.00',
      orderType: 'Dedicated Server',

      // Dedicated server specific fields
      cpuModel: '',
      bandwidth: '',
      location: '',
      storage: '',
      subnetSize: '',
      operatingSystem: '',

      // VPS specific fields
      vpsConfiguration: '',
      vpsIpPrice: '',
      vpsLocation: '',
      vpsApplication: '',

      // IP Transit specific fields
      transitConfiguration: '',
      transitLocation: '',
      transitAdditionalIps: '',

      // Colocation specific fields
      rackUnits: '1',
      kw: '1',
      colocationBandwidth: '',
      colocationAdditionalIps: '',
      colocationLocation: '',

      // Other configuration data
      serverConfig: {}
    });

    // Reset validation errors for device fields
    const newErrors = { ...validationErrors };
    ['cpuModel', 'bandwidth', 'location', 'storage', 'subnetSize', 'operatingSystem'].forEach(field => {
      delete newErrors[field];
    });
    setValidationErrors(newErrors);

    // Reset price override
    setPriceOverride(false);

    // Clear edit mode
    setEditingDeviceIndex(null);

    // Re-fetch options to reset defaults
    fetchServerOptions();

    // Collapse price breakdown
    setIsPriceBreakdownExpanded(false);
  };

  // Validate customer information
const validateCustomerInfo = () => {
  const errors = {};

  // Check for customer_id specifically, not just customerName
  // Make sure it's a valid numeric ID
  const customerId = formData.customer_id ? formData.customer_id.toString().replace(/\D/g, '') : '';

  if (!customerId) {
    errors.customerName = 'Please select a customer from the dropdown';
    console.log('Customer ID validation failed:', formData.customer_id, 'Cleaned ID:', customerId);
  }

  if (!formData.orderNote) {
    errors.orderNote = 'Order note is required';
  }

  setValidationErrors(prev => ({ ...prev, ...errors }));
  return Object.keys(errors).length === 0;
};

// Create a single order with the current device
const createSingleOrder = async () => {
  // Validate customer info first
  if (!validateCustomerInfo()) {
    console.error('Customer validation failed:', formData.customer_id);
    alert('Please select a customer from the dropdown');
    return;
  }

  // Then validate device form
  if (!validateDeviceForm()) {
    return;
  }

  // Show loading state
  setIsLoading(true);

  try {
    // Ensure customer_id is a valid numeric string
    const customerId = formData.customer_id ? formData.customer_id.toString().replace(/\D/g, '') : '';

    if (!customerId) {
      console.error('Invalid customer_id:', formData.customer_id);
      alert('Please select a valid customer before creating an order.');
      setIsLoading(false);
      return;
    }

    let combinedDescription = '';
    let locationName = 'Unknown';
    let orderItems = [];

    // Create description and items based on active tab
    if (activeTab === 'dedicated') {
      // Get all the component names for the description
      const cpuName = serverOptions.cpus.find(cpu => cpu.id.toString() === formData.cpuModel)?.cpu || 'Selected CPU';
      locationName = filteredOptions.cities.find(city => city.id.toString() === formData.location)?.name || 'Selected Location';
      const bandwidthName = filteredOptions.bandwidths.find(b => b.id.toString() === formData.bandwidth)?.name || 'Selected Bandwidth';
      const storageName = filteredOptions.storages.find(s => s.id.toString() === formData.storage)?.name || 'Selected Storage';
      const subnetName = filteredOptions.subnets.find(s => s.id.toString() === formData.subnetSize)?.name || 'Selected Subnet';
      const osName = serverOptions.operating_systems.find(os => os.id.toString() === formData.operatingSystem)?.name || 'Selected OS';

      // Create a combined description for the main item
      combinedDescription = `${formData.orderType}: ${cpuName} | Location: ${locationName} | Operating System: ${osName} | Bandwidth: ${bandwidthName} | Storage: ${storageName} | Subnet: ${subnetName}`;

      // Create dedicated server order item
      orderItems = [
        {
          name: formData.orderType,
          description: combinedDescription,
          quantity: 1,
          price: parseFloat(formData.initialPrice).toFixed(2),
          // Include IDs for the backend to save in orders_items
          cpuId: formData.cpuModel,
          bandwidthId: formData.bandwidth,
          locationId: formData.location,
          storageId: formData.storage,
          subnetId: formData.subnetSize,
          requirementPrice: parseFloat(formData.recurringPrice).toFixed(2),
          period: 'monthly' // Default to monthly, can be updated based on your needs
        }
      ];
    }
    else if (activeTab === 'vps') {
      // Get VPS component names for the description
      const configName = vpsOptions.configurations.find(c => c.id.toString() === formData.vpsConfiguration)?.label || 'Selected Configuration';
      locationName = vpsOptions.cities.find(city => city.id.toString() === formData.vpsLocation)?.name || 'Selected Location';
      const ipOption = vpsOptions.ipPrices.find(ip => ip.id.toString() === formData.vpsIpPrice)?.label || 'Selected IP Option';
      const appName = formData.vpsApplication ?
        vpsOptions.applications.find(app => app.id.toString() === formData.vpsApplication)?.label || 'Selected Application' :
        'No Application';

      // Create a combined description for the VPS item
      combinedDescription = `${formData.orderType}: ${configName} | Location: ${locationName} | IP Option: ${ipOption} | Application: ${appName}`;

      // Create VPS order item
      orderItems = [
        {
          name: formData.orderType,
          description: combinedDescription,
          quantity: 1,
          price: parseFloat(formData.initialPrice).toFixed(2),
          // Include IDs for the backend to save in orders_items
          vpsConfigurationId: formData.vpsConfiguration,
          vpsIpPriceId: formData.vpsIpPrice,
          locationId: formData.vpsLocation,
          vpsApplicationId: formData.vpsApplication || null,
          requirementPrice: parseFloat(formData.recurringPrice).toFixed(2),
          period: 'monthly' // Default to monthly, can be updated based on your needs
        }
      ];
    }
    else if (activeTab === 'transit') {
      // Get IP Transit component names for the description
      const configName = transitOptions.configurations.find(c => c.id.toString() === formData.transitConfiguration)?.label || 'Selected Configuration';
      locationName = transitOptions.cities.find(city => city.id.toString() === formData.transitLocation)?.name || 'Selected Location';

      // Create a combined description for the IP Transit item
      combinedDescription = `${formData.orderType}: ${configName} | Location: ${locationName}`;

      // Create IP Transit order item
      orderItems = [
        {
          name: formData.orderType,
          description: combinedDescription,
          quantity: 1,
          price: parseFloat(formData.initialPrice).toFixed(2),
          // Include IDs for the backend to save in orders_items
          transitConfigurationId: formData.transitConfiguration,
          locationId: formData.transitLocation,
          requirementPrice: parseFloat(formData.recurringPrice).toFixed(2),
          period: 'monthly' // Default to monthly, can be updated based on your needs
        }
      ];
    }
    else if (activeTab === 'colocation') {
      // Get Colocation component names for the description
      const rackUnitType = colocationOptions.rackUnitOptions.find(option => option.id === formData.rackUnits)?.label || formData.rackUnits + 'U';
      const kwValue = formData.kw;
      const selectedBandwidth = colocationOptions.bandwidthOptions.find(
        option => option.id === formData.colocationBandwidth
      );
      const bandwidthLabel = selectedBandwidth?.label || `${formData.colocationBandwidth} Gbps`;
      locationName = colocationOptions.cities.find(city => city.id.toString() === formData.colocationLocation)?.name || 'Selected Location';

      // Create a combined description for the Colocation item
      combinedDescription = `${formData.orderType}: ${rackUnitType} | kW: ${kwValue} | Bandwidth: ${bandwidthLabel} | Location: ${locationName}`;

      // Create Colocation order item
      orderItems = [
        {
          name: formData.orderType,
          description: combinedDescription,
          quantity: 1,
          price: parseFloat(formData.initialPrice).toFixed(2),
          // Include IDs for the backend to save in orders_items
          rackUnits: formData.rackUnits,
          kw: formData.kw,
          bandwidthId: formData.colocationBandwidth,
          locationId: formData.colocationLocation,
          requirementPrice: parseFloat(formData.recurringPrice).toFixed(2),
          period: 'monthly' // Default to monthly, can be updated based on your needs
        }
      ];
    }

    // Create order with just the current device and the combined description
    // Simplify the data structure to match what the API expects
    const orderData = {
      // Customer information
      customerName: formData.customerName,
      customer_id: customerId, // Use the cleaned ID
      clientEmail: formData.clientEmail,

      // Order details
      orderNote: formData.orderNote,
      status: 'Pending', // Default status for new orders

      // Price information
      initialPrice: parseFloat(formData.initialPrice).toFixed(2),
      recurringPrice: parseFloat(formData.recurringPrice).toFixed(2),

      // Location information
      location: locationName,

      // Item information
      items: orderItems,

      // Authentication
      token: localStorage.getItem('admin_token')
    };

    console.log('Sending single order data:', orderData);

    // Call our new API endpoint
    console.log('API request payload:', JSON.stringify(orderData, null, 2));

    // Check if customer_id is properly set
    if (!orderData.customer_id) {
      console.error('customer_id is missing in the request payload!');
    }

    // Use the api_admin_orders.php endpoint instead of api_admin_invoices.php
    const response = await fetch(`${API_URL}/api_admin_orders.php?f=create_order`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(orderData)
    });

    console.log('API response status:', response.status);

    if (!response.ok) {
      throw new Error(`Server responded with status: ${response.status}`);
    }

    // Get the response text first to debug
    const responseText = await response.text();
    console.log('API response text:', responseText);

    let result;
    try {
      // Try to parse the response as JSON
      result = JSON.parse(responseText);
    } catch (parseError) {
      console.error('Failed to parse JSON response:', parseError);

      // If we can't parse the response but the order was created (we know this because
      // you mentioned the order and invoice are being created), create a synthetic success response
      result = {
        success: true,
        id: 'ORD-' + new Date().getTime(), // Generate a temporary ID
        customerName: formData.customerName,
        date: new Date().toISOString().split('T')[0],
        amount: '€' + parseFloat(formData.initialPrice).toFixed(2),
        status: 'Pending',
        paymentStatus: 'Pending',
        items: 1,
        location: filteredOptions.cities.find(city => city.id.toString() === formData.location)?.name || 'Unknown',
        invoice: {
          proformaNumber: 'Generated' // Placeholder
        }
      };
    }

    // Close the modal and show success message
    onClose();

    // Show success notification
    alert(`Order created successfully! ${result.invoice && result.invoice.proformaNumber ? `Proforma Invoice #${result.invoice.proformaNumber} has been generated.` : ''}`);

    // Call the callback if provided
    if (typeof onSave === 'function') {
      // Make sure the result has the expected format for OrdersPage
      const formattedResult = {
        ...result,
        success: true // Ensure success is set
      };
      onSave(formattedResult);
    }
  } catch (error) {
    console.error('Error creating order:', error);
    alert(`Error creating order: ${error.message}`);
  } finally {
    setIsLoading(false);
  }
};

// Handle submit for multi-device orders
const handleSubmit = async () => {
  // Validate customer information
  if (!validateCustomerInfo()) {
    console.error('Customer validation failed:', formData.customer_id);
    alert('Please select a customer from the dropdown');
    return;
  }

  // Make sure we have at least one device
  if (deviceList.length === 0) {
    alert('Please add at least one device to the order');
    return;
  }

  // Show loading state
  setIsLoading(true);

  try {
    // Format each device with a combined description
    const formattedDevices = deviceList.map(device => {
      let deviceDescription = '';
      let deviceData = {};

      // Handle different device types
      if (device.orderType === 'Dedicated Server') {
        // Get component names for this device
        const cpuName = device.cpuName || 'CPU';
        const locationName = device.locationName || 'Location';
        const osName = device.osName || 'OS';
        const bandwidthName = serverOptions.bandwidths.find(b => b.id.toString() === device.bandwidth)?.name || 'Bandwidth';
        const storageName = serverOptions.storages.find(s => s.id.toString() === device.storage)?.name || 'Storage';
        const subnetName = serverOptions.subnets.find(s => s.id.toString() === device.subnetSize)?.name || 'Subnet';

        // Create a comprehensive description for dedicated server
        deviceDescription = `${device.orderType}: ${cpuName} | Location: ${locationName} | OS: ${osName} | Bandwidth: ${bandwidthName} | Storage: ${storageName} | Subnet: ${subnetName}`;

        deviceData = {
          cpuId: device.cpuModel,
          bandwidthId: device.bandwidth,
          locationId: device.location,
          storageId: device.storage,
          subnetId: device.subnetSize,
          cpuName,
          locationName,
          osName,
          bandwidthName,
          storageName,
          subnetName
        };
      }
      else if (device.orderType === 'VPS') {
        // Get VPS component names for this device
        const configName = device.vpsConfigName || 'Configuration';
        const locationName = device.locationName || 'Location';
        const ipOption = device.vpsIpName || 'IP Option';
        const appName = device.vpsAppName || 'No Application';

        // Create a comprehensive description for VPS
        deviceDescription = `${device.orderType}: ${configName} | Location: ${locationName} | IP Option: ${ipOption} | Application: ${appName}`;

        deviceData = {
          vpsConfigurationId: device.vpsConfiguration,
          vpsIpPriceId: device.vpsIpPrice,
          locationId: device.vpsLocation || device.location,
          vpsApplicationId: device.vpsApplication || null,
          vpsConfigName: configName,
          locationName,
          vpsIpName: ipOption,
          vpsAppName: appName
        };
      }
      else if (device.orderType === 'IP Transit') {
        // Get IP Transit component names for this device
        const configName = device.transitConfigName || 'Configuration';
        const locationName = device.locationName || 'Location';

        // Create a comprehensive description for IP Transit
        deviceDescription = `${device.orderType}: ${configName} | Location: ${locationName}`;

        deviceData = {
          transitConfigurationId: device.transitConfiguration,
          locationId: device.transitLocation || device.location,
          transitConfigName: configName,
          locationName
        };
      }
      else if (device.orderType === 'Colocation') {
        // Get Colocation component names for this device
        const rackUnitsName = device.rackUnitsName || 'Rack Units';
        const kwValue = device.kwValue || '1';
        const bandwidthName = device.bandwidthName || '1 Gbps';
        const locationName = device.locationName || 'Location';

        // Create a comprehensive description for Colocation
        deviceDescription = `${device.orderType}: ${rackUnitsName} | kW: ${kwValue} | Bandwidth: ${bandwidthName} | Location: ${locationName}`;

        deviceData = {
          rackUnits: device.rackUnits,
          kw: device.kwValue,
          bandwidthId: device.colocationBandwidth,
          locationId: device.colocationLocation || device.location,
          rackUnitsName,
          kwValue,
          bandwidthName,
          locationName
        };
      }

      return {
        ...device,
        ...deviceData,
        combinedDescription: deviceDescription
      };
    });

    // Ensure customer_id is a valid numeric string
    const customerId = formData.customer_id ? formData.customer_id.toString().replace(/\D/g, '') : '';

    if (!customerId) {
      console.error('Invalid customer_id for multi-device order:', formData.customer_id);
      alert('Please select a valid customer before creating an order.');
      setIsLoading(false);
      return;
    }

    // Determine location display - use the first device's location or 'Multiple'
    let locationDisplay = 'Multiple Locations';
    if (deviceList.length > 0) {
      const firstDevice = deviceList[0];
      if (firstDevice.orderType === 'Dedicated Server' && firstDevice.location) {
        locationDisplay = filteredOptions.cities.find(city => city.id.toString() === firstDevice.location)?.name || 'Unknown';
      } else if (firstDevice.orderType === 'VPS' && firstDevice.vpsLocation) {
        locationDisplay = vpsOptions.cities.find(city => city.id.toString() === firstDevice.vpsLocation)?.name || 'Unknown';
      } else if (firstDevice.orderType === 'IP Transit' && firstDevice.transitLocation) {
        locationDisplay = transitOptions.cities.find(city => city.id.toString() === firstDevice.transitLocation)?.name || 'Unknown';
      } else if (firstDevice.orderType === 'Colocation' && firstDevice.colocationLocation) {
        locationDisplay = colocationOptions.cities.find(city => city.id.toString() === firstDevice.colocationLocation)?.name || 'Unknown';
      }
    }

    // Prepare data for API - simplify to match what the API expects
    const orderData = {
      // Customer information
      customerName: formData.customerName,
      customer_id: customerId, // Use the cleaned ID
      clientEmail: formData.clientEmail,

      // Order details
      orderNote: formData.orderNote,
      status: 'Pending', // Default status for new orders

      // Price information
      initialPrice: totalPrice.initial.toFixed(2),
      recurringPrice: totalPrice.recurring.toFixed(2),

      // Location information
      location: locationDisplay,

      // Item information
      items: formattedDevices.map(device => {
        const baseItem = {
          name: device.orderType || 'Server',
          description: device.combinedDescription,
          quantity: 1,
          price: device.initialPrice,
          requirementPrice: device.recurringPrice,
          period: 'monthly' // Default to monthly, can be updated based on your needs
        };

        // Add type-specific properties
        if (device.orderType === 'Dedicated Server') {
          return {
            ...baseItem,
            cpuId: device.cpuModel,
            bandwidthId: device.bandwidth,
            locationId: device.location,
            storageId: device.storage,
            subnetId: device.subnetSize
          };
        } else if (device.orderType === 'VPS') {
          return {
            ...baseItem,
            vpsConfigurationId: device.vpsConfiguration,
            vpsIpPriceId: device.vpsIpPrice,
            locationId: device.vpsLocation,
            vpsApplicationId: device.vpsApplication
          };
        } else if (device.orderType === 'IP Transit') {
          return {
            ...baseItem,
            transitConfigurationId: device.transitConfiguration,
            locationId: device.transitLocation,
            transitAdditionalIpsId: device.transitAdditionalIps
          };
        } else if (device.orderType === 'Colocation') {
          return {
            ...baseItem,
            rackUnits: device.rackUnits,
            kw: device.kwValue,
            bandwidthId: device.colocationBandwidth,
            locationId: device.colocationLocation,
            colocationAdditionalIpsId: device.colocationAdditionalIps
          };
        }

        return baseItem;
      }),

      // Authentication
      token: localStorage.getItem('admin_token')
    };

    console.log('Sending order data:', orderData);

    // Call our API endpoint
    console.log('Multi-device API request payload:', JSON.stringify(orderData, null, 2));

    // Check if customer_id is properly set
    if (!orderData.customer_id) {
      console.error('customer_id is missing in the multi-device request payload!');
    }

    // Use the api_admin_orders.php endpoint instead of api_admin_invoices.php
    const response = await fetch(`${API_URL}/api_admin_orders.php?f=create_order`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(orderData)
    });

    console.log('Multi-device API response status:', response.status);

    if (!response.ok) {
      throw new Error(`Server responded with status: ${response.status}`);
    }

    // Get the response text first to debug
    const responseText = await response.text();
    console.log('Multi-device API response text:', responseText);

    let result;
    try {
      // Try to parse the response as JSON
      result = JSON.parse(responseText);
    } catch (parseError) {
      console.error('Failed to parse JSON response:', parseError);

      // If we can't parse the response but the order was created (we know this because
      // you mentioned the order and invoice are being created), create a synthetic success response
      result = {
        success: true,
        id: 'ORD-' + new Date().getTime(), // Generate a temporary ID
        customerName: formData.customerName,
        date: new Date().toISOString().split('T')[0],
        amount: '€' + parseFloat(totalPrice.initial).toFixed(2),
        status: 'Pending',
        paymentStatus: 'Pending',
        items: deviceList.length,
        location: deviceList.length > 0 && deviceList[0].location ?
          filteredOptions.cities.find(city => city.id.toString() === deviceList[0].location)?.name || 'Unknown' :
          'Multiple Locations',
        invoice: {
          proformaNumber: 'Generated' // Placeholder
        }
      };
    }

    // Close the modal and show success message
    onClose();

    // Show success notification
    alert(`Order created successfully! ${result.invoice && result.invoice.proformaNumber ? `Proforma Invoice #${result.invoice.proformaNumber} has been generated.` : ''}`);

    // Call the callback if provided
    if (typeof onSave === 'function') {
      // Make sure the result has the expected format for OrdersPage
      const formattedResult = {
        ...result,
        success: true // Ensure success is set
      };
      onSave(formattedResult);
    }
  } catch (error) {
    console.error('Error creating order:', error);
    alert(`Error creating order: ${error.message}`);
  } finally {
    setIsLoading(false);
  }
};

  // Validate device form
  const validateDeviceForm = () => {
    const errors = {};

    // Dedicated server specific validation
    if (activeTab === 'dedicated') {
      if (!formData.cpuModel) {
        errors.cpuModel = 'CPU model is required';
      }

      if (!formData.bandwidth) {
        errors.bandwidth = 'Bandwidth is required';
      }

      if (!formData.location) {
        errors.location = 'Location is required';
      }

      if (!formData.storage) {
        errors.storage = 'Storage is required';
      }

      if (!formData.subnetSize) {
        errors.subnetSize = 'Subnet size is required';
      }

      if (!formData.operatingSystem) {
        errors.operatingSystem = 'Operating system is required';
      }
    }

    // VPS specific validation
    else if (activeTab === 'vps') {
      if (!formData.vpsConfiguration) {
        errors.vpsConfiguration = 'VPS configuration is required';
      }

      if (!formData.vpsLocation) {
        errors.vpsLocation = 'Location is required';
      }

      if (!formData.vpsIpPrice) {
        errors.vpsIpPrice = 'IP option is required';
      }
    }

    // IP Transit specific validation
    else if (activeTab === 'transit') {
      if (!formData.transitConfiguration) {
        errors.transitConfiguration = 'IP Transit configuration is required';
      }

      if (!formData.transitLocation) {
        errors.transitLocation = 'Location is required';
      }

      // Additional IPs is optional, so no validation required
    }

    // Colocation specific validation
    else if (activeTab === 'colocation') {
      if (!formData.rackUnits) {
        errors.rackUnits = 'Rack units selection is required';
      }

      if (!formData.kw) {
        errors.kw = 'kW value is required';
      } else if (isNaN(parseFloat(formData.kw)) || parseFloat(formData.kw) <= 0) {
        errors.kw = 'kW must be a positive number';
      }

      if (!formData.colocationBandwidth) {
        errors.colocationBandwidth = 'Bandwidth is required';
      }

      if (!formData.colocationLocation) {
        errors.colocationLocation = 'Location is required';
      }

      // Additional IPs is optional, so no validation required
    }

    setValidationErrors(prev => ({ ...prev, ...errors }));
    return Object.keys(errors).length === 0;
  };

  // Update price based on selected configuration
  const updatePriceFromConfiguration = (cpuId, _locationId, bandwidthId, storageId, subnetId) => {
    try {
      // Get the CPU's base price from the configuration
      const { configurations, bandwidths, subnets, storages, cpus } = serverOptions;

      if (!configurations || configurations.length === 0) {
        console.log("No configurations available for price calculation");
        return;
      }

      // Use provided values or values from current form state
      const currentCpuId = cpuId || formData.cpuModel;

      // Find the configuration for this CPU
      const cpuConfig = configurations.find(config => config.cpu_id.toString() === currentCpuId);

      if (!cpuConfig) {
        console.log("No configuration found for CPU ID:", currentCpuId);
        return;
      }

      // Get the CPU model details from the cpus array
      const cpuModel = cpus && cpus.length > 0 ?
        cpus.find(cpu => cpu.id.toString() === currentCpuId) : null;

      // Base price from the CPU model or configuration
      let basePrice = 0;
      if (cpuModel && cpuModel.price) {
        basePrice = parseFloat(cpuModel.price);
      } else if (cpuConfig.price) {
        basePrice = parseFloat(cpuConfig.price);
      }

      // Add bandwidth price if applicable
      let bandwidthPrice = 0;
      const currentBandwidthId = bandwidthId || formData.bandwidth;

      if (currentBandwidthId && bandwidths && bandwidths.length > 0) {
        const selectedBandwidth = bandwidths.find(b => b.id.toString() === currentBandwidthId);
        if (selectedBandwidth && selectedBandwidth.price) {
          bandwidthPrice = parseFloat(selectedBandwidth.price);
        }
      }

      // Add subnet price if applicable
      let subnetPrice = 0;
      const currentSubnetId = subnetId || formData.subnetSize;

      if (currentSubnetId && subnets && subnets.length > 0) {
        const selectedSubnet = subnets.find(s => s.id.toString() === currentSubnetId);
        if (selectedSubnet && selectedSubnet.price) {
          subnetPrice = parseFloat(selectedSubnet.price);
        }
      }

      // Add storage price if applicable
      let storagePrice = 0;
      const currentStorageId = storageId || formData.storage;

      if (currentStorageId && storages && storages.length > 0) {
        const selectedStorage = storages.find(s => s.id.toString() === currentStorageId);
        if (selectedStorage && selectedStorage.price) {
          storagePrice = parseFloat(selectedStorage.price);
        }
      }

      // Calculate total price - ensure all components are included
      const totalPrice = basePrice + bandwidthPrice + subnetPrice + storagePrice;

      console.log("Price calculation:", {
        basePrice,
        bandwidthPrice,
        storagePrice,
        subnetPrice,
        totalPrice
      });

      // Store the price components in the form data for display in the UI
      const priceComponents = {
        cpuPrice: basePrice,
        bandwidthPrice,
        storagePrice,
        subnetPrice
      };

      // Update form data with calculated prices
      setFormData(prev => ({
        ...prev,
        initialPrice: totalPrice.toFixed(2),
        recurringPrice: totalPrice.toFixed(2), // Set recurring price equal to initial price by default
        priceComponents: priceComponents // Store price components for UI display
      }));
    } catch (err) {
      console.error("Error calculating price:", err);
    }
  };

  // Calculate and update VPS prices
  const updateVpsPrices = () => {
    if (activeTab !== 'vps' || priceOverride) return;

    try {
      // Check if vpsOptions is available
      if (!vpsOptions || !vpsOptions.configurations || !vpsOptions.ipPrices) {
        console.log("VPS options not available for price calculation");
        // Set default prices
        setFormData(prev => ({
          ...prev,
          initialPrice: "15.00",
          recurringPrice: "15.00"
        }));
        return;
      }

      // Get the selected VPS configuration price
      const selectedConfig = vpsOptions.configurations.find(
        config => config.id.toString() === formData.vpsConfiguration
      );

      // Get the selected IP price
      const selectedIp = vpsOptions.ipPrices.find(
        ip => ip.id.toString() === formData.vpsIpPrice
      );

      // Calculate total price
      const configPrice = selectedConfig ? parseFloat(selectedConfig.price) : 0;
      const ipPrice = selectedIp ? parseFloat(selectedIp.price) : 0;
      const totalPrice = configPrice + ipPrice;

      console.log("VPS price calculation:", {
        configPrice,
        ipPrice,
        totalPrice,
        selectedConfig,
        selectedIp
      });

      // Store the price components for display in the price breakdown
      const priceComponents = {
        configPrice: configPrice,
        ipPrice: ipPrice
      };

      // Update form data with calculated prices and price components
      setFormData(prev => ({
        ...prev,
        initialPrice: totalPrice.toFixed(2),
        recurringPrice: totalPrice.toFixed(2),
        priceComponents: priceComponents // Store price components for UI display
      }));
    } catch (err) {
      console.error("Error calculating VPS price:", err);
      // Set default prices in case of error
      setFormData(prev => ({
        ...prev,
        initialPrice: "15.00",
        recurringPrice: "15.00"
      }));
    }
  };

  // Handle form field changes
  const handleChange = (e) => {
    const { name, value } = e.target;

    // Special handling for price fields when price override is enabled
    if (priceOverride && (name === 'initialPrice' || name === 'recurringPrice')) {
      // Remove currency symbol and validate as number
      let numericValue = value.replace(/[€£$]/g, '').trim();
      numericValue = numericValue.replace(/[^0-9.]/g, '');

      // Ensure it's a valid number
      if (numericValue === '' || !isNaN(parseFloat(numericValue))) {
        setFormData(prev => ({
          ...prev,
          [name]: numericValue
        }));
      }
      return;
    }

    // Special handling for CPU selection
    if (name === 'cpuModel') {
      setFormData(prev => ({
        ...prev,
        [name]: value,
        // Reset dependent fields when CPU changes
        location: '',
        bandwidth: '',
        storage: '',
        subnetSize: ''
      }));

      // Update filtered options based on the selected CPU
      updateFilteredOptions(value, serverOptions.configurations, serverOptions);
    }
    // Handle VPS-specific fields
    else if (['vpsConfiguration', 'vpsIpPrice', 'vpsLocation', 'vpsApplication'].includes(name)) {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));

      // Update VPS prices when configuration or IP option changes
      if (['vpsConfiguration', 'vpsIpPrice'].includes(name) && !priceOverride) {
        // Use setTimeout to ensure the form state is updated before calculating price
        setTimeout(() => updateVpsPrices(), 0);
      }
    }
    // Handle IP Transit-specific fields
    else if (['transitConfiguration', 'transitLocation', 'transitAdditionalIps'].includes(name)) {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));

      // Update IP Transit prices when configuration or additional IPs change
      if ((name === 'transitConfiguration' || name === 'transitAdditionalIps') && !priceOverride) {
        // Use setTimeout to ensure the form state is updated before calculating price
        setTimeout(() => updateTransitPrices(), 0);
      }
    }
    // Handle Colocation-specific fields
    else if (['rackUnits', 'kw', 'colocationBandwidth', 'colocationAdditionalIps', 'colocationLocation'].includes(name)) {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));

      // Update Colocation prices when rack units, bandwidth, additional IPs, or kW changes
      if (['rackUnits', 'kw', 'colocationBandwidth', 'colocationAdditionalIps'].includes(name) && !priceOverride) {
        // Use setTimeout to ensure the form state is updated before calculating price
        setTimeout(() => updateColocationPrices(), 0);
      }
    }
    else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));

      // Update price for configuration fields only if price override is not enabled
      if (!priceOverride && ['bandwidth', 'location', 'storage', 'subnetSize'].includes(name)) {
        // Pass the current form values plus the new value being changed
        // This ensures we're using the most up-to-date values for calculation
        updatePriceFromConfiguration(
          formData.cpuModel,
          name === 'location' ? value : formData.location,
          name === 'bandwidth' ? value : formData.bandwidth,
          name === 'storage' ? value : formData.storage,
          name === 'subnetSize' ? value : formData.subnetSize
        );
      }
    }

    // Clear validation error for this field
    if (validationErrors[name]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle price override toggle
  const handlePriceOverrideToggle = () => {
    setPriceOverride(!priceOverride);

    // If turning off price override, recalculate prices based on current configuration
    if (priceOverride) {
      if (activeTab === 'dedicated') {
        // Pass all current form values to ensure accurate calculation
        updatePriceFromConfiguration(
          formData.cpuModel,
          formData.location,
          formData.bandwidth,
          formData.storage,
          formData.subnetSize
        );
      } else if (activeTab === 'vps') {
        // Recalculate VPS prices
        updateVpsPrices();
      }
    }
  };

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
    setCustomerSelected(false); // Reset the selected flag when user types
    setFormData(prev => ({
      ...prev,
      customerName: e.target.value,
      customer_id: ''
    }));

    // Clear validation error when user starts typing again
    if (validationErrors.customerName) {
      setValidationErrors(prev => ({ ...prev, customerName: '' }));
    }
  };

  // Handle client selection from dropdown
  const handleSelectClient = (client) => {
    // Make sure we have a valid client object
    if (!client || !client.id) {
      console.error('Invalid client object:', client);
      return;
    }

    // Ensure client.id is converted to a string to avoid type issues
    // The API expects a numeric ID, so remove any non-numeric characters
    const rawId = client.id.toString();
    const customerId = rawId.replace(/\D/g, '');

    console.log('Selected client:', client);
    console.log('Raw ID:', rawId);
    console.log('Cleaned customer_id:', customerId);

    if (!customerId) {
      console.error('Failed to extract a valid customer ID');
      alert('Could not extract a valid customer ID. Please select a different customer.');
      return;
    }

    setFormData(prev => ({
      ...prev,
      customerName: client.company_name || `${client.first_name} ${client.last_name}`,
      customer_id: customerId,
      clientEmail: client.email || ''
    }));
    setSearchQuery(client.company_name || `${client.first_name} ${client.last_name}`);
    setShowSearchResults(false);
    setCustomerSelected(true);

    // Create a reference to the active element before we try to blur it
    const activeElement = document.activeElement;

    // Force blur on the active element if it exists
    if (activeElement && typeof activeElement.blur === 'function') {
      activeElement.blur();
    }
  };

  // Helper functions to set default prices for each tab
  const setDefaultDedicatedPrice = () => {
    // Always set a default price based on the first available CPU
    if (serverOptions.cpus.length > 0 && serverOptions.configurations.length > 0) {
      // Find first available CPU with configuration
      const configuredCpuIds = serverOptions.configurations.map(config => config.cpu_id);
      const availableCpus = serverOptions.cpus.filter(cpu => configuredCpuIds.includes(cpu.id));

      if (availableCpus.length > 0) {
        const defaultCpuId = availableCpus[0].id.toString();

        // Find default values for this CPU
        const cpuConfig = serverOptions.configurations.find(config => config.cpu_id.toString() === defaultCpuId);

        if (cpuConfig) {
          // Get CPU price
          const cpuPrice = parseFloat(cpuConfig.price || 0);

          // Parse configuration options
          const bandwidthIds = cpuConfig.bandwidths ? cpuConfig.bandwidths.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id)) : [];
          const storageIds = cpuConfig.storages ? cpuConfig.storages.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id)) : [];
          const subnetIds = cpuConfig.subnets ? cpuConfig.subnets.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id)) : [];

          // Get first bandwidth option if available
          let bandwidthPrice = 0;
          if (bandwidthIds.length > 0) {
            const defaultBandwidth = serverOptions.bandwidths.find(b => b.id === bandwidthIds[0]);
            if (defaultBandwidth && defaultBandwidth.price) {
              bandwidthPrice = parseFloat(defaultBandwidth.price);
            }
          }

          // Get first storage option if available
          let storagePrice = 0;
          if (storageIds.length > 0) {
            const defaultStorage = serverOptions.storages.find(s => s.id === storageIds[0]);
            if (defaultStorage && defaultStorage.price) {
              storagePrice = parseFloat(defaultStorage.price);
            }
          }

          // Get first subnet option if available
          let subnetPrice = 0;
          if (subnetIds.length > 0) {
            const defaultSubnet = serverOptions.subnets.find(s => s.id === subnetIds[0]);
            if (defaultSubnet && defaultSubnet.price) {
              subnetPrice = parseFloat(defaultSubnet.price);
            }
          }

          // Calculate total price
          const totalPrice = cpuPrice + bandwidthPrice + storagePrice + subnetPrice;

          // Update the form data with calculated prices
          setFormData(prev => ({
            ...prev,
            initialPrice: totalPrice.toFixed(2),
            recurringPrice: totalPrice.toFixed(2)
          }));
          return;
        }
      }
    }

    // Fallback to a default price if no options available
    setFormData(prev => ({
      ...prev,
      initialPrice: "300.00",
      recurringPrice: "300.00"
    }));
  };

  const setDefaultVpsPrice = () => {
    // Always set a default price based on the first available configuration
    if (vpsOptions.configurations.length > 0 && vpsOptions.ipPrices.length > 0) {
      const configPrice = parseFloat(vpsOptions.configurations[0].price || 0);
      const ipPrice = parseFloat(vpsOptions.ipPrices[0].price || 0);
      const totalPrice = configPrice + ipPrice;

      // Store the price components for display in the price breakdown
      const priceComponents = {
        configPrice: configPrice,
        ipPrice: ipPrice
      };

      setFormData(prev => ({
        ...prev,
        initialPrice: totalPrice.toFixed(2),
        recurringPrice: totalPrice.toFixed(2),
        priceComponents: priceComponents // Store price components for UI display
      }));
    } else {
      // Fallback to a default price
      setFormData(prev => ({
        ...prev,
        initialPrice: "15.00",
        recurringPrice: "15.00",
        priceComponents: {
          configPrice: 10.00,
          ipPrice: 5.00
        }
      }));
    }
  };

  const setDefaultTransitPrice = () => {
    // Always set a default price based on the first available configuration
    if (transitOptions.configurations.length > 0) {
      const configPrice = parseFloat(transitOptions.configurations[0].price || 0);
      const additionalIpsPrice = transitOptions.additionalIps && transitOptions.additionalIps.length > 0 ?
        parseFloat(transitOptions.additionalIps[0].price || 0) : 0;
      const totalPrice = configPrice + additionalIpsPrice;

      setFormData(prev => ({
        ...prev,
        initialPrice: totalPrice.toFixed(2),
        recurringPrice: totalPrice.toFixed(2)
      }));
    } else {
      // Fallback to a default price
      setFormData(prev => ({
        ...prev,
        initialPrice: "100.00",
        recurringPrice: "100.00"
      }));
    }
  };

  const setDefaultColocationPrice = () => {
    // Always calculate a default price based on the first available options
    const rackUnitPrice = colocationOptions.rackUnitOptions.length > 0 ?
      parseFloat(colocationOptions.rackUnitOptions[0].price || 0) : 300.00;

    const bandwidthPrice = colocationOptions.bandwidthOptions.length > 0 ?
      parseFloat(colocationOptions.bandwidthOptions[0].price || 0) : 100.00;

    // Default kW value is 1
    const kwPrice = 0;

    // Add additional IPs price if available
    const additionalIpsPrice = colocationOptions.additionalIps && colocationOptions.additionalIps.length > 0 ?
      parseFloat(colocationOptions.additionalIps[0].price || 0) : 0;

    const totalPrice = rackUnitPrice + bandwidthPrice + kwPrice + additionalIpsPrice;

    setFormData(prev => ({
      ...prev,
      initialPrice: totalPrice.toFixed(2),
      recurringPrice: totalPrice.toFixed(2)
    }));
  };

  // Handle tab change
  const handleTabChange = (tab) => {
    // Store the previous tab
    const previousTab = activeTab;

    // Update the active tab
    setActiveTab(tab);

    // Update order type based on selected tab
    const orderTypes = {
      dedicated: 'Dedicated Server',
      vps: 'VPS',
      transit: 'IP Transit',
      colocation: 'Colocation'
    };

    // If we're switching to a different tab, always update the price
    if (tab !== previousTab) {
      // First update the order type
      setFormData(prev => ({
        ...prev,
        orderType: orderTypes[tab] || 'Dedicated Server'
      }));

      // Then update the price based on the new tab's default values
      // We do this even if price override is enabled, to ensure the price is always updated
      setTimeout(() => {
        // Temporarily disable price override for the calculation
        const wasOverrideEnabled = priceOverride;

        // Set default prices based on the tab we're switching to
        if (tab === 'dedicated') {
          setDefaultDedicatedPrice();
        } else if (tab === 'vps') {
          setDefaultVpsPrice();
        } else if (tab === 'transit') {
          setDefaultTransitPrice();
        } else if (tab === 'colocation') {
          setDefaultColocationPrice();
        }

        // If price override was enabled, we need to re-enable it
        // but we've already updated the price
        if (wasOverrideEnabled) {
          console.log('Price override was enabled, but we updated the price anyway');
        }
      }, 100); // Small delay to ensure state updates have completed
    } else {
      // If we're not switching tabs, just update the order type
      setFormData(prev => ({
        ...prev,
        orderType: orderTypes[tab] || 'Dedicated Server'
      }));
    }
  };





  // Toggle price breakdown expansion
  const togglePriceBreakdown = () => {
    setIsPriceBreakdownExpanded(!isPriceBreakdownExpanded);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-[1050px] overflow-hidden">
        {/* Header */}
        <div className="bg-gray-50 px-4 py-2 border-b flex justify-between items-center">
          <h2 className="text-lg font-bold text-gray-800">
            {multiItemMode ? "Create Multi-Device Order" : "Create Order"}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors"
          >
            <XIcon className="w-5 h-5" />
          </button>
        </div>

        {/* Tab Navigation */}
        <div className="border-b">
          <div className="flex">
            <button
              className={`px-3 py-2 font-medium text-xs ${activeTab === 'dedicated' ? 'text-indigo-700 border-b-2 border-indigo-700' : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => handleTabChange('dedicated')}
            >
              <Server className="w-3 h-3 inline mr-1" />
              Dedicated
            </button>
            <button
              className={`px-3 py-2 font-medium text-xs ${activeTab === 'vps' ? 'text-indigo-700 border-b-2 border-indigo-700' : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => handleTabChange('vps')}
            >
              <Package className="w-3 h-3 inline mr-1" />
              VPS
            </button>
            <button
              className={`px-3 py-2 font-medium text-xs ${activeTab === 'transit' ? 'text-indigo-700 border-b-2 border-indigo-700' : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => handleTabChange('transit')}
            >
              <Activity className="w-3 h-3 inline mr-1" />
              IP Transit
            </button>
            <button
              className={`px-3 py-2 font-medium text-xs ${activeTab === 'colocation' ? 'text-indigo-700 border-b-2 border-indigo-700' : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => handleTabChange('colocation')}
            >
              <Network className="w-3 h-3 inline mr-1" />
              Colocation
            </button>
          </div>
        </div>

        {/* Body */}
        <div className="p-3 max-h-[75vh] overflow-y-auto">
          <div className="space-y-4">
            {/* Customer Information Section - Always visible */}
            <div className="bg-gray-50 p-3 rounded-md">
              <h3 className="text-md font-medium mb-2">Customer Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {/* Customer Search */}
                <div className="relative">
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    Client/Company <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <User className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                    <input
                      type="text"
                      placeholder="Search clients..."
                      className={`pl-7 pr-3 py-1.5 text-sm border rounded-md w-full ${
                        validationErrors.customerName ? 'border-red-500' : 'border-gray-300'
                      }`}
                      value={searchQuery}
                      onChange={handleSearchChange}
                      onFocus={() => {
                        if (searchQuery.length >= 2 && !customerSelected) {
                          setShowSearchResults(true);
                        }
                      }}
                      onBlur={() => {
                        // Delay hiding results to allow for clicking
                        setTimeout(() => setShowSearchResults(false), 200);
                      }}
                    />
                    {isSearching && (
                      <div className="absolute right-2 top-1/2 -translate-y-1/2">
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-indigo-700"></div>
                      </div>
                    )}
                  </div>

                  {validationErrors.customerName && (
                    <p className="text-red-500 text-xs mt-0.5">{validationErrors.customerName}</p>
                  )}

                  {/* Display selected customer info */}
                  {customerSelected && formData.customer_id && (
                    <div className="mt-0.5 text-xs text-gray-600">
                      ID: {formData.customer_id}
                      {formData.clientEmail && ` • ${formData.clientEmail}`}
                    </div>
                  )}

                  {/* Search Results Dropdown */}
                  {showSearchResults && searchResults.length > 0 && (
                    <div className="absolute z-10 w-full mt-0.5 bg-white shadow-lg rounded-md border border-gray-300 max-h-40 overflow-y-auto">
                      {searchResults.map((client) => (
                        <div
                          key={client.id}
                          className="px-2 py-1.5 hover:bg-gray-100 cursor-pointer text-sm"
                          onClick={() => handleSelectClient(client)}
                        >
                          <div className="font-medium">{client.company_name || `${client.first_name} ${client.last_name}`}</div>
                          <div className="text-xs text-gray-500">
                            ID: {client.id} {client.email && `• ${client.email}`}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Order Name */}
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    Order Note <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <Package className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                    <input
                      type="text"
                      name="orderNote"
                      placeholder="e.g. Multiple Server Order"
                      className={`pl-7 pr-3 py-1.5 text-sm border rounded-md w-full ${
                        validationErrors.orderNote ? 'border-red-500' : 'border-gray-300'
                      }`}
                      value={formData.orderNote}
                      onChange={handleChange}
                    />
                  </div>
                  {validationErrors.orderNote && (
                    <p className="text-red-500 text-xs mt-0.5">{validationErrors.orderNote}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Device Configuration Section */}
            <div className="bg-gray-50 p-3 rounded-md">
              <h3 className="text-md font-medium mb-2">
                {editingDeviceIndex !== null ? 'Edit Device' : 'Add Device'}
              </h3>

              {/* Dedicated Server Tab */}
              {activeTab === 'dedicated' && (
                <div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    {/* CPU Model */}
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        CPU Model <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <Cpu className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                        <select
                          name="cpuModel"
                          className={`pl-7 pr-3 py-1.5 text-sm border rounded-md w-full appearance-none ${
                            validationErrors.cpuModel ? 'border-red-500' : 'border-gray-300'
                          }`}
                          value={formData.cpuModel}
                          onChange={handleChange}
                        >
                          <option value="">Select CPU Model</option>
                          {serverOptions.cpus
                            .filter(cpu => serverOptions.configurations
                              .some(config => config.cpu_id === cpu.id))
                            .map(cpu => (
                              <option key={cpu.id} value={cpu.id.toString()}>
                                {cpu.cpu} {cpu.price ? `(€${parseFloat(cpu.price).toFixed(2)})` : ''}
                              </option>
                            ))
                          }
                        </select>
                      </div>
                      {validationErrors.cpuModel && (
                        <p className="text-red-500 text-xs mt-0.5">{validationErrors.cpuModel}</p>
                      )}
                    </div>

                    {/* Location */}
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Location <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <Map className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                        <select
                          name="location"
                          className={`pl-7 pr-3 py-1.5 text-sm border rounded-md w-full appearance-none ${
                            validationErrors.location ? 'border-red-500' : 'border-gray-300'
                          }`}
                          value={formData.location}
                          onChange={handleChange}
                          disabled={!formData.cpuModel || filteredOptions.cities.length === 0}
                        >
                          <option value="">Select Location</option>
                          {filteredOptions.cities.map(city => (
                            <option key={city.id} value={city.id.toString()}>
                              {city.name} - {city.datacenter}
                            </option>
                          ))}
                        </select>
                      </div>
                      {validationErrors.location && (
                        <p className="text-red-500 text-xs mt-0.5">{validationErrors.location}</p>
                      )}
                    </div>

                    {/* Operating System */}


                    {/* Bandwidth */}
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Bandwidth <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <Activity className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                        <select
                          name="bandwidth"
                          className={`pl-7 pr-3 py-1.5 text-sm border rounded-md w-full appearance-none ${
                            validationErrors.bandwidth ? 'border-red-500' : 'border-gray-300'
                          }`}
                          value={formData.bandwidth}
                          onChange={handleChange}
                          disabled={!formData.cpuModel || filteredOptions.bandwidths.length === 0}
                        >
                          <option value="">Select Bandwidth</option>
                          {filteredOptions.bandwidths.map(bandwidth => (
                            <option key={bandwidth.id} value={bandwidth.id.toString()}>
                              {bandwidth.name} {bandwidth.price > 0 ? `+€${bandwidth.price}` : ''}
                            </option>
                          ))}
                        </select>
                      </div>
                      {validationErrors.bandwidth && (
                        <p className="text-red-500 text-xs mt-0.5">{validationErrors.bandwidth}</p>
                      )}
                    </div>

                    {/* Storage */}
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Storage <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <HardDrive className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                        <select
                          name="storage"
                          className={`pl-7 pr-3 py-1.5 text-sm border rounded-md w-full appearance-none ${
                            validationErrors.storage ? 'border-red-500' : 'border-gray-300'
                          }`}
                          value={formData.storage}
                          onChange={handleChange}
                          disabled={!formData.cpuModel || filteredOptions.storages.length === 0}
                        >
                          <option value="">Select Storage</option>
                          {filteredOptions.storages.map(storage => (
                            <option key={storage.id} value={storage.id.toString()}>
                              {storage.name} {storage.price > 0 ? `(+€${storage.price})` : ''}
                            </option>
                          ))}
                        </select>
                      </div>
                      {validationErrors.storage && (
                        <p className="text-red-500 text-xs mt-0.5">{validationErrors.storage}</p>
                      )}
                    </div>

                    {/* Subnet Size */}
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Subnet Size <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <Network className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                        <select
                          name="subnetSize"
                          className={`pl-7 pr-3 py-1.5 text-sm border rounded-md w-full appearance-none ${
                            validationErrors.subnetSize ? 'border-red-500' : 'border-gray-300'
                          }`}
                          value={formData.subnetSize}
                          onChange={handleChange}
                          disabled={!formData.cpuModel || filteredOptions.subnets.length === 0}
                        >
                          <option value="">Select Subnet Size</option>
                          {filteredOptions.subnets.map(subnet => (
                            <option key={subnet.id} value={subnet.id.toString()}>
                              {subnet.name} {subnet.price > 0 ? `(+€${subnet.price})` : ''}
                            </option>
                          ))}
                        </select>
                      </div>
                      {validationErrors.subnetSize && (
                        <p className="text-red-500 text-xs mt-0.5">{validationErrors.subnetSize}</p>
                      )}
                    </div>


                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Operating System <span className="text-red-500">*</span>
                      </label>
                      <div className="flex items-center space-x-1">
                        <div className="relative flex-grow">
                          <Globe className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                          <select
                            name="operatingSystem"
                            className={`pl-7 pr-3 py-1.5 text-sm border rounded-md w-full appearance-none ${
                              validationErrors.operatingSystem ? 'border-red-500' : 'border-gray-300'
                            }`}
                            value={formData.operatingSystem}
                            onChange={handleChange}
                          >
                            <option value="">Select OS</option>
                            {serverOptions.operating_systems.map(os => (
                              <option key={os.id} value={os.id.toString()}>{os.name}</option>
                            ))}
                          </select>
                        </div>
                        <button
                          type="button"
                          onClick={() => setShowAddOsModal(true)}
                          className="p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center flex-shrink-0"
                          title="Add New Operating System"
                        >
                          <Plus className="w-3 h-3" />
                        </button>
                      </div>
                      {validationErrors.operatingSystem && (
                        <p className="text-red-500 text-xs mt-0.5">{validationErrors.operatingSystem}</p>
                      )}
                    </div>
                  </div>



                  {/* Device Price */}
                  <div className="mt-3">
                    {/* Price Breakdown - Collapsible with toggle */}
                    {!priceOverride && activeTab === 'dedicated' && formData.cpuModel && (
                      <div className="mb-3 bg-gray-50 p-2 rounded-md border border-gray-200">
                        <div
                          onClick={togglePriceBreakdown}
                          className="flex justify-between items-center cursor-pointer"
                        >
                          <h4 className="text-xs font-medium text-gray-700">Price Breakdown</h4>
                          <button
                            type="button"
                            className="text-gray-500 focus:outline-none"
                          >
                            {isPriceBreakdownExpanded ? (
                              <ChevronUp className="w-4 h-4" />
                            ) : (
                              <ChevronDown className="w-4 h-4" />
                            )}
                          </button>
                        </div>

                        {isPriceBreakdownExpanded && (
                          <div className="space-y-1 text-xs mt-2">
                            {/* CPU Price */}
                            <div className="flex justify-between">
                              <span>CPU ({serverOptions.cpus.find(cpu => cpu.id.toString() === formData.cpuModel)?.cpu || 'Selected CPU'})</span>
                              <span className="font-medium">
                                €{formData.priceComponents?.cpuPrice?.toFixed(2) || serverOptions.cpus.find(cpu => cpu.id.toString() === formData.cpuModel)?.price || serverOptions.configurations.find(config => config.cpu_id.toString() === formData.cpuModel)?.price || '0.00'}
                              </span>
                            </div>

                            {/* Bandwidth Price */}
                            {formData.bandwidth && (
                              <div className="flex justify-between">
                                <span>Bandwidth ({serverOptions.bandwidths.find(b => b.id.toString() === formData.bandwidth)?.name || 'Selected Bandwidth'})</span>
                                <span className="font-medium">
                                  €{formData.priceComponents?.bandwidthPrice?.toFixed(2) || serverOptions.bandwidths.find(b => b.id.toString() === formData.bandwidth)?.price || '0.00'}
                                </span>
                              </div>
                            )}

                            {/* Storage Price */}
                            {formData.storage && (
                              <div className="flex justify-between">
                                <span>Storage ({serverOptions.storages.find(s => s.id.toString() === formData.storage)?.name || 'Selected Storage'})</span>
                                <span className="font-medium">
                                  €{formData.priceComponents?.storagePrice?.toFixed(2) || serverOptions.storages.find(s => s.id.toString() === formData.storage)?.price || '0.00'}
                                </span>
                              </div>
                            )}

                            {/* Subnet Price */}
                            {formData.subnetSize && (
                              <div className="flex justify-between">
                                <span>Subnet ({serverOptions.subnets.find(s => s.id.toString() === formData.subnetSize)?.name || 'Selected Subnet'})</span>
                                <span className="font-medium">
                                  €{formData.priceComponents?.subnetPrice?.toFixed(2) || serverOptions.subnets.find(s => s.id.toString() === formData.subnetSize)?.price || '0.00'}
                                </span>
                              </div>
                            )}

                            <div className="border-t border-gray-200 pt-1 mt-1 flex justify-between font-medium">
                              <span>Total</span>
                              <span>€{parseFloat(formData.initialPrice).toFixed(2)}</span>
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Price input fields */}
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Initial Price
                        </label>
                        <div className="relative">
                          <DollarSign className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                          <input
                            type="text"
                            name="initialPrice"
                            className={`pl-7 pr-3 py-1.5 text-sm border rounded-md w-full font-medium ${priceOverride ? 'border-indigo-300 bg-white' : 'border-gray-300 bg-gray-50'}`}
                            value={priceOverride ? `€${formData.initialPrice}` : `€${parseFloat(formData.initialPrice).toFixed(2)}`}
                            onChange={handleChange}
                            readOnly={!priceOverride}
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Monthly Recurring
                        </label>
                        <div className="relative">
                          <DollarSign className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                          <input
                            type="text"
                            name="recurringPrice"
                            className={`pl-7 pr-3 py-1.5 text-sm border rounded-md w-full font-medium ${priceOverride ? 'border-indigo-300 bg-white' : 'border-gray-300 bg-gray-50'}`}
                            value={priceOverride ? `€${formData.recurringPrice}` : `€${parseFloat(formData.recurringPrice).toFixed(2)}`}
                            onChange={handleChange}
                            readOnly={!priceOverride}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-2 flex items-center justify-between">
                    <div className="flex items-center">
                      <span className="text-xs text-gray-600 mr-2">Override Price</span>
                      <button
                        type="button"
                        onClick={handlePriceOverrideToggle}
                        className={`relative inline-flex h-5 w-10 items-center rounded-full ${priceOverride ? 'bg-indigo-600' : 'bg-gray-200'}`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${priceOverride ? 'translate-x-5' : 'translate-x-1'}`}
                        />
                      </button>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-2">
                    {!multiItemMode && (
                    <button
                      type="button"
                      onClick={createSingleOrder}
                      className="px-4 py-1.5 text-sm bg-indigo-700 text-white rounded-md hover:bg-indigo-800 flex items-center"
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <>
                          <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1.5"></div>
                          Processing...
                        </>
                      ) : (
                        <>
                          <Plus className="w-3 h-3 mr-1" />
                          Create Order
                        </>
                      )}
                    </button>
                  )}

                      {multiItemMode && (
                        <button
                          type="button"
                          onClick={addDeviceToList}
                          className="px-4 py-1.5 text-sm bg-indigo-700 text-white rounded-md hover:bg-indigo-800 flex items-center"
                        >
                          {editingDeviceIndex !== null ? (
                            <>
                              <RefreshCw className="w-3 h-3 mr-1" />
                              Update Device
                            </>
                          ) : (
                            <>
                              <Plus className="w-3 h-3 mr-1" />
                              Add To Order
                            </>
                          )}
                        </button>
                      )}

                      {!multiItemMode && (
                        <button
                          type="button"
                          onClick={enableMultiItemMode}
                          className="px-4 py-1.5 text-sm border border-indigo-700 text-indigo-700 rounded-md hover:bg-indigo-50 flex items-center"
                          title="Add multiple items to this order"
                        >
                          <List className="w-3 h-3 mr-1" />
                          Add Multiple
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* VPS Tab */}
              {activeTab === 'vps' && (
                <div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    {/* VPS Configuration */}
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        VPS Configuration <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <Cpu className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                        <select
                          name="vpsConfiguration"
                          className={`pl-7 pr-3 py-1.5 text-sm border rounded-md w-full appearance-none ${
                            validationErrors.vpsConfiguration ? 'border-red-500' : 'border-gray-300'
                          }`}
                          value={formData.vpsConfiguration}
                          onChange={handleChange}
                        >
                          <option value="">Select Configuration</option>
                          {vpsOptions.configurations.map(config => (
                            <option key={config.id} value={config.id.toString()}>
                              {config.label} (€{parseFloat(config.price).toFixed(2)})
                            </option>
                          ))}
                        </select>
                      </div>
                      {validationErrors.vpsConfiguration && (
                        <p className="text-red-500 text-xs mt-0.5">{validationErrors.vpsConfiguration}</p>
                      )}
                    </div>

                    {/* Location */}
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Location <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <Map className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                        <select
                          name="vpsLocation"
                          className={`pl-7 pr-3 py-1.5 text-sm border rounded-md w-full appearance-none ${
                            validationErrors.vpsLocation ? 'border-red-500' : 'border-gray-300'
                          }`}
                          value={formData.vpsLocation}
                          onChange={handleChange}
                        >
                          <option value="">Select Location</option>
                          {vpsOptions.cities.map(city => (
                            <option key={city.id} value={city.id.toString()}>
                              {city.name} - {city.datacenter}
                            </option>
                          ))}
                        </select>
                      </div>
                      {validationErrors.vpsLocation && (
                        <p className="text-red-500 text-xs mt-0.5">{validationErrors.vpsLocation}</p>
                      )}
                    </div>

                    {/* IP Option */}
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        IP Option <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <Globe className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                        <select
                          name="vpsIpPrice"
                          className={`pl-7 pr-3 py-1.5 text-sm border rounded-md w-full appearance-none ${
                            validationErrors.vpsIpPrice ? 'border-red-500' : 'border-gray-300'
                          }`}
                          value={formData.vpsIpPrice}
                          onChange={handleChange}
                        >
                          <option value="">Select IP Option</option>
                          {vpsOptions.ipPrices.map(ip => (
                            <option key={ip.id} value={ip.id.toString()}>
                              {ip.label} (+€{parseFloat(ip.price).toFixed(2)})
                            </option>
                          ))}
                        </select>
                      </div>
                      {validationErrors.vpsIpPrice && (
                        <p className="text-red-500 text-xs mt-0.5">{validationErrors.vpsIpPrice}</p>
                      )}
                    </div>

                    {/* Application */}
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Application
                      </label>
                      <div className="relative">
                        <Package className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                        <select
                          name="vpsApplication"
                          className="pl-7 pr-3 py-1.5 text-sm border border-gray-300 rounded-md w-full appearance-none"
                          value={formData.vpsApplication}
                          onChange={handleChange}
                        >
                          <option value="">Select Application (Optional)</option>
                          {vpsOptions.applications.map(app => (
                            <option key={app.id} value={app.id.toString()}>
                              {app.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* Price Breakdown */}
                  {!priceOverride && activeTab === 'vps' && formData.vpsConfiguration && (
                    <div className="mt-3 bg-gray-50 p-2 rounded-md border border-gray-200">
                      <div
                        onClick={togglePriceBreakdown}
                        className="flex justify-between items-center cursor-pointer"
                      >
                        <h4 className="text-xs font-medium text-gray-700">Price Breakdown</h4>
                        <button
                          type="button"
                          className="text-gray-500 focus:outline-none"
                        >
                          {isPriceBreakdownExpanded ? (
                            <ChevronUp className="w-4 h-4" />
                          ) : (
                            <ChevronDown className="w-4 h-4" />
                          )}
                        </button>
                      </div>

                      {isPriceBreakdownExpanded && (
                        <div className="space-y-1 text-xs mt-2">
                          {/* VPS Configuration Price */}
                          {formData.vpsConfiguration && (
                            <div className="flex justify-between">
                              <span>VPS Configuration ({vpsOptions.configurations.find(c => c.id.toString() === formData.vpsConfiguration)?.label || 'Selected Configuration'})</span>
                              <span className="font-medium">
                                €{formData.priceComponents?.configPrice?.toFixed(2) || vpsOptions.configurations.find(c => c.id.toString() === formData.vpsConfiguration)?.price || '0.00'}
                              </span>
                            </div>
                          )}

                          {/* IP Price */}
                          {formData.vpsIpPrice && (
                            <div className="flex justify-between">
                              <span>IP Option ({vpsOptions.ipPrices.find(ip => ip.id.toString() === formData.vpsIpPrice)?.label || 'Selected IP Option'})</span>
                              <span className="font-medium">
                                €{formData.priceComponents?.ipPrice?.toFixed(2) || vpsOptions.ipPrices.find(ip => ip.id.toString() === formData.vpsIpPrice)?.price || '0.00'}
                              </span>
                            </div>
                          )}

                          <div className="border-t border-gray-200 pt-1 mt-1 flex justify-between font-medium">
                            <span>Total</span>
                            <span>€{parseFloat(formData.initialPrice).toFixed(2)}</span>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Price Display */}
                  <div className="mt-4">
                    {/* Price Breakdown - Collapsible with toggle */}


                    {/* Price input fields */}
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Initial Price
                        </label>
                        <div className="relative">
                          <DollarSign className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                          <input
                            type="text"
                            name="initialPrice"
                            className={`pl-7 pr-3 py-1.5 text-sm border rounded-md w-full font-medium ${priceOverride ? 'border-indigo-300 bg-white' : 'border-gray-300 bg-gray-50'}`}
                            value={priceOverride ? `€${formData.initialPrice}` : `€${parseFloat(formData.initialPrice).toFixed(2)}`}
                            onChange={handleChange}
                            readOnly={!priceOverride}
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Monthly Recurring
                        </label>
                        <div className="relative">
                          <DollarSign className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                          <input
                            type="text"
                            name="recurringPrice"
                            className={`pl-7 pr-3 py-1.5 text-sm border rounded-md w-full font-medium ${priceOverride ? 'border-indigo-300 bg-white' : 'border-gray-300 bg-gray-50'}`}
                            value={priceOverride ? `€${formData.recurringPrice}` : `€${parseFloat(formData.recurringPrice).toFixed(2)}`}
                            onChange={handleChange}
                            readOnly={!priceOverride}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="mt-4 flex justify-between">
                    {/* Price Override Toggle */}
                    <div className="flex items-center">
                      <span className="text-xs text-gray-600 mr-2">Override Price</span>
                      <button
                        type="button"
                        onClick={handlePriceOverrideToggle}
                        className={`relative inline-flex h-5 w-10 items-center rounded-full ${priceOverride ? 'bg-indigo-600' : 'bg-gray-200'}`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${priceOverride ? 'translate-x-5' : 'translate-x-1'}`}
                        />
                      </button>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-2">
                      {/* Create Order Button */}
                      {!multiItemMode && (
                        <button
                          type="button"
                          onClick={createSingleOrder}
                          className="px-4 py-1.5 text-sm bg-indigo-700 text-white rounded-md hover:bg-indigo-800 flex items-center"
                          disabled={isLoading}
                        >
                          {isLoading ? (
                            <>
                              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1.5"></div>
                              Processing...
                            </>
                          ) : (
                            <>
                              <Plus className="w-3 h-3 mr-1" />
                              Create Order
                            </>
                          )}
                        </button>
                      )}

                      {/* Add to Order Button (for multi-item mode) */}
                      {multiItemMode && (
                        <button
                          type="button"
                          onClick={addDeviceToList}
                          className="px-4 py-1.5 text-sm bg-indigo-700 text-white rounded-md hover:bg-indigo-800 flex items-center"
                        >
                          {editingDeviceIndex !== null ? (
                            <>
                              <Edit2 className="w-3 h-3 mr-1" />
                              Update Device
                            </>
                          ) : (
                            <>
                              <Plus className="w-3 h-3 mr-1" />
                              Add To Order
                            </>
                          )}
                        </button>
                      )}

                      {/* Enable Multi-Item Mode Button */}
                      {!multiItemMode && !editingDeviceIndex && (
                        <button
                          type="button"
                          onClick={enableMultiItemMode}
                          className="px-4 py-1.5 text-sm border border-indigo-700 text-indigo-700 rounded-md hover:bg-indigo-50 flex items-center"
                          title="Add multiple items to this order"
                        >
                          <List className="w-3 h-3 mr-1" />
                          Add Multiple
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* IP Transit Tab */}
              {activeTab === 'transit' && (
                <div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    {/* IP Transit Configuration */}
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Bandwidth <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <Activity className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                        <select
                          name="transitConfiguration"
                          className={`pl-7 pr-3 py-1.5 text-sm border rounded-md w-full appearance-none ${
                            validationErrors.transitConfiguration ? 'border-red-500' : 'border-gray-300'
                          }`}
                          value={formData.transitConfiguration}
                          onChange={handleChange}
                        >
                          <option value="">Select Bandwidth</option>
                          {transitOptions.configurations.map(config => (
                            <option key={config.id} value={config.id.toString()}>
                              {config.label} (€{parseFloat(config.price).toFixed(2)})
                            </option>
                          ))}
                        </select>
                      </div>
                      {validationErrors.transitConfiguration && (
                        <p className="text-red-500 text-xs mt-0.5">{validationErrors.transitConfiguration}</p>
                      )}
                    </div>

                    {/* Location */}
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Location <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <Map className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                        <select
                          name="transitLocation"
                          className={`pl-7 pr-3 py-1.5 text-sm border rounded-md w-full appearance-none ${
                            validationErrors.transitLocation ? 'border-red-500' : 'border-gray-300'
                          }`}
                          value={formData.transitLocation}
                          onChange={handleChange}
                        >
                          <option value="">Select Location</option>
                          {transitOptions.cities.map(city => (
                            <option key={city.id} value={city.id.toString()}>
                              {city.name} {city.datacenter ? `(${city.datacenter})` : ''}
                            </option>
                          ))}
                        </select>
                      </div>
                      {validationErrors.transitLocation && (
                        <p className="text-red-500 text-xs mt-0.5">{validationErrors.transitLocation}</p>
                      )}
                    </div>

                    {/* Additional IPs */}
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Additional IPs
                      </label>
                      <div className="relative">
                        <Globe className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                        <select
                          name="transitAdditionalIps"
                          className="pl-7 pr-3 py-1.5 text-sm border border-gray-300 rounded-md w-full appearance-none"
                          value={formData.transitAdditionalIps}
                          onChange={handleChange}
                        >
                          <option value="">No Additional IPs</option>
                          {transitOptions.additionalIps && transitOptions.additionalIps.map(ips => (
                            <option key={ips.id} value={ips.id.toString()}>
                              {ips.label} (+€{parseFloat(ips.price).toFixed(2)})
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* Price Breakdown */}
                  {!priceOverride && formData.transitConfiguration && (
                    <div className="mt-3 bg-gray-50 p-2 rounded-md border border-gray-200">
                      <div
                        onClick={togglePriceBreakdown}
                        className="flex justify-between items-center cursor-pointer"
                      >
                        <h4 className="text-xs font-medium text-gray-700">Price Breakdown</h4>
                        <button
                          type="button"
                          className="text-gray-500 focus:outline-none"
                        >
                          {isPriceBreakdownExpanded ? (
                            <ChevronUp className="w-4 h-4" />
                          ) : (
                            <ChevronDown className="w-4 h-4" />
                          )}
                        </button>
                      </div>

                      {isPriceBreakdownExpanded && (
                        <div className="space-y-1 text-xs mt-2">
                          {/* Transit Configuration Price */}
                          {formData.transitConfiguration && (
                            <div className="flex justify-between">
                              <span>IP Transit ({transitOptions.configurations.find(c => c.id.toString() === formData.transitConfiguration)?.label || 'Selected Configuration'})</span>
                              <span className="font-medium">
                                €{transitOptions.configurations.find(c => c.id.toString() === formData.transitConfiguration)?.price || '0.00'}
                              </span>
                            </div>
                          )}

                          {/* Additional IPs Price */}
                          {formData.transitAdditionalIps && (
                            <div className="flex justify-between">
                              <span>Additional IPs ({transitOptions.additionalIps?.find(ips => ips.id.toString() === formData.transitAdditionalIps)?.label || 'Selected IPs'})</span>
                              <span className="font-medium">
                                €{transitOptions.additionalIps?.find(ips => ips.id.toString() === formData.transitAdditionalIps)?.price || '0.00'}
                              </span>
                            </div>
                          )}

                          <div className="border-t border-gray-200 pt-1 mt-1 flex justify-between font-medium">
                            <span>Total</span>
                            <span>€{parseFloat(formData.initialPrice).toFixed(2)}</span>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Price input fields */}
                  <div className="mt-3 grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Initial Price
                      </label>
                      <div className="relative">
                        <DollarSign className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                        <input
                          type="text"
                          name="initialPrice"
                          className={`pl-7 pr-3 py-1.5 text-sm border rounded-md w-full font-medium ${priceOverride ? 'border-indigo-300 bg-white' : 'border-gray-300 bg-gray-50'}`}
                          value={priceOverride ? `€${formData.initialPrice}` : `€${parseFloat(formData.initialPrice).toFixed(2)}`}
                          onChange={handleChange}
                          readOnly={!priceOverride}
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Monthly Recurring
                      </label>
                      <div className="relative">
                        <DollarSign className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                        <input
                          type="text"
                          name="recurringPrice"
                          className={`pl-7 pr-3 py-1.5 text-sm border rounded-md w-full font-medium ${priceOverride ? 'border-indigo-300 bg-white' : 'border-gray-300 bg-gray-50'}`}
                          value={priceOverride ? `€${formData.recurringPrice}` : `€${parseFloat(formData.recurringPrice).toFixed(2)}`}
                          onChange={handleChange}
                          readOnly={!priceOverride}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="mt-4 flex justify-between">
                    {/* Price Override Toggle */}
                    <div className="flex items-center">
                      <span className="text-xs text-gray-600 mr-2">Override Price</span>
                      <button
                        type="button"
                        onClick={handlePriceOverrideToggle}
                        className={`relative inline-flex h-5 w-10 items-center rounded-full ${priceOverride ? 'bg-indigo-600' : 'bg-gray-200'}`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${priceOverride ? 'translate-x-5' : 'translate-x-1'}`}
                        />
                      </button>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-2">
                      {/* Create Order Button */}
                      {!multiItemMode && (
                        <button
                          type="button"
                          onClick={createSingleOrder}
                          className="px-4 py-1.5 text-sm bg-indigo-700 text-white rounded-md hover:bg-indigo-800 flex items-center"
                          disabled={isLoading}
                        >
                          {isLoading ? (
                            <>
                              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1.5"></div>
                              Processing...
                            </>
                          ) : (
                            <>
                              <Plus className="w-3 h-3 mr-1" />
                              Create Order
                            </>
                          )}
                        </button>
                      )}

                      {/* Add to Order Button (for multi-item mode) */}
                      {multiItemMode && (
                        <button
                          type="button"
                          onClick={addDeviceToList}
                          className="px-4 py-1.5 text-sm bg-indigo-700 text-white rounded-md hover:bg-indigo-800 flex items-center"
                        >
                          {editingDeviceIndex !== null ? (
                            <>
                              <Edit2 className="w-3 h-3 mr-1" />
                              Update Device
                            </>
                          ) : (
                            <>
                              <Plus className="w-3 h-3 mr-1" />
                              Add To Order
                            </>
                          )}
                        </button>
                      )}

                      {/* Enable Multi-Item Mode Button */}
                      {!multiItemMode && !editingDeviceIndex && (
                        <button
                          type="button"
                          onClick={enableMultiItemMode}
                          className="px-4 py-1.5 text-sm border border-indigo-700 text-indigo-700 rounded-md hover:bg-indigo-50 flex items-center"
                          title="Add multiple items to this order"
                        >
                          <List className="w-3 h-3 mr-1" />
                          Add Multiple
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Colocation Tab */}
              {activeTab === 'colocation' && (
                <div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {/* Rack Units */}
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Rack Units <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <Network className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                        <select
                          name="rackUnits"
                          className={`pl-7 pr-3 py-1.5 text-sm border rounded-md w-full appearance-none ${
                            validationErrors.rackUnits ? 'border-red-500' : 'border-gray-300'
                          }`}
                          value={formData.rackUnits}
                          onChange={handleChange}
                        >
                          <option value="1">1U</option>
                          <option value="2">2U</option>
                          <option value="3">3U</option>
                          <option value="4">4U</option>
                          <option value="5">5U</option>
                          <option value="6">6U</option>
                          <option value="7">7U</option>
                          <option value="8">8U</option>
                          <option value="9">9U</option>
                          <option value="half">Half Rack</option>
                          <option value="full">Full Rack</option>
                        </select>
                      </div>
                      {validationErrors.rackUnits && (
                        <p className="text-red-500 text-xs mt-0.5">{validationErrors.rackUnits}</p>
                      )}
                    </div>

                    {/* kW */}
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        kW <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <Activity className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                        <input
                          type="number"
                          name="kw"
                          min="1"
                          step="0.5"
                          className={`pl-7 pr-3 py-1.5 text-sm border rounded-md w-full ${
                            validationErrors.kw ? 'border-red-500' : 'border-gray-300'
                          }`}
                          value={formData.kw}
                          onChange={handleChange}
                        />
                      </div>
                      {validationErrors.kw && (
                        <p className="text-red-500 text-xs mt-0.5">{validationErrors.kw}</p>
                      )}
                    </div>

                    {/* Bandwidth */}
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Bandwidth <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <Wifi className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                        <select
                          name="colocationBandwidth"
                          className={`pl-7 pr-3 py-1.5 text-sm border rounded-md w-full appearance-none ${
                            validationErrors.colocationBandwidth ? 'border-red-500' : 'border-gray-300'
                          }`}
                          value={formData.colocationBandwidth}
                          onChange={handleChange}
                        >
                          <option value="">Select Bandwidth</option>
                          {colocationOptions.bandwidthOptions.map(option => (
                            <option key={option.id} value={option.id}>
                              {option.label} (+€{parseFloat(option.price).toFixed(2)})
                            </option>
                          ))}
                        </select>
                      </div>
                      {validationErrors.colocationBandwidth && (
                        <p className="text-red-500 text-xs mt-0.5">{validationErrors.colocationBandwidth}</p>
                      )}
                    </div>

                    {/* Additional IPs */}
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Additional IPs
                      </label>
                      <div className="relative">
                        <Globe className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                        <select
                          name="colocationAdditionalIps"
                          className="pl-7 pr-3 py-1.5 text-sm border border-gray-300 rounded-md w-full appearance-none"
                          value={formData.colocationAdditionalIps}
                          onChange={handleChange}
                        >
                          <option value="">No Additional IPs</option>
                          {colocationOptions.additionalIps && colocationOptions.additionalIps.map(ips => (
                            <option key={ips.id} value={ips.id.toString()}>
                              {ips.label} (+€{parseFloat(ips.price).toFixed(2)})
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>

                    {/* Location */}
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Location <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <Map className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                        <select
                          name="colocationLocation"
                          className={`pl-7 pr-3 py-1.5 text-sm border rounded-md w-full appearance-none ${
                            validationErrors.colocationLocation ? 'border-red-500' : 'border-gray-300'
                          }`}
                          value={formData.colocationLocation}
                          onChange={handleChange}
                        >
                          <option value="">Select Location</option>
                          {colocationOptions.cities.map(city => (
                            <option key={city.id} value={city.id.toString()}>
                              {city.name} {city.datacenter ? `(${city.datacenter})` : ''}
                            </option>
                          ))}
                        </select>
                      </div>
                      {validationErrors.colocationLocation && (
                        <p className="text-red-500 text-xs mt-0.5">{validationErrors.colocationLocation}</p>
                      )}
                    </div>
                  </div>

                  {/* Price Breakdown */}
                  {!priceOverride && formData.rackUnits && (
                    <div className="mt-3 bg-gray-50 p-2 rounded-md border border-gray-200">
                      <div
                        onClick={togglePriceBreakdown}
                        className="flex justify-between items-center cursor-pointer"
                      >
                        <h4 className="text-xs font-medium text-gray-700">Price Breakdown</h4>
                        <button
                          type="button"
                          className="text-gray-500 focus:outline-none"
                        >
                          {isPriceBreakdownExpanded ? (
                            <ChevronUp className="w-4 h-4" />
                          ) : (
                            <ChevronDown className="w-4 h-4" />
                          )}
                        </button>
                      </div>

                      {isPriceBreakdownExpanded && (
                        <div className="space-y-1 text-xs mt-2">
                          {/* Rack Units Price */}
                          <div className="flex justify-between">
                            <span>Rack Units ({colocationOptions.rackUnitOptions.find(option => option.id === formData.rackUnits)?.label || formData.rackUnits + 'U'})</span>
                            <span className="font-medium">
                              €{colocationOptions.rackUnitOptions.find(option => option.id === formData.rackUnits)?.price || '0.00'}
                            </span>
                          </div>

                          {/* kW Price */}
                          <div className="flex justify-between">
                            <span>Power ({formData.kw} kW)</span>
                            <span className="font-medium">€{(parseFloat(formData.kw) * 50).toFixed(2)}</span>
                          </div>

                          {/* Bandwidth Price */}
                          <div className="flex justify-between">
                            <span>Bandwidth ({colocationOptions.bandwidthOptions.find(option => option.id === formData.colocationBandwidth)?.label || `${formData.colocationBandwidth} Gbps`})</span>
                            <span className="font-medium">
                              €{colocationOptions.bandwidthOptions.find(option => option.id === formData.colocationBandwidth)?.price || '0.00'}
                            </span>
                          </div>

                          {/* Additional IPs Price */}
                          {formData.colocationAdditionalIps && (
                            <div className="flex justify-between">
                              <span>Additional IPs ({colocationOptions.additionalIps?.find(ips => ips.id.toString() === formData.colocationAdditionalIps)?.label || 'Selected IPs'})</span>
                              <span className="font-medium">
                                €{colocationOptions.additionalIps?.find(ips => ips.id.toString() === formData.colocationAdditionalIps)?.price || '0.00'}
                              </span>
                            </div>
                          )}

                          <div className="border-t border-gray-200 pt-1 mt-1 flex justify-between font-medium">
                            <span>Total</span>
                            <span>€{parseFloat(formData.initialPrice).toFixed(2)}</span>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Price input fields */}
                  <div className="mt-3 grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Initial Price
                      </label>
                      <div className="relative">
                        <DollarSign className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                        <input
                          type="text"
                          name="initialPrice"
                          className={`pl-7 pr-3 py-1.5 text-sm border rounded-md w-full font-medium ${priceOverride ? 'border-indigo-300 bg-white' : 'border-gray-300 bg-gray-50'}`}
                          value={priceOverride ? `€${formData.initialPrice}` : `€${parseFloat(formData.initialPrice).toFixed(2)}`}
                          onChange={handleChange}
                          readOnly={!priceOverride}
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Monthly Recurring
                      </label>
                      <div className="relative">
                        <DollarSign className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 w-3 h-3" />
                        <input
                          type="text"
                          name="recurringPrice"
                          className={`pl-7 pr-3 py-1.5 text-sm border rounded-md w-full font-medium ${priceOverride ? 'border-indigo-300 bg-white' : 'border-gray-300 bg-gray-50'}`}
                          value={priceOverride ? `€${formData.recurringPrice}` : `€${parseFloat(formData.recurringPrice).toFixed(2)}`}
                          onChange={handleChange}
                          readOnly={!priceOverride}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="mt-4 flex justify-between">
                    {/* Price Override Toggle */}
                    <div className="flex items-center">
                      <span className="text-xs text-gray-600 mr-2">Override Price</span>
                      <button
                        type="button"
                        onClick={handlePriceOverrideToggle}
                        className={`relative inline-flex h-5 w-10 items-center rounded-full ${priceOverride ? 'bg-indigo-600' : 'bg-gray-200'}`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${priceOverride ? 'translate-x-5' : 'translate-x-1'}`}
                        />
                      </button>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-2">
                      {/* Create Order Button */}
                      {!multiItemMode && (
                        <button
                          type="button"
                          onClick={createSingleOrder}
                          className="px-4 py-1.5 text-sm bg-indigo-700 text-white rounded-md hover:bg-indigo-800 flex items-center"
                          disabled={isLoading}
                        >
                          {isLoading ? (
                            <>
                              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1.5"></div>
                              Processing...
                            </>
                          ) : (
                            <>
                              <Plus className="w-3 h-3 mr-1" />
                              Create Order
                            </>
                          )}
                        </button>
                      )}

                      {/* Add to Order Button (for multi-item mode) */}
                      {multiItemMode && (
                        <button
                          type="button"
                          onClick={addDeviceToList}
                          className="px-4 py-1.5 text-sm bg-indigo-700 text-white rounded-md hover:bg-indigo-800 flex items-center"
                        >
                          {editingDeviceIndex !== null ? (
                            <>
                              <Edit2 className="w-3 h-3 mr-1" />
                              Update Device
                            </>
                          ) : (
                            <>
                              <Plus className="w-3 h-3 mr-1" />
                              Add To Order
                            </>
                          )}
                        </button>
                      )}

                      {/* Enable Multi-Item Mode Button */}
                      {!multiItemMode && !editingDeviceIndex && (
                        <button
                          type="button"
                          onClick={enableMultiItemMode}
                          className="px-4 py-1.5 text-sm border border-indigo-700 text-indigo-700 rounded-md hover:bg-indigo-50 flex items-center"
                          title="Add multiple items to this order"
                        >
                          <List className="w-3 h-3 mr-1" />
                          Add Multiple
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Device List - Only shown in multi-item mode */}
            {multiItemMode && (
              <div className="bg-white rounded-md border">
                <div className="px-3 py-2 border-b bg-gray-50 flex justify-between items-center">
                  <h3 className="text-md font-medium">Devices in Order ({deviceList.length})</h3>
                  {deviceList.length > 0 && (
                    <div className="text-md font-medium text-indigo-700">
                      Total: €{totalPrice.initial.toFixed(2)} / €{totalPrice.recurring.toFixed(2)} monthly
                    </div>
                  )}
                </div>

                {deviceList.length === 0 ? (
                  <div className="p-4 text-center text-gray-500">
                    <Server className="w-8 h-8 mx-auto mb-1 text-gray-300" />
                    <p className="text-sm">No devices added yet. Configure a device above and click "Add To Order".</p>
                  </div>
                ) : (
                  <div className="divide-y">
                    {deviceList.map((device, index) => (
                      <div key={device.id} className="p-3 hover:bg-gray-50">
                        <div className="flex justify-between">
                          <div>
                            <div className="font-medium">{device.cpuName}</div>
                            <div className="text-xs text-gray-600">
                              {device.locationName} • {device.osName}
                            </div>
                          </div>
                          <div className="flex items-center space-x-4">
                            <div className="text-right">
                              <div className="font-medium">€{parseFloat(device.initialPrice).toFixed(2)}</div>
                              <div className="text-xs text-gray-600">€{parseFloat(device.recurringPrice).toFixed(2)}/mo</div>
                            </div>
                            <div className="flex space-x-1">
                              <button
                                type="button"
                                onClick={() => editDevice(index)}
                                className="p-1 text-indigo-700 hover:bg-indigo-100 rounded"
                                title="Edit Device"
                              >
                                <Edit2 className="w-4 h-4" />
                              </button>
                              <button
                                type="button"
                                onClick={() => removeDevice(index)}
                                className="p-1 text-red-600 hover:bg-red-100 rounded"
                                title="Remove Device"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Footer with action buttons */}
        {multiItemMode && (
        <div className="bg-gray-50 px-3 py-2 border-t flex justify-between items-center">




          <button
              onClick={onClose}
              className="px-3 py-1.5 text-sm border border-gray-300 rounded-md text-gray-700 mr-2 hover:bg-gray-100"
            >
              Cancel
            </button>
            <button
            onClick={handleSubmit}
            className={`px-3 py-1.5 text-sm rounded-md flex items-center ${
              deviceList.length === 0 || isLoading
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-indigo-700 text-white hover:bg-indigo-800'
            }`}
            disabled={isLoading || deviceList.length === 0}
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1.5"></div>
                Processing...
              </>
            ) : (
              'Create Multi-Device Order'
            )}
          </button>

          </div>

         )}
      </div>

      {/* Add Operating System Modal */}
      {showAddOsModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-bold">Add New Operating System</h3>
              <button
                onClick={() => {
                  setShowAddOsModal(false);
                  setNewOsData({ os_name: '', os_template: '', logo_url: '' });
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">OS Name*</label>
              <input
                type="text"
                value={newOsData.os_name}
                onChange={(e) => setNewOsData({...newOsData, os_name: e.target.value})}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="e.g. Ubuntu 22.04 LTS"
              />
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">OS Template*</label>
              <input
                type="text"
                value={newOsData.os_template}
                onChange={(e) => setNewOsData({...newOsData, os_template: e.target.value})}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="e.g. ubuntu-22.04"
              />
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Logo</label>
              <input
                type="text"
                value={newOsData.logo_url}
                onChange={(e) => setNewOsData({...newOsData, logo_url: e.target.value})}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="e.g. ubuntu"
              />
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t">
              <button
                type="button"
                onClick={() => {
                  setShowAddOsModal(false);
                  setNewOsData({ os_name: '', os_template: '', logo_url: '' });
                }}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
                disabled={isLoading}
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleAddOs}
                className="px-4 py-2 bg-indigo-700 text-white rounded-md text-sm hover:bg-indigo-800 flex items-center"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Adding...
                  </>
                ) : (
                  <>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Operating System
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MultiDeviceOrderModal;