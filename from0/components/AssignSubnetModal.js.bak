import React, { useState, useEffect } from 'react';
import { XCircle, AlertCircle, Link, Server, Check, Filter } from 'lucide-react';
import axios from 'axios';
import { API_URL } from '../config';
const AssignSubnetModal = ({ subnet, onClose, onAssignSubnet }) => {
  const [formData, setFormData] = useState({
    subnet_id: subnet.id,
    server_id: '',
    server_type: 'dedicated',
    set_as_main_ip: false // This will be determined automatically
  });
  
  const [servers, setServers] = useState({
    dedicated: [],
    blade: []
  });
  
  const [loading, setLoading] = useState(false);
  const [serversLoading, setServersLoading] = useState(false);
  const [error, setError] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedServerName, setSelectedServerName] = useState('');
  const [selectedServerMainIp, setSelectedServerMainIp] = useState(null);
  
  // Fetch servers
  useEffect(() => {
    const fetchServers = async () => {
      const token = localStorage.getItem('admin_token');
      if (!token) {
        console.error('Authentication required. Please log in again.');
        return;
      }

      setServersLoading(true);
      try {
        // Fetch dedicated servers
        const dedicatedResponse = await axios.post(`${API_URL}/api_admin_inventory.php?f=get_inventory_dedicated', {
          token: token,
          search: searchQuery
        });
        
        // Fetch blade servers
        const bladeResponse = await axios.post(`${API_URL}/api_admin_inventory.php?f=get_blade_server_inventory', {
          token: token,
          search: searchQuery
        });
        
        console.log('Dedicated Servers:', dedicatedResponse.data);
        console.log('Blade Servers:', bladeResponse.data);
        
        setServers({
          dedicated: dedicatedResponse.data || [],
          blade: bladeResponse.data || []
        });
      } catch (err) {
        console.error('Error fetching servers:', err);
        setError('Failed to load servers. Please try again.');
      } finally {
        setServersLoading(false);
      }
    };
    
    fetchServers();
  }, [searchQuery]);
  
  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    
    // If changing server type, reset the server_id
    if (name === 'server_type') {
      setFormData({
        ...formData,
        [name]: value,
        server_id: ''
      });
      setSelectedServerName('');
      setSelectedServerMainIp(null);
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
      
      // If selecting a server, store its name and main IP for display
      if (name === 'server_id' && value) {
        const serverType = formData.server_type;
        const selectedServer = servers[serverType].find(s => s.id.toString() === value);
        if (selectedServer) {
          setSelectedServerName(selectedServer.label);
          setSelectedServerMainIp(selectedServer.main_ip);
        }
      }
    }
  };
  
  // Handle search input
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.server_id) {
      console.error('Please select a server');
      setError('Please select a server');
      return;
    }
    
    setLoading(true);
    setError('');
    
    try {
      // Automatically determine if this should be the main IP
      const assignData = {
        ...formData,
        subnet_id: typeof subnet.id === 'string' 
          ? parseInt(subnet.id.replace(/\D/g, ''), 10) 
          : subnet.id,
        // Override set_as_main_ip based on whether the server has an existing main IP
        set_as_main_ip: !selectedServerMainIp
      };

      console.log('Assigning subnet with data:', assignData);

      await onAssignSubnet(assignData);
      console.log('Subnet assigned successfully');
      onClose();
    } catch (err) {
      console.error('Full error object:', err);
      setError(
        err.response?.data?.error || 
        err.message || 
        'An error occurred while assigning the subnet'
      );
    } finally {
      setLoading(false);
    }
  };
  
  // Get filtered servers based on server type
  const getFilteredServers = () => {
    // Use Array.from to create a new array and avoid spread syntax issue
    const serverList = Array.from(servers[formData.server_type] || []);
    
    // Sort servers by label
    return serverList.sort((a, b) => {
      return a.label.localeCompare(b.label);
    });
  };
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg max-w-md w-full">
        <div className="p-6 border-b flex justify-between items-center">
          <h2 className="text-xl font-bold text-gray-800">Assign Subnet to Server</h2>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <XCircle className="w-5 h-5" />
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {error && (
            <div className="p-3 bg-red-50 text-red-700 rounded-md flex items-center">
              <AlertCircle className="w-5 h-5 mr-2" />
              {error}
            </div>
          )}
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Subnet
            </label>
            <div className="p-2 bg-gray-100 rounded-md font-mono">
              {subnet.cidr}
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Server Type
            </label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="server_type"
                  value="dedicated"
                  checked={formData.server_type === 'dedicated'}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-indigo-600 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">Dedicated Server</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="server_type"
                  value="blade"
                  checked={formData.server_type === 'blade'}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-indigo-600 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">Blade Server</span>
              </label>
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Search Servers
            </label>
            <div className="relative">
              <input
                type="text"
                placeholder="Type to search..."
                value={searchQuery}
                onChange={handleSearchChange}
                className="w-full p-2 pl-8 border rounded-md"
              />
              <Filter className="absolute left-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Select Server
            </label>
            <select
              name="server_id"
              value={formData.server_id}
              onChange={handleInputChange}
              className="w-full p-2 border rounded-md"
              required
            >
              <option value="">-- Select a server --</option>
              {getFilteredServers().map(server => (
                <option key={server.id} value={server.id}>
                  {server.label} {server.ipmi ? `(IPMI: ${server.ipmi})` : ''}
                </option>
              ))}
            </select>
            
            {serversLoading && (
              <div className="mt-2 text-sm text-gray-500">
                Loading servers...
              </div>
            )}
            
            {!serversLoading && getFilteredServers().length === 0 && (
              <div className="mt-2 text-sm text-gray-500">
                No {formData.server_type} servers found. Try a different search.
              </div>
            )}
            
            {selectedServerName && (
              <div className="mt-2 text-sm flex items-center">
                {selectedServerMainIp ? (
                  <div className="text-yellow-600 flex items-center">
                    <AlertCircle className="w-4 h-4 mr-1" />
                    This will be configured as an additional IP for the server
                  </div>
                ) : (
                  <div className="text-green-600 flex items-center">
                    <Check className="w-4 h-4 mr-1" />
                    This will be set as the main IP for the server
                  </div>
                )}
              </div>
            )}
          </div>
          
          <div className="pt-4 border-t flex justify-end">
            <button
              type="button"
              onClick={onClose}
              className="mr-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className={`px-4 py-2 text-sm font-medium text-white rounded-md flex items-center ${
                formData.server_id && !loading
                  ? 'bg-indigo-700 hover:bg-indigo-800'
                  : 'bg-indigo-400 cursor-not-allowed'
              }`}
              disabled={!formData.server_id || loading}
            >
              <Link className="w-4 h-4 mr-1" />
              {loading ? 'Assigning...' : 'Assign Subnet'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AssignSubnetModal;