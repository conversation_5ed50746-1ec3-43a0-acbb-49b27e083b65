import React, { useEffect, useState } from 'react';

// PageTransition component to wrap page content and provide smooth transitions
const PageTransition = ({ children }) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Set a very short timeout to ensure the transition happens after the component mounts
    // This helps prevent the flash of content during navigation
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 10);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className={`page-transition ${isVisible ? 'opacity-100' : 'opacity-90'}`}>
      {children}
    </div>
  );
};

export default PageTransition;
