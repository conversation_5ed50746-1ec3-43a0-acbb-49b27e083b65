// MacAddressDetectionButton.js
import React, { useState } from 'react';
import { Wifi, Refresh<PERSON>w, AlertTriangle, CheckCircle, Network, ChevronDown, ChevronUp } from 'lucide-react';

const MacAddressDetectionButton = ({ selectedItem, onUpdateMac, isEditMode, serverType = 'blade' }) => {
  const [detecting, setDetecting] = useState(false);
  const [detectionResults, setDetectionResults] = useState(null);
  const [error, setError] = useState(null);
  const [showDebug, setShowDebug] = useState(false); // Toggle for showing debug info
  const [showAllMacs, setShowAllMacs] = useState(false); // Toggle for showing all MACs
  const [selectedMacIndex, setSelectedMacIndex] = useState(0); // Index of selected MAC

  const detectMacAddress = async () => {
    if (!selectedItem || !selectedItem.ipmi) {
      setError("No valid iDRAC IP address available");
      return;
    }
    
    try {
      setDetecting(true);
      setError(null);
      setDetectionResults(null);
      
      const token = localStorage.getItem('admin_token');
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=detect_mac_address', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          token: token,
          server_id: selectedItem.id,
          ipmi_address: selectedItem.ipmi,
          ipmi_username: 'root',
          ipmi_password: selectedItem.ipmi_root_pass || '',
          idrac_version: selectedItem.idrac_version || 8,
          server_type: serverType
        })
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP error ${response.status}: ${errorText}`);
      }
      
      const data = await response.json();
      
      if (data.success && data.all_mac_addresses && data.all_mac_addresses.length > 0) {
        // Filter out WWN/WWPN addresses which are 8 octets instead of 6
        // MAC addresses are always 6 octets (e.g., 00:11:22:33:44:55)
        // WWNs are 8 octets (e.g., 20:00:00:11:22:33:44:55)
        const filteredMacs = data.all_mac_addresses.filter(mac => {
          // Check if this is a valid MAC address (6 octets)
          const macAddress = mac.mac;
          const parts = macAddress.split(/[:-]/);
          
          // Skip if it has 8 octets (likely a WWN)
          if (parts.length === 8) {
            console.log(`Skipping WWN/WWPN: ${macAddress}`);
            return false;
          }
          
          // Skip if it starts with patterns typical of WWN
          if (macAddress.startsWith('20:00:') || 
              macAddress.startsWith('21:00:') || 
              macAddress.startsWith('10:00:')) {
            console.log(`Skipping WWN/WWPN pattern: ${macAddress}`);
            return false;  
          }
          
          return true;
        });
        
        // Look in the raw output for specific MAC address fields
        const rawOutput = data.raw_output || "";
        let permanentMacMatch = null;
        
        // Search for PermanentMACAddress field in the output
        const permanentMacRegex = /PermanentMACAddress\s*=\s*([0-9A-Fa-f]{2}(?::[0-9A-Fa-f]{2}){5})/g;
        const macMatches = [...rawOutput.matchAll(permanentMacRegex)];
        
        if (macMatches.length > 0) {
          permanentMacMatch = macMatches[0][1];
          console.log(`Found PermanentMACAddress in raw output: ${permanentMacMatch}`);
          
          // Add this to the filtered MACs if not already there
          if (!filteredMacs.some(mac => mac.mac.toLowerCase() === permanentMacMatch.toLowerCase())) {
            filteredMacs.unshift({
              mac: permanentMacMatch,
              interface: "PermanentMACAddress",
              is_permanent: true,
              link_status: "Unknown"
            });
          }
        }
        
        // If we have no valid MAC addresses after filtering, show an error
        if (filteredMacs.length === 0) {
          setError("No valid MAC addresses detected (found only WWN/WWPN identifiers)");
          setDetecting(false);
          return;
        }
        
        // Find the best MAC to use as primary
        let bestMacIndex = 0;
        
        // First try to use the exact PermanentMACAddress if found
        if (permanentMacMatch) {
          const exactMatchIndex = filteredMacs.findIndex(
            mac => mac.mac.toLowerCase() === permanentMacMatch.toLowerCase()
          );
          if (exactMatchIndex >= 0) {
            bestMacIndex = exactMatchIndex;
          }
        }
        
        // Otherwise, try to find any permanent MAC address
        if (!permanentMacMatch) {
          const permanentMacIndex = filteredMacs.findIndex(mac => mac.is_permanent);
          if (permanentMacIndex >= 0) {
            bestMacIndex = permanentMacIndex;
          }
        }
        
        // If no permanent MAC found, look for specific pattern matches
        if (bestMacIndex === 0 && !filteredMacs[0].is_permanent) {
          // Look for interfaces that are likely to be the primary/physical
          for (let i = 0; i < filteredMacs.length; i++) {
            const mac = filteredMacs[i];
            const interfaceName = mac.interface?.toLowerCase() || '';
            
            // Prioritize physical interfaces, integrated NICs, etc.
            if (
              interfaceName.includes('physical') || 
              interfaceName.includes('integrated') || 
              interfaceName.includes('nic.integrated') ||
              interfaceName.includes('embedded') ||
              interfaceName.includes('lom') ||
              (interfaceName.includes('nic') && !interfaceName.includes('virtual'))
            ) {
              bestMacIndex = i;
              break;
            }
          }
        }
        
        // Set initial selected MAC
        setSelectedMacIndex(bestMacIndex);
        
        setDetectionResults({
          primary_mac: filteredMacs[bestMacIndex].mac,
          all_macs: filteredMacs,
          raw_data: data.raw_output,
          pendingUpdates: true
        });
      } else {
        setError(data.error || "No MAC address detected");
      }
    } catch (err) {
      console.error("Error detecting MAC address:", err);
      setError(err.message || "Failed to detect MAC address");
    } finally {
      setDetecting(false);
    }
  };

  const selectMac = (index) => {
    if (detectionResults && detectionResults.all_macs && detectionResults.all_macs[index]) {
      setSelectedMacIndex(index);
      setDetectionResults({
        ...detectionResults,
        primary_mac: detectionResults.all_macs[index].mac,
        pendingUpdates: true
      });
    }
  };

  const applyMacAddress = () => {
    if (!detectionResults || !detectionResults.primary_mac) {
      setError("No MAC address to apply");
      return;
    }
    
    // Call the update function from props with the detected MAC
    onUpdateMac(detectionResults.primary_mac);
    
    // Mark as applied
    setDetectionResults(prev => ({
      ...prev,
      pendingUpdates: false,
      changesApplied: true
    }));
  };

  if (isEditMode) return null;

  return (
    <div className="mb-2 mt-1">
      <button
        id="mac-detect-button"
        onClick={detectMacAddress}
        disabled={detecting}
        className={`flex items-center px-2 py-1 rounded text-xs font-medium
          ${detecting ? 'bg-gray-200 text-gray-600' : 'bg-indigo-100 hover:bg-indigo-200 text-indigo-700'}`}
      >
        {detecting ? <RefreshCw className="w-3 h-3 mr-1 animate-spin" /> : <Wifi className="w-3 h-3 mr-1" />}
        {detecting ? 'Detecting MAC...' : 'Auto-Detect MAC Address'}
      </button>
      
      {error && (
        <div className="mt-2 p-2 bg-red-50 text-red-700 rounded-md text-xs flex items-start">
          <AlertTriangle className="w-3 h-3 mr-1 mt-0.5 flex-shrink-0" />
          <span>{error}</span>
        </div>
      )}

      {detectionResults && !error && (
        <div className="mt-2 p-2 bg-green-50 text-green-800 rounded-md text-xs">
          <div className="flex items-center justify-between mb-1">
            <div className="flex items-center">
              <CheckCircle className="w-3 h-3 mr-1" />
              <span className="font-medium">
                {detectionResults.primary_mac 
                  ? (detectionResults.all_macs[selectedMacIndex]?.is_permanent 
                    ? "Permanent MAC address detected!" 
                    : "MAC address detected!")
                  : "Detection completed but no MAC address found"}
              </span>
            </div>
            <div className="flex space-x-2">
           {/*     <button 
                onClick={() => setShowAllMacs(!showAllMacs)}
                className="text-indigo-600 hover:text-indigo-800 text-xs px-1 py-0.5 bg-white rounded border border-indigo-200"
              >
                {showAllMacs ? "Hide MACs" : "All MACs"}
              </button>
         <button 
                onClick={() => setShowDebug(!showDebug)}
                className="text-indigo-600 hover:text-indigo-800 text-xs px-1 py-0.5 bg-white rounded border border-indigo-200"
              >
                {showDebug ? "Hide Debug" : "Show Debug"}
              </button>  */}
            </div>
          </div>

          {detectionResults.primary_mac && detectionResults.pendingUpdates && (
            <div className="mt-1">
             
              
              {showAllMacs && detectionResults.all_macs && detectionResults.all_macs.length > 0 && (
                <div className="mt-2 bg-white p-2 rounded border border-gray-200">
                  <p className="text-xs font-medium mb-1 text-gray-700">All detected MAC addresses:</p>
                  <ul className="text-xs space-y-1">
                    {detectionResults.all_macs.map((mac, index) => (
                      <li key={index} 
                        className={`flex items-center justify-between p-1 rounded cursor-pointer ${
                          index === selectedMacIndex 
                            ? 'bg-indigo-50 border border-indigo-200' 
                            : 'hover:bg-gray-50'
                        }`}
                        onClick={() => selectMac(index)}
                      >
                        <div className="flex items-center">
                          <span className={`w-3 h-3 mr-1 rounded-full ${
                            mac.is_permanent 
                              ? 'bg-green-100 border border-green-300' 
                              : 'bg-blue-100 border border-blue-300'
                          }`}></span>
                          <span className="font-medium">{mac.mac}</span>
                          {mac.is_permanent && (
                            <span className="ml-1 text-green-600 text-xs">(Permanent)</span>
                          )}
                        </div>
                        <div className="text-gray-500">
                          {mac.interface && `${mac.interface}`}
                          {mac.link_status && ` (${mac.link_status})`}
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              
              <button
                onClick={applyMacAddress}
                className="mt-2 px-2 py-1 bg-indigo-600 text-white rounded text-xs font-medium hover:bg-indigo-700"
              >
                Use This MAC Address
              </button>
            </div>
          )}

          {detectionResults.changesApplied && (
            <div className="mt-2 p-2 bg-green-100 rounded border border-green-200">
              <p className="text-green-800 text-xs font-medium flex items-center">
                <CheckCircle className="w-3 h-3 mr-1" />
                MAC address updated successfully!
              </p>
            </div>
          )}
          
          {/* Debug information */}
          {showDebug && (
            <div className="mt-3 border-t border-gray-200 pt-2">
              <div className="font-medium mb-1">Debug Information:</div>
              
              {detectionResults.debug_lines && detectionResults.debug_lines.length > 0 && (
                <div className="mb-2">
                  <div className="font-medium text-xs mb-1">Parser Debug:</div>
                  <div className="bg-gray-100 p-2 rounded text-gray-800 overflow-auto max-h-40 text-xs font-mono whitespace-pre-wrap">
                    {detectionResults.debug_lines.map((line, idx) => (
                      <div key={idx}>{line}</div>
                    ))}
                  </div>
                </div>
              )}
              
              <div className="font-medium text-xs mb-1">Raw Output:</div>
              <div className="bg-gray-100 p-2 rounded text-gray-800 overflow-auto max-h-60 text-xs font-mono whitespace-pre-wrap">
                {detectionResults.raw_data}
              </div>
              
              {detectionResults.commands_tried && (
                <div className="mt-2">
                  <div className="font-medium text-xs mb-1">Commands Used:</div>
                  <div className="bg-gray-100 p-2 rounded text-gray-800 overflow-auto max-h-20 text-xs font-mono whitespace-pre-wrap">
                    {Object.entries(detectionResults.commands_tried).map(([name, cmd], idx) => (
                      cmd ? <div key={idx}><span className="font-medium">{name}:</span> {cmd}</div> : null
                    ))}
                  </div>
                </div>
              )}
              
              <button 
                onClick={() => navigator.clipboard.writeText(detectionResults.raw_data)}
                className="mt-2 px-2 py-1 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded text-xs"
              >
                Copy Raw Output
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MacAddressDetectionButton;