import React, { useState, useEffect, useCallback, memo, useRef } from 'react';
import { 
  RefreshCw,
  Power,
  PowerOff,
  AlertTriangle
} from 'lucide-react';
import { API_URL } from '../config';
// Using memo to prevent unnecessary re-renders
const PowerManagement = memo(({ 
  server, 
  serverType, 
  ipmiAddress, 
  ipmiRootPassword, 
  onRefresh 
}) => {
  const [powerStatus, setPowerStatus] = useState('unknown');
  const [isLoading, setIsLoading] = useState(false);
  const [lastAction, setLastAction] = useState(null);
  const [actionError, setActionError] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // CRITICAL FIX: Completely disable automatic refreshes - they're causing the cycle
  
  // Fetch current power status with useCallback to maintain reference stability
  const fetchPowerStatus = useCallback(async (retryCount = 0) => {
    if (!ipmiAddress || !ipmiRootPassword) {
      setPowerStatus('unavailable');
      return;
    }
    
    console.log("fetchPowerStatus called");
    
    try {
      setIsLoading(true);
      // Only show errors after all retries fail
      if (retryCount === 0) {
        setActionError(null);
      }
      
      const token = localStorage.getItem('admin_token');
      
      console.log(`Fetching current power status (attempt ${retryCount + 1})...`);
      
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=power_control`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          server_id: server.id,
          server_type: serverType,
          ipmi_address: ipmiAddress,
          ipmi_username: 'root',
          ipmi_password: ipmiRootPassword,
          action: 'status'
        })
      });
      
      // Get response text first for debugging
      const responseText = await response.text();
      console.log(`Status check raw response (attempt ${retryCount + 1}):`, responseText);
      
      // Extract and parse the first JSON object if multiple are returned
      let data;
      try {
        if (responseText.indexOf('}{') !== -1) {
          const firstJsonEnd = responseText.indexOf('}{') + 1;
          const firstJsonPart = responseText.substring(0, firstJsonEnd);
          data = JSON.parse(firstJsonPart);
        } else {
          data = JSON.parse(responseText);
        }
        
        if (data.success) {
            console.log("Power status received:", data.power_status);
            setPowerStatus(data.power_status || 'unknown');
            setActionError(null); // Clear any previous errors since we got a successful response
            setIsLoading(false); // Set loading to false immediately
            return; // Exit the function once we have the power status
          } else {
          console.error("Status check failed:", data.error);
          
          // If we hit the "No more sessions" error, retry after a delay
          if (data.error && data.error.includes("No more sessions") && retryCount < 3) {
            console.log(`Will retry in ${2000 * (retryCount + 1)}ms...`);
            setTimeout(() => fetchPowerStatus(retryCount + 1), 2000 * (retryCount + 1));
            return;
          }
          
          // Only set the error if we've exhausted all retries
          if (retryCount >= 3) {
            setActionError(`Failed to get power status: ${data.error}`);
          }
        }
      } catch (parseError) {
        console.error("Error parsing status response:", parseError);
        
        // Retry on parse errors too
        if (retryCount < 3) {
          console.log(`Will retry after parse error in ${2000 * (retryCount + 1)}ms...`);
          setTimeout(() => fetchPowerStatus(retryCount + 1), 2000 * (retryCount + 1));
          return;
        }
        
        if (retryCount >= 3) {
          setActionError(`Failed to parse status response: ${parseError.message}`);
        }
      }
    } catch (err) {
      console.error("Error fetching power status:", err);
      
      // Retry on network/other errors too
      if (retryCount < 3) {
        console.log(`Will retry after error in ${2000 * (retryCount + 1)}ms...`);
        setTimeout(() => fetchPowerStatus(retryCount + 1), 2000 * (retryCount + 1));
        return;
      }
      
      if (retryCount >= 3) {
        setActionError(`Failed to get power status: ${err.message}`);
      }
    } finally {
      if (retryCount >= 3 || powerStatus !== 'unknown') {
        setIsLoading(false);
      }
    }
  }, [ipmiAddress, ipmiRootPassword, server?.id, serverType]);
  
  // Perform power action (on, off, restart)
  const performPowerAction = useCallback(async (action) => {
    if (!ipmiAddress || !ipmiRootPassword) {
      setActionError('IPMI credentials not available');
      return;
    }
    
    console.log(`performPowerAction: ${action}`);
    
    try {
      setIsLoading(true);
      setLastAction(action);
      setActionError(null);
      
      const token = localStorage.getItem('admin_token');
      
      // Set temporary status to show action is in progress
      if (action === 'on') setPowerStatus('powering_on');
      else if (action === 'off') setPowerStatus('powering_off');
      else if (action === 'restart') setPowerStatus('restarting');
      
      // Send power command
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=power_control`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          server_id: server.id,
          server_type: serverType,
          ipmi_address: ipmiAddress,
          ipmi_username: 'root',
          ipmi_password: ipmiRootPassword,
          action: action
        })
      });
      
      // Make sure we get a valid JSON response
      let responseText = await response.text();
      let data;
      
      try {
        // The server might be sending two JSON objects concatenated
        // Try to extract just the first valid JSON object
        if (responseText.indexOf('}{') !== -1) {
          console.warn("Detected concatenated JSON response, attempting to fix");
          const firstJsonEnd = responseText.indexOf('}{') + 1;
          const firstJsonPart = responseText.substring(0, firstJsonEnd);
          console.log("Extracted first JSON part:", firstJsonPart);
          data = JSON.parse(firstJsonPart);
        } else {
          data = JSON.parse(responseText);
        }
      } catch (parseError) {
        console.error("JSON parse error:", parseError, "Response:", responseText);
        
        // Last resort - try regular expression to extract first valid JSON object
        try {
          const jsonRegex = /{.*?}/;
          const match = responseText.match(jsonRegex);
          if (match && match[0]) {
            console.warn("Attempting to extract JSON with regex");
            data = JSON.parse(match[0]);
          } else {
            throw new Error("No valid JSON found in response");
          }
        } catch (regexError) {
          throw new Error(`Invalid response from server. Response was: ${responseText.substring(0, 100)}...`);
        }
      }
      
      if (!data.success) {
        throw new Error(data.error || 'Unknown error occurred');
      }
      
      // Success - just show confirmation message, don't wait for final status
      console.log(`${action} command sent successfully`);
      
      // Update the status to reflect command was sent
      setLastAction(action);
      
      // For better UX - assume the command will succeed and update status optimistically
      // We use shorter timeouts than before
      if (action === 'on') {
        setTimeout(() => {
          console.log("Setting power status to 'on'");
          setPowerStatus('on');
        }, 1500); 
      } else if (action === 'off') {
        setTimeout(() => {
          console.log("Setting power status to 'off'");
          setPowerStatus('off');
        }, 1500);
      } else if (action === 'restart') {
        // For restart, show transitional state longer but still reasonably fast
        setTimeout(() => {
          console.log("Restart: Setting power status to 'off'");
          setPowerStatus('off');
          setTimeout(() => {
            console.log("Restart: Setting power status to 'on'");
            setPowerStatus('on');
          }, 1500);
        }, 1500);
      }
      
    } catch (err) {
      console.error(`Error performing power action ${action}:`, err);
      
      // Provide user-friendly error message
      let errorMessage = `Failed to ${action} server: ${err.message}`;
      setActionError(errorMessage);
      
      // Reset status if action failed
      setPowerStatus(powerStatus === 'powering_on' || powerStatus === 'powering_off' || powerStatus === 'restarting' 
        ? 'unknown' 
        : powerStatus);
    } finally {
      setIsLoading(false);
    }
  }, [ipmiAddress, ipmiRootPassword, powerStatus, server?.id, serverType]);
  
  // Manual refresh function for the "Refresh Parent" button
  const handleManualRefresh = useCallback(() => {
    if (!onRefresh) return;
    
    console.log("Manual refresh triggered");
    setIsRefreshing(true);
    onRefresh();
    setTimeout(() => setIsRefreshing(false), 500);
  }, [onRefresh]);
  
  // Load power status on component mount only
  useEffect(() => {
    if (ipmiAddress && ipmiRootPassword) {
      console.log("Initial power status fetch");
      fetchPowerStatus();
    }
    
    // No cleanup or dependencies beyond initial mount
  }, []);
  
  // CRITICAL FIX: REMOVE ALL AUTO-REFRESH EFFECTS
  // The previous automatic refresh effects were causing the cycle
  
  // Get status badge color based on power status
  const getStatusBadgeClass = () => {
    switch (powerStatus) {
      case 'on':
        return 'bg-green-100 text-green-800';
      case 'off':
        return 'bg-red-100 text-red-800';
      case 'powering_on':
      case 'powering_off':
      case 'restarting':
        return 'bg-yellow-100 text-yellow-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      case 'unavailable':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  // Get status text based on power status
  const getStatusText = () => {
    switch (powerStatus) {
      case 'on':
        return 'Powered On';
      case 'off':
        return 'Powered Off';
      case 'powering_on':
        return 'Powering On...';
      case 'powering_off':
        return 'Powering Off...';
      case 'restarting':
        return 'Restarting...';
      case 'error':
        return 'Error';
      case 'unavailable':
        return 'Unavailable';
      default:
        return 'Unknown';
    }
  };
  
  // If IPMI credentials are not available
  if (!ipmiAddress || !ipmiRootPassword) {
    return (
      <div className="bg-gray-50 p-3 rounded text-sm text-gray-700">
        <p>Power management unavailable. IPMI credentials required.</p>
      </div>
    );
  }
  
  // Calculate if we are in a transition state
  const isTransitioning = powerStatus === 'powering_on' || powerStatus === 'powering_off' || powerStatus === 'restarting';
  
  // Calculate button disabled states
  const isPowerOnDisabled = isLoading || isRefreshing || powerStatus === 'on' || isTransitioning;
  const isPowerOffDisabled = isLoading || isRefreshing || powerStatus === 'off' || isTransitioning;
  const isRestartDisabled = isLoading || isRefreshing || powerStatus === 'off' || isTransitioning;
  
  return (
    <div className="transition-opacity duration-200">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center">
          <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center ${getStatusBadgeClass()}`}>
            {powerStatus === 'on' ? (
              <Power className="w-3 h-3 mr-1" />
            ) : powerStatus === 'off' ? (
              <PowerOff className="w-3 h-3 mr-1" />
            ) : (
              <RefreshCw className={`w-3 h-3 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
            )}
            {getStatusText()}
          </span>
          
          <button
            onClick={() => !isLoading && fetchPowerStatus()}
            disabled={isLoading}
            className="ml-2 p-1 text-gray-500 hover:text-indigo-700 rounded hover:bg-gray-100 relative group"
            title="Refresh Power Status"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin text-indigo-600' : ''}`} />
            <span className="hidden group-hover:block absolute top-full left-1/2 transform -translate-x-1/2 bg-black text-white text-xs rounded py-1 px-2 mt-1 whitespace-nowrap">
              Check Status
            </span>
          </button>
          
 
        </div>
        
        <div className="flex space-x-2">
          <button
            onClick={() => performPowerAction('on')}
            disabled={isPowerOnDisabled}
            className={`px-2 py-1 rounded text-xs font-medium flex items-center ${
              isPowerOnDisabled
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-green-100 text-green-800 hover:bg-green-200'
            }`}
          >
            <Power className="w-3 h-3 mr-1" />
            Power On
          </button>
          
          <button
            onClick={() => performPowerAction('restart')}
            disabled={isRestartDisabled}
            className={`px-2 py-1 rounded text-xs font-medium flex items-center ${
              isRestartDisabled
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-blue-100 text-blue-800 hover:bg-blue-200'
            }`}
          >
            <RefreshCw className="w-3 h-3 mr-1" />
            Restart
          </button>
          
          <button
            onClick={() => performPowerAction('off')}
            disabled={isPowerOffDisabled}
            className={`px-2 py-1 rounded text-xs font-medium flex items-center ${
              isPowerOffDisabled
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-red-100 text-red-800 hover:bg-red-200'
            }`}
          >
            <PowerOff className="w-3 h-3 mr-1" />
            Power Off
          </button>
        </div>
      </div>
      
      {actionError && !isLoading && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-3 py-2 rounded text-xs mb-3">
          {actionError}
        </div>
      )}
      
      {lastAction && !actionError && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-3 py-2 rounded text-xs mb-3 flex items-center justify-between">
          <span>
            {lastAction === 'on' && 'Power on command sent successfully.'}
            {lastAction === 'off' && 'Power off command sent successfully.'}
            {lastAction === 'restart' && 'Restart command sent successfully.'}
          </span>
        </div>
      )}
    </div>
  );
});

PowerManagement.displayName = 'PowerManagement';

export default PowerManagement;