import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { 
  RefreshCw, 
  AlertTriangle, 
  Thermometer, 
  Wind, 
  Battery, 
  Activity,
  Clock,
  Lock,
  Download,
  Server
} from 'lucide-react';
import { API_URL } from '../config';
const SensorInfoTab = ({ server, serverType }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [idracConnected, setIdracConnected] = useState(false);
  const [idracVersion, setIdracVersion] = useState(8);
  
  // Sensor data
  const [sensorData, setSensorData] = useState({
    temperatures: [],
    fans: [],
    power: [],
    voltages: [],
    others: []
  });
  
  // Raw output for debugging
  const [rawOutput, setRawOutput] = useState('');
  
  // Default credentials - memoized
  const idracCredentials = useMemo(() => ({
    username: 'root',
    password: server?.ipmi_root_pass || ''
  }), [server?.ipmi_root_pass]);

  // Execute iDRAC batch commands - more efficient as it makes a single API call
  const executeIdracBatchCommands = useCallback(async (commands, bypassCache = false) => {
    try {
      const token = localStorage.getItem('admin_token');
      
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=execute_idrac_batch_commands`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token,
          ipmi_address: server?.ipmi,
          ipmi_username: idracCredentials.username,
          ipmi_password: idracCredentials.password,
          idrac_version: idracVersion,
          server_id: server?.id,
          server_type: serverType,
          commands,
          bypass_cache: bypassCache
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      
      return await response.json();
    } catch (err) {
      console.error(`Error executing batch commands:`, err);
      throw err;
    }
  }, [server?.id, server?.ipmi, idracCredentials, idracVersion, serverType]);

  // Execute single iDRAC command - for backward compatibility
  const executeIdracCommand = useCallback(async (command, bypassCache = false) => {
    try {
      const token = localStorage.getItem('admin_token');
      
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=execute_idrac_command`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token,
          ipmi_address: server?.ipmi,
          ipmi_username: idracCredentials.username,
          ipmi_password: idracCredentials.password,
          idrac_version: idracVersion,
          server_id: server?.id,
          server_type: serverType,
          command,
          bypass_cache: bypassCache
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      
      return await response.json();
    } catch (err) {
      console.error(`Error executing command '${command}':`, err);
      throw err;
    }
  }, [server?.id, server?.ipmi, idracCredentials, idracVersion, serverType]);

  // Process sensor data
  const processSensorData = useCallback((output) => {
    // Initialize result object
    const result = {
      temperatures: [],
      fans: [],
      power: [],
      voltages: [],
      others: []
    };
    
    if (!Array.isArray(output) || output.length === 0) {
      return result;
    }
    
    // Regular expressions for different sensor types - compiled once for better performance
    const temperatureRegex = /(Temp|Temperature)/i;
    const fanRegex = /Fan/i;
    const powerRegex = /(Power|Watt)/i;
    const voltageRegex = /(Volt|VRM|PSU)/i;
    
    // Process each line
    output.forEach(line => {
      // Skip empty lines or headers
      if (!line.trim() || line.includes('---') || line.includes('Sensor')) {
        return;
      }
      
      // Split line by pipe character or spaces
      let parts;
      if (line.includes('|')) {
        parts = line.split('|').map(part => part.trim());
      } else {
        // For outputs that don't use pipe separators
        parts = line.split(/\s{2,}/).filter(part => part.trim() !== '');
      }
      
      // Need at least 3 parts: name, reading, status
      if (parts.length < 3) {
        return;
      }
      
      const name = parts[0];
      const reading = parts[1];
      const status = parts[2];
      
      // Determine the sensor type and extract numeric value
      let numericValue = null;
      const numericMatch = reading.match(/(\d+(\.\d+)?)/);
      if (numericMatch) {
        numericValue = parseFloat(numericMatch[1]);
      }
      
      const sensorData = {
        name,
        reading,
        status,
        value: numericValue
      };
      
      // Categorize the sensor
      if (temperatureRegex.test(name)) {
        result.temperatures.push(sensorData);
      } else if (fanRegex.test(name)) {
        result.fans.push(sensorData);
      } else if (powerRegex.test(name)) {
        result.power.push(sensorData);
      } else if (voltageRegex.test(name)) {
        result.voltages.push(sensorData);
      } else {
        result.others.push(sensorData);
      }
    });
    
    return result;
  }, []);

  // Optimized fetch sensor data - single API call with all required commands
  const fetchSensorData = useCallback(async (bypassCache = false) => {
    if (!server || !server.ipmi) {
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      // Define the commands we want to execute in a single batch
      // This is more efficient than making multiple API calls
      const commands = [
        'racadm getsensorinfo', // Primary command for sensor data
        'racadm version',       // To confirm connection is working
        'racadm get System.ServerInfo' // Fallback information if sensors fail
      ];
      
      // Execute all commands in a single API call
      const batchResult = await executeIdracBatchCommands(commands, bypassCache);
      
      if (!batchResult.success) {
        throw new Error(batchResult.error || 'Failed to execute commands');
      }
      
      // Check if connection is successful based on version command
      if (batchResult.results['racadm version']?.success) {
        setIdracConnected(true);
      } else {
        throw new Error('Failed to connect to iDRAC');
      }
      
      // Process sensor information if available
      const sensorResult = batchResult.results['racadm getsensorinfo'];
      
      if (sensorResult?.success) {
        // Store raw output for debugging
        setRawOutput(sensorResult.raw_output?.join('\n') || '');
        
        // Process the sensor data - either use server-processed data or our own processing
        const processedData = sensorResult.processed_output || processSensorData(sensorResult.raw_output || []);
        setSensorData(processedData);
        setLastUpdated(new Date());
      } else if (batchResult.results['racadm get System.ServerInfo']?.success) {
        // Use server info as a fallback
        setError("Connected to iDRAC but sensor data is limited. Some sensors may not be available.");
        
        // Create minimal data to show connectivity works
        setSensorData({
          temperatures: [],
          fans: [],
          power: [],
          voltages: [],
          others: []
        });
        setLastUpdated(new Date());
      } else {
        throw new Error('Unable to retrieve sensor information');
      }
      
    } catch (err) {
      console.error('Error fetching sensor information:', err);
      setError(`Failed to fetch sensor data: ${err.message}`);
      setIdracConnected(false);
    } finally {
      setLoading(false);
    }
  }, [server, executeIdracBatchCommands, processSensorData]);

  // Connect to iDRAC and fetch data in a single operation
  const connectToIdrac = useCallback(async () => {
    // Use fetchSensorData for connection and data retrieval
    // This simplifies the code and ensures we don't have multiple loading states
    if (!server?.ipmi || !idracCredentials.username) {
      setError("iDRAC configuration is missing");
      return;
    }
    
    await fetchSensorData(true); // Force bypass cache on initial connection
  }, [server?.ipmi, idracCredentials, fetchSensorData]);

  // Disconnect from iDRAC
  const disconnectFromIdrac = useCallback(() => {
    setIdracConnected(false);
    setSensorData({
      temperatures: [],
      fans: [],
      power: [],
      voltages: [],
      others: []
    });
  }, []);

  // Effect to connect to iDRAC on mount
  useEffect(() => {
    if (server?.ipmi) {
      connectToIdrac();
    }
  }, [server?.id, server?.ipmi, connectToIdrac]);

  // Export sensor data as CSV
  const exportSensorData = useCallback(() => {
    // Prepare the CSV content
    const headers = ['Category', 'Sensor Name', 'Reading', 'Status'];
    
    const rows = [
      ...sensorData.temperatures.map(sensor => ['Temperature', sensor.name, sensor.reading, sensor.status]),
      ...sensorData.fans.map(sensor => ['Fan', sensor.name, sensor.reading, sensor.status]),
      ...sensorData.power.map(sensor => ['Power', sensor.name, sensor.reading, sensor.status]),
      ...sensorData.voltages.map(sensor => ['Voltage', sensor.name, sensor.reading, sensor.status]),
      ...sensorData.others.map(sensor => ['Other', sensor.name, sensor.reading, sensor.status])
    ];
    
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.map(cell => `"${(cell || '').toString().replace(/"/g, '""')}"`).join(','))
    ].join('\n');
    
    // Create download link
    const encodedUri = encodeURI('data:text/csv;charset=utf-8,' + csvContent);
    const link = document.createElement('a');
    link.setAttribute('href', encodedUri);
    link.setAttribute('download', `idrac_sensors_${server?.id || 'server'}_${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    
    // Trigger download
    link.click();
    
    // Clean up
    document.body.removeChild(link);
  }, [sensorData, server?.id]);

  // Get status color
  const getStatusColor = useCallback((status) => {
    status = status?.toLowerCase() || '';
    
    if (status.includes('ok') || status.includes('normal') || status.includes('good')) {
      return 'text-green-600';
    } else if (status.includes('warning') || status.includes('caution')) {
      return 'text-yellow-600';
    } else if (status.includes('critical') || status.includes('fail') || status.includes('error')) {
      return 'text-red-600';
    }
    
    return 'text-gray-600';
  }, []);

  // Loading state
  if (loading && !idracConnected) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-4 flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="w-12 h-12 animate-spin mx-auto text-indigo-700 mb-3" />
          <p className="text-lg text-gray-600">Connecting to iDRAC and loading sensor data...</p>
        </div>
      </div>
    );
  }
  
  // Error state with retry button
  if (error && !idracConnected) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-4">
        <div className="text-center max-w-md mx-auto">
          <div className="bg-red-100 p-4 rounded-lg mb-4">
            <AlertTriangle className="w-12 h-12 text-red-600 mx-auto mb-3" />
            <p className="text-lg text-red-700 font-medium">{error}</p>
          </div>
          <p className="text-sm text-gray-600 mb-4">
            Unable to connect to iDRAC. This could be due to incorrect credentials or network issues.
          </p>
          <button 
            onClick={connectToIdrac}
            className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Main component rendering
  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm p-4">
        <div className="flex flex-wrap justify-between items-center">
          <h4 className="text-lg font-semibold text-gray-800 mb-2 md:mb-0">
            Hardware Sensor Information
            {idracConnected && (
              <span className="ml-2 text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded">
                iDRAC Connected
              </span>
            )}
          </h4>
          <div className="flex space-x-2">
            {idracConnected ? (
              <>
                <button
                  onClick={disconnectFromIdrac}
                  className="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md flex items-center text-sm transition-colors"
                >
                  <Lock className="w-4 h-4 mr-1.5" />
                  Disconnect
                </button>
                <button
                  onClick={() => fetchSensorData(true)} // Force bypass cache
                  className="p-2 text-gray-500 hover:text-indigo-700 bg-gray-100 hover:bg-gray-200 rounded-full transition-colors"
                  title="Refresh Sensor Data"
                  disabled={loading}
                >
                  <RefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
                </button>
                <button
                  onClick={exportSensorData}
                  className="p-2 text-gray-500 hover:text-indigo-700 bg-gray-100 hover:bg-gray-200 rounded-full transition-colors"
                  title="Export Sensor Data"
                >
                  <Download className="w-5 h-5" />
                </button>
              </>
            ) : (
              <button
                onClick={connectToIdrac}
                className="px-3 py-1 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md flex items-center text-sm transition-colors"
              >
                <Server className="w-4 h-4 mr-1.5" />
                Connect to iDRAC
              </button>
            )}
          </div>
        </div>
        
        {/* Last updated timestamp */}
        {lastUpdated && (
          <div className="text-xs text-gray-500 mt-1">
            Last updated: {lastUpdated.toLocaleString()}
          </div>
        )}
      </div>

      {/* Main content - show loading spinner if fetching data */}
      {loading && idracConnected ? (
        <div className="bg-white rounded-lg shadow-sm p-4 flex items-center justify-center h-48">
          <div className="text-center">
            <RefreshCw className="w-10 h-10 animate-spin mx-auto text-indigo-700 mb-3" />
            <p className="text-md text-gray-600">Updating sensor information...</p>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {/* Temperature Sensors */}
          <div className="bg-white rounded-lg shadow-sm p-4">
            <div className="flex items-center mb-4">
              <Thermometer className="w-5 h-5 text-red-500 mr-2" />
              <h5 className="text-md font-semibold text-gray-800">Temperature Sensors</h5>
            </div>
            {sensorData.temperatures.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Sensor
                      </th>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Reading
                      </th>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {sensorData.temperatures.map((sensor, idx) => (
                      <tr key={idx} className="hover:bg-gray-50">
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-800">{sensor.name}</td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-600">{sensor.reading}</td>
                        <td className={`px-3 py-2 whitespace-nowrap text-sm font-medium ${getStatusColor(sensor.status)}`}>
                          {sensor.status}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <p className="text-sm text-gray-500 italic">No temperature sensor data available</p>
            )}
          </div>
          
          {/* Fan Sensors */}
          <div className="bg-white rounded-lg shadow-sm p-4">
            <div className="flex items-center mb-4">
              <Wind className="w-5 h-5 text-blue-500 mr-2" />
              <h5 className="text-md font-semibold text-gray-800">Fan Sensors</h5>
            </div>
            {sensorData.fans.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Sensor
                      </th>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Reading
                      </th>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {sensorData.fans.map((sensor, idx) => (
                      <tr key={idx} className="hover:bg-gray-50">
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-800">{sensor.name}</td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-600">{sensor.reading}</td>
                        <td className={`px-3 py-2 whitespace-nowrap text-sm font-medium ${getStatusColor(sensor.status)}`}>
                          {sensor.status}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <p className="text-sm text-gray-500 italic">No fan sensor data available</p>
            )}
          </div>
          
          {/* Power Sensors */}
          <div className="bg-white rounded-lg shadow-sm p-4">
            <div className="flex items-center mb-4">
              <Battery className="w-5 h-5 text-green-500 mr-2" />
              <h5 className="text-md font-semibold text-gray-800">Power Consumption</h5>
            </div>
            {sensorData.power.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Sensor
                      </th>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Reading
                      </th>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {sensorData.power.map((sensor, idx) => (
                      <tr key={idx} className="hover:bg-gray-50">
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-800">{sensor.name}</td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-600">{sensor.reading}</td>
                        <td className={`px-3 py-2 whitespace-nowrap text-sm font-medium ${getStatusColor(sensor.status)}`}>
                          {sensor.status}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <p className="text-sm text-gray-500 italic">No power consumption data available</p>
            )}
          </div>
          
          {/* Voltage Sensors */}
          <div className="bg-white rounded-lg shadow-sm p-4">
            <div className="flex items-center mb-4">
              <Activity className="w-5 h-5 text-yellow-500 mr-2" />
              <h5 className="text-md font-semibold text-gray-800">Voltage Sensors</h5>
            </div>
            {sensorData.voltages.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Sensor
                      </th>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Reading
                      </th>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {sensorData.voltages.map((sensor, idx) => (
                      <tr key={idx} className="hover:bg-gray-50">
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-800">{sensor.name}</td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-600">{sensor.reading}</td>
                        <td className={`px-3 py-2 whitespace-nowrap text-sm font-medium ${getStatusColor(sensor.status)}`}>
                          {sensor.status}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <p className="text-sm text-gray-500 italic">No voltage sensor data available</p>
            )}
          </div>
          
          {/* Other Sensors */}
          {sensorData.others.length > 0 && (
            <div className="bg-white rounded-lg shadow-sm p-4 lg:col-span-2">
              <div className="flex items-center mb-4">
                <Clock className="w-5 h-5 text-purple-500 mr-2" />
                <h5 className="text-md font-semibold text-gray-800">Other Sensors</h5>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Sensor
                      </th>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Reading
                      </th>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {sensorData.others.map((sensor, idx) => (
                      <tr key={idx} className="hover:bg-gray-50">
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-800">{sensor.name}</td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-600">{sensor.reading}</td>
                        <td className={`px-3 py-2 whitespace-nowrap text-sm font-medium ${getStatusColor(sensor.status)}`}>
                          {sensor.status}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      )}

      {/* No data message */}
      {!loading && idracConnected && 
       sensorData.temperatures.length === 0 && 
       sensorData.fans.length === 0 && 
       sensorData.power.length === 0 && 
       sensorData.voltages.length === 0 && 
       sensorData.others.length === 0 && (
        <div className="bg-white rounded-lg shadow-sm p-6 text-center">
          <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-3" />
          <h5 className="text-lg font-medium text-gray-800 mb-2">No Sensor Data Available</h5>
          <p className="text-gray-600">
            No sensor information could be retrieved from the iDRAC. This might be due to an unsupported iDRAC version or hardware configuration.
          </p>
          <button
            onClick={() => fetchSensorData(true)} // Force bypass cache
            className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Try Again
          </button>
        </div>
      )}
    </div>
  );
};

export default SensorInfoTab;