import React, { useState, useEffect } from 'react';
import {
  XCircle,
  Server,
  Globe,
  Clock,
  HardDrive,
  Cpu,
  Wifi,
  Network,
  Settings,
  Save,
  AlertTriangle,
  RefreshCw,
  ExternalLink
} from 'lucide-react';
import ServerDetailsModal from './ServerDetailsModal';

const ServiceDetailModal = ({ isOpen, onClose, serviceId, API_BASE_URL }) => {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [service, setService] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [formData, setFormData] = useState({});

  // State for server details modal
  const [serverDetailsOpen, setServerDetailsOpen] = useState(false);
  const [selectedServer, setSelectedServer] = useState(null);
  const [serverDetailsEditMode, setServerDetailsEditMode] = useState(false);

  // State for dropdown options
  const [options, setOptions] = useState({
    cpus: [],
    storages: [],
    bandwidths: [],
    cities: [], // for locations
    subnets: [],
    loadingOptions: false
  });

  // Fetch service details when modal opens and serviceId changes
  useEffect(() => {
    if (isOpen && serviceId) {
      // Disable edit mode when loading new service
      setEditMode(false);
      fetchServiceDetails(serviceId);
      fetchServerOptions();
    }
  }, [isOpen, serviceId]);

  // Function to fetch server options (CPUs, storage, bandwidth, locations, subnets)
  const fetchServerOptions = async () => {
    try {
      setOptions(prev => ({ ...prev, loadingOptions: true }));

      const token = localStorage.getItem('admin_token');
      if (!token) {
        throw new Error("Authentication token not found. Please log in again.");
      }

      const response = await fetch(`${API_BASE_URL}/api_admin_server_config.php?f=get_server_options`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch server options');
      }

      console.log('Received server options:', data.options);

      setOptions({
        cpus: data.options.cpus || [],
        storages: data.options.storages || [],
        bandwidths: data.options.bandwidths || [],
        cities: data.options.cities || [], // for locations
        subnets: data.options.subnets || [],
        loadingOptions: false
      });
    } catch (err) {
      console.error("Error fetching server options:", err);
      setOptions(prev => ({ ...prev, loadingOptions: false }));
    }
  };

  const fetchServiceDetails = async (id) => {
    try {
      // Only set loading to true if we're not already saving
      if (!saving) {
        setLoading(true);
      }
      setError(null);

      const token = localStorage.getItem('admin_token');
      if (!token) {
        throw new Error("Authentication token not found. Please log in again.");
      }

      console.log(`Fetching service details for ID: ${id}`);

      // Make sure we're using a valid ID format
      const serviceId = parseInt(id, 10);
      if (isNaN(serviceId)) {
        throw new Error(`Invalid service ID format: ${id}`);
      }

      const response = await fetch(`${API_BASE_URL}/api_admin_orders.php?f=get_service_by_id`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          service_id: serviceId
        })
      });

      if (!response.ok) {
        // Handle specific HTTP error codes
        if (response.status === 404) {
          throw new Error(`Service with ID ${id} not found. Please check if this is the correct service ID.`);
        } else {
          throw new Error(`HTTP error ${response.status}`);
        }
      }

      // Log the raw response for debugging
      const responseText = await response.text();
      console.log(`Raw service details response for ID ${id}:`, responseText);

      // Try to parse the JSON response
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Error parsing service details JSON:', parseError);
        throw new Error(`Invalid JSON response from server: ${responseText.substring(0, 100)}...`);
      }

      if (!data.success) {
        throw new Error(data.error || `Failed to fetch service details for ID ${id}`);
      }

      console.log('Received service data:', data.service);

      // Verify that the returned service has the expected ID
      if (data.service && data.service.id && parseInt(data.service.id, 10) !== parseInt(id, 10)) {
        console.warn(`Warning: Requested service ID ${id} but received service with ID ${data.service.id}`);
      }

      setService(data.service);

      // Initialize form data with current values
      setFormData({
        cpu_id: data.service.cpu_id || '',
        storage_id: data.service.storage_id || '',
        bandwidth_id: data.service.bandwidth_id || '',
        location_id: data.service.location_id || '',
        subnet_id: data.service.subnet_id || '',
        order_price: data.service.order_price || '',
        requirement_price: data.service.requirement_price || '',
        payment_period: data.service.payment_period || 'monthly',
        hostname: data.service.hostname || '',
        server_id: data.service.server_id || '',
        due_date: data.service.due_date || '',
        status: data.service.status || 'Active'
      });

      // If there's a server ID, fetch the server details to get the label
      if (data.service.server_id) {
        try {
          await fetchServerDetails(data.service.server_id, false); // Pass false to not open the modal
        } catch (serverErr) {
          console.error("Error fetching server details during service load:", serverErr);
          // Don't fail the whole service load if server details can't be fetched
        }
      }
    } catch (err) {
      console.error("Error fetching service details:", err);
      setError(`Failed to load service details: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSaveChanges = async () => {
    try {
      setSaving(true);

      const token = localStorage.getItem('admin_token');
      if (!token) {
        throw new Error("Authentication token not found. Please log in again.");
      }

      // Create request payload
      const payload = {
        token: token,
        service_id: serviceId,
        ...formData
      };

      console.log('Saving service changes:', payload);

      const response = await fetch(`${API_BASE_URL}/api_admin_orders.php?f=update_service_details`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to update service details');
      }

      console.log('Service update response:', data);

      // Update local service state with the updated data
      // If the API doesn't return the display names, we need to find them from our options
      // Preserve existing service data and merge with updates
      const updatedService = { ...service, ...data.service };

      // Find and add CPU display name if needed
      if (formData.cpu_id && (!updatedService.cpu || updatedService.cpu === 'Not specified')) {
        const selectedCpu = options.cpus.find(cpu => cpu.id.toString() === formData.cpu_id.toString());
        if (selectedCpu) {
          updatedService.cpu = selectedCpu.cpu;
        }
      }

      // Find and add Storage display name if needed
      if (formData.storage_id && (!updatedService.storage || updatedService.storage === 'Not specified')) {
        const selectedStorage = options.storages.find(storage => storage.id.toString() === formData.storage_id.toString());
        if (selectedStorage) {
          updatedService.storage = selectedStorage.name;
        }
      }

      // Find and add Bandwidth display name if needed
      if (formData.bandwidth_id && (!updatedService.bandwidth || updatedService.bandwidth === 'Not specified')) {
        const selectedBandwidth = options.bandwidths.find(bandwidth => bandwidth.id.toString() === formData.bandwidth_id.toString());
        if (selectedBandwidth) {
          updatedService.bandwidth = selectedBandwidth.name;
        }
      }

      // Find and add Location display name if needed
      if (formData.location_id && (!updatedService.location || updatedService.location === 'Not specified')) {
        const selectedLocation = options.cities.find(city => city.id.toString() === formData.location_id.toString());
        if (selectedLocation) {
          updatedService.location = selectedLocation.name;
        }
      }

      // Find and add Subnet display name if needed
      if (formData.subnet_id && (!updatedService.subnet || updatedService.subnet === 'Not specified')) {
        const selectedSubnet = options.subnets.find(subnet => subnet.id.toString() === formData.subnet_id.toString());
        if (selectedSubnet) {
          updatedService.subnet = selectedSubnet.name;
        }
      }

      // Update due_date if it was changed
      if (formData.due_date) {
        // Store the date in a format that will display correctly
        const dateParts = formData.due_date.split('-');
        if (dateParts.length === 3) {
          // Convert from YYYY-MM-DD to a format that will display as DD/MM/YYYY
          const year = dateParts[0];
          const month = dateParts[1];
          const day = dateParts[2];

          // Create a date object and store it in a format our formatDate function can handle
          const dateObj = new Date(`${year}-${month}-${day}T00:00:00`);
          if (!isNaN(dateObj.getTime())) {
            updatedService.due_date = dateObj.toISOString();
          } else {
            updatedService.due_date = formData.due_date;
          }
        } else {
          updatedService.due_date = formData.due_date;
        }
      }

      console.log('Updated service with display names:', updatedService);
      setService(updatedService);

      // Exit edit mode
      setEditMode(false);

      alert('Service details updated successfully!');
    } catch (err) {
      console.error("Error updating service details:", err);
      setError(`Failed to update service: ${err.message}`);
      alert(`Error: ${err.message}`);
    } finally {
      setSaving(false);
    }
  };

  const formatDate = (dateString) => {
    try {
      if (!dateString) return 'N/A';
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Invalid date';

      // Format as DD/MM/YYYY
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Month is 0-indexed
      const year = date.getFullYear();

      return `${day}/${month}/${year}`;
    } catch (e) {
      console.error('Date formatting error:', e, dateString);
      return 'Invalid date';
    }
  };

  // Helper function to format date for input field (YYYY-MM-DD)
  const formatDateForInput = (dateString) => {
    try {
      if (!dateString) return '';
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '';

      // Format as YYYY-MM-DD for input field
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Month is 0-indexed
      const year = date.getFullYear();

      return `${year}-${month}-${day}`;
    } catch (e) {
      console.error('Date formatting error for input:', e, dateString);
      return '';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg max-w-3xl w-full max-h-[90vh] overflow-y-auto">
        {/* Modal Header */}
        <div className="p-6 border-b flex justify-between items-center">
          <h2 className="text-xl font-bold text-gray-800">
            {error ? 'Error Loading Service' :
             `Service Details: ${service?.hostname || `ID: ${serviceId}`}`}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <XCircle className="w-5 h-5" />
          </button>
        </div>

        {/* Modal Content */}
        <div className="p-6 space-y-6">
          {error ? (
            <div className="text-center p-8 text-red-600">
              <AlertTriangle className="w-12 h-12 mx-auto mb-4" />
              <p className="mb-4">{error}</p>
              <div className="mb-4 text-gray-700 text-sm">
                <p>If you're seeing a "Service not found" error, it might be because:</p>
                <ul className="list-disc text-left ml-8 mt-2">
                  <li>The service ID is incorrect</li>
                  <li>The service has been deleted</li>
                  <li>The service ID format is not what the API expects</li>
                </ul>
              </div>
              <div className="flex justify-center space-x-4">
                <button
                  onClick={() => fetchServiceDetails(serviceId)}
                  className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
                >
                  <RefreshCw className="w-4 h-4 inline mr-2" />
                  Retry
                </button>
                <button
                  onClick={onClose}
                  className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
                >
                  <XCircle className="w-4 h-4 inline mr-2" />
                  Close
                </button>
              </div>
            </div>
          ) : (
            <>
              {/* Service Header */}
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center border-b pb-4">
                <div>
                  <div className="text-2xl font-bold text-gray-800">
                    <div className="flex items-center">
                      <Server className="w-5 h-5 mr-2 text-indigo-700" />
                      {service?.hostname || `Service #${serviceId}`}
                    </div>
                  </div>
                  <div className="text-sm text-gray-500 mt-1">
                    ID: {service?.id || serviceId} {service?.database_id ? `(DB ID: ${service.database_id})` : ''}
                  </div>
                </div>
                <div className="mt-2 md:mt-0 flex gap-2">
                  {/* Status Badge/Dropdown */}
                  {editMode ? (
                    <div className="relative">
                      <select
                        name="status"
                        value={formData.status}
                        onChange={handleInputChange}
                        className="px-2 py-1 rounded-md text-xs font-medium border appearance-none pr-6"
                      >
                        <option value="Active">Active</option>
                        <option value="Pending">Pending</option>
                        <option value="Suspended">Suspended</option>
                        <option value="Cancelled">Cancelled</option>
                      </select>
                      <div className="absolute right-1 top-1/2 -translate-y-1/2 pointer-events-none">
                        <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                      </div>
                    </div>
                  ) : (
                    <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit ${
                      service?.status === 'Active' ? 'bg-green-100 text-green-800' :
                      service?.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
                      service?.status === 'Suspended' ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {service?.status || (loading && !saving ? 'Loading...' : 'Unknown Status')}
                    </span>
                  )}

                  {/* Edit/Save Button */}
                  <button
                    onClick={() => editMode ? handleSaveChanges() : setEditMode(true)}
                    className={`px-3 py-1 rounded-md text-xs font-medium flex items-center ${
                      editMode ? 'bg-green-600 text-white' : 'bg-indigo-600 text-white'
                    } ${(loading || saving) ? 'opacity-70 cursor-wait' : ''}`}
                    disabled={loading || saving}
                  >
                    {editMode ? (
                      <>
                        {saving ? (
                          <>
                            <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="w-3 h-3 mr-1" />
                            Save Changes
                          </>
                        )}
                      </>
                    ) : (
                      <>
                        <Settings className="w-3 h-3 mr-1" />
                        Edit Details
                      </>
                    )}
                  </button>
                </div>
              </div>

              {/* Service Details in Columns */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Column 1: Technical Details */}
                <div>
                  <h3 className="text-lg font-bold mb-2">Technical Specifications</h3>
                  <div className="bg-gray-50 rounded-md p-4 space-y-4">
                    {/* CPU */}
                    <div>
                      <div className="text-sm text-gray-500 mb-1 flex items-center">
                        <Cpu className="w-4 h-4 mr-1" />
                        CPU
                      </div>
                      {editMode ? (
                        <div className="relative">
                          {options.loadingOptions && (
                            <div className="absolute right-2 top-1/2 -translate-y-1/2">
                              <RefreshCw className="w-4 h-4 animate-spin text-gray-400" />
                            </div>
                          )}
                          <select
                            name="cpu_id"
                            value={formData.cpu_id}
                            onChange={handleInputChange}
                            className="w-full p-2 border rounded-md text-sm appearance-none pr-8"
                            disabled={options.loadingOptions}
                          >
                            <option value="">Select CPU</option>
                            {options.cpus.map(cpu => (
                              <option key={cpu.id} value={cpu.id}>
                                {cpu.cpu}
                              </option>
                            ))}
                          </select>
                        </div>
                      ) : (
                        <div className="font-medium">{service?.cpu || (loading && !saving ? 'Loading...' : 'Not specified')}</div>
                      )}
                    </div>

                    {/* Storage */}
                    <div>
                      <div className="text-sm text-gray-500 mb-1 flex items-center">
                        <HardDrive className="w-4 h-4 mr-1" />
                        Storage
                      </div>
                      {editMode ? (
                        <div className="relative">
                          {options.loadingOptions && (
                            <div className="absolute right-2 top-1/2 -translate-y-1/2">
                              <RefreshCw className="w-4 h-4 animate-spin text-gray-400" />
                            </div>
                          )}
                          <select
                            name="storage_id"
                            value={formData.storage_id}
                            onChange={handleInputChange}
                            className="w-full p-2 border rounded-md text-sm appearance-none pr-8"
                            disabled={options.loadingOptions}
                          >
                            <option value="">Select Storage</option>
                            {options.storages.map(storage => (
                              <option key={storage.id} value={storage.id}>
                                {storage.name}
                              </option>
                            ))}
                          </select>
                        </div>
                      ) : (
                        <div className="font-medium">{service?.storage || (loading && !saving ? 'Loading...' : 'Not specified')}</div>
                      )}
                    </div>

                    {/* Bandwidth */}
                    <div>
                      <div className="text-sm text-gray-500 mb-1 flex items-center">
                        <Wifi className="w-4 h-4 mr-1" />
                        Bandwidth
                      </div>
                      {editMode ? (
                        <div className="relative">
                          {options.loadingOptions && (
                            <div className="absolute right-2 top-1/2 -translate-y-1/2">
                              <RefreshCw className="w-4 h-4 animate-spin text-gray-400" />
                            </div>
                          )}
                          <select
                            name="bandwidth_id"
                            value={formData.bandwidth_id}
                            onChange={handleInputChange}
                            className="w-full p-2 border rounded-md text-sm appearance-none pr-8"
                            disabled={options.loadingOptions}
                          >
                            <option value="">Select Bandwidth</option>
                            {options.bandwidths.map(bandwidth => (
                              <option key={bandwidth.id} value={bandwidth.id}>
                                {bandwidth.name}
                              </option>
                            ))}
                          </select>
                        </div>
                      ) : (
                        <div className="font-medium">{service?.bandwidth || (loading && !saving ? 'Loading...' : 'Not specified')}</div>
                      )}
                    </div>

                    {/* Subnet */}
                    <div>
                      <div className="text-sm text-gray-500 mb-1 flex items-center">
                        <Network className="w-4 h-4 mr-1" />
                        Subnet
                      </div>
                      {editMode ? (
                        <div className="relative">
                          {options.loadingOptions && (
                            <div className="absolute right-2 top-1/2 -translate-y-1/2">
                              <RefreshCw className="w-4 h-4 animate-spin text-gray-400" />
                            </div>
                          )}
                          <select
                            name="subnet_id"
                            value={formData.subnet_id}
                            onChange={handleInputChange}
                            className="w-full p-2 border rounded-md text-sm appearance-none pr-8"
                            disabled={options.loadingOptions}
                          >
                            <option value="">Select Subnet</option>
                            {options.subnets.map(subnet => (
                              <option key={subnet.id} value={subnet.id}>
                                {subnet.name}
                              </option>
                            ))}
                          </select>
                        </div>
                      ) : (
                        <div className="font-medium">{service?.subnet || (loading && !saving ? 'Loading...' : 'Not specified')}</div>
                      )}
                    </div>

                    {/* Location */}
                    <div>
                      <div className="text-sm text-gray-500 mb-1 flex items-center">
                        <Globe className="w-4 h-4 mr-1" />
                        Location
                      </div>
                      {editMode ? (
                        <div className="relative">
                          {options.loadingOptions && (
                            <div className="absolute right-2 top-1/2 -translate-y-1/2">
                              <RefreshCw className="w-4 h-4 animate-spin text-gray-400" />
                            </div>
                          )}
                          <select
                            name="location_id"
                            value={formData.location_id}
                            onChange={handleInputChange}
                            className="w-full p-2 border rounded-md text-sm appearance-none pr-8"
                            disabled={options.loadingOptions}
                          >
                            <option value="">Select Location</option>
                            {options.cities.map(city => (
                              <option key={city.id} value={city.id}>
                                {city.name}
                              </option>
                            ))}
                          </select>
                        </div>
                      ) : (
                        <div className="font-medium">{service?.location || (loading && !saving ? 'Loading...' : 'Not specified')}</div>
                      )}
                    </div>

                    {/* Server ID */}
                    <div>
                      <div className="text-sm text-gray-500 mb-1 flex items-center">
                        <Server className="w-4 h-4 mr-1" />
                        Server
                      </div>
                      {editMode ? (
                        <input
                          type="text"
                          name="server_id"
                          value={formData.server_id}
                          onChange={handleInputChange}
                          className="w-full p-2 border rounded-md text-sm"
                          placeholder="Server ID"
                        />
                      ) : (
                        <div className="font-medium">
                          {service?.server_id ? (
                            <button
                              onClick={() => handleOpenServerDetails(service.server_id)}
                              className="text-indigo-700 hover:text-indigo-900 flex items-center"
                            >
                              {service.server_label ? `${service.server_label} #${service.server_id}` : `Server #${service.server_id}`}
                              <ExternalLink className="w-3 h-3 ml-1" />
                            </button>
                          ) : 'Not assigned'}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Column 2: Billing & Administrative */}
                <div>
                  <h3 className="text-lg font-bold mb-2">Billing & Administration</h3>
                  <div className="bg-gray-50 rounded-md p-4 space-y-4">
                    {/* Hostname */}
                    <div>
                      <div className="text-sm text-gray-500 mb-1">Hostname</div>
                      {editMode ? (
                        <input
                          type="text"
                          name="hostname"
                          value={formData.hostname}
                          onChange={handleInputChange}
                          className="w-full p-2 border rounded-md text-sm"
                          placeholder="Hostname"
                        />
                      ) : (
                        <div className="font-medium">{service?.hostname || (loading && !saving ? 'Loading...' : 'Not specified')}</div>
                      )}
                    </div>

                    {/* Pricing */}
                    <div>
                      <div className="text-sm text-gray-500 mb-1">One-time Price</div>
                      {editMode ? (
                        <input
                          type="text"
                          name="order_price"
                          value={formData.order_price}
                          onChange={handleInputChange}
                          className="w-full p-2 border rounded-md text-sm"
                          placeholder="Order Price"
                        />
                      ) : (
                        <div className="font-medium">{service?.price || (loading && !saving ? 'Loading...' : 'Not specified')}</div>
                      )}
                    </div>

                    {/* Recurring Price */}
                    <div>
                      <div className="text-sm text-gray-500 mb-1">Recurring Price</div>
                      {editMode ? (
                        <input
                          type="text"
                          name="requirement_price"
                          value={formData.requirement_price}
                          onChange={handleInputChange}
                          className="w-full p-2 border rounded-md text-sm"
                          placeholder="Recurring Price"
                        />
                      ) : (
                        <div className="font-medium">{service?.recurring_price || (loading && !saving ? 'Loading...' : 'Not specified')}</div>
                      )}
                    </div>

                    {/* Payment Period */}
                    <div>
                      <div className="text-sm text-gray-500 mb-1">Payment Period</div>
                      {editMode ? (
                        <select
                          name="payment_period"
                          value={formData.payment_period}
                          onChange={handleInputChange}
                          className="w-full p-2 border rounded-md text-sm"
                        >
                          <option value="monthly">Monthly</option>
                          <option value="quarterly">Quarterly</option>
                          <option value="semi-annual">Semi-Annual</option>
                          <option value="annual">Annual</option>
                        </select>
                      ) : (
                        <div className="font-medium">
                          {service?.payment_period ?
                            service.payment_period.charAt(0).toUpperCase() + service.payment_period.slice(1) :
                            (loading && !saving ? 'Loading...' : 'Monthly')}
                        </div>
                      )}
                    </div>

                    {/* Order Date */}
                    <div>
                      <div className="text-sm text-gray-500 mb-1 flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        Order Date
                      </div>
                      <div className="font-medium">{service ? formatDate(service.date_ordered || service.created) : (loading && !saving ? 'Loading...' : 'N/A')}</div>
                    </div>

                    {/* Due Date */}
                    <div>
                      <div className="text-sm text-gray-500 mb-1 flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        Due Date
                      </div>
                      {editMode ? (
                        <input
                          type="date"
                          name="due_date"
                          value={formatDateForInput(formData.due_date)}
                          onChange={handleInputChange}
                          className="w-full p-2 border rounded-md text-sm"
                        />
                      ) : (
                        <div className="font-medium">{service ? formatDate(service.due_date) : (loading && !saving ? 'Loading...' : 'N/A')}</div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
             {/*
            Debug Info - Only in development
              {process.env.NODE_ENV === 'development' && (
                <div className="mt-8 p-4 border border-gray-200 rounded-md">
                  <h4 className="text-sm font-bold text-gray-700 mb-2">Debug Information</h4>
                  <pre className="text-xs overflow-auto bg-gray-50 p-2 rounded">
                    {JSON.stringify(service, null, 2)}
                  </pre>
                </div>
              )} */}
            </>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Close
          </button>
        </div>
      </div>

      {/* Server Details Modal */}
      {serverDetailsOpen && selectedServer && (
        <ServerDetailsModal
          selectedItem={selectedServer}
          isEditMode={serverDetailsEditMode}
          setIsEditMode={setServerDetailsEditMode}
          selectedTab={selectedServer.server_type || 'dedicated'} // Use server_type from API response
          handleEditItemChange={() => {}} // Not needed for view-only
          handleUpdateItem={() => {}} // Not needed for view-only
          closeItemDetails={() => setServerDetailsOpen(false)}
          navigateTo={(url) => window.location.href = url}
          handleUpdateStorage={() => {}} // Not needed for view-only
          handleUpdateMac={() => {}} // Not needed for view-only
          handleUnallocateSubnet={() => {}} // Not needed for view-only
          discoverSwitchPorts={() => {}} // Not needed for view-only
          fetchSwitchPorts={() => {}} // Not needed for view-only
          visiblePasswords={{}} // Not needed for view-only
          togglePasswordVisibility={() => {}} // Not needed for view-only
          getCountryFlag={(country) => country ? `🏳️` : ''} // Simple flag placeholder
          getDatacenterName={(id) => 'Datacenter'} // Simple datacenter placeholder
          isSubnetSelectionModalOpen={false}
          setIsSubnetSelectionModalOpen={() => {}}
          isSelectingMainSubnet={false}
          setIsSelectingMainSubnet={() => {}}
          handleSubnetSelection={() => {}}
          ramConfigurations={[]}
          cpuModels={[]}
          switchModels={[]}
          setShowAddCpuModal={() => {}}
          setShowAddRamModal={() => {}}
          setShowAddSwitchModelModal={() => {}}
          storageConfigurations={[]}
          cities={[]}
          countries={[]}
          racks={[]}
          chassis={[]}
          bladeServers={[]}
          portsRefreshKey={0}
        />
      )}
    </div>
  );

  // Function to handle opening server details
  function handleOpenServerDetails(serverId) {
    // Fetch server details and open the modal
    fetchServerDetails(serverId, true);
  }

  // Function to fetch server details
  async function fetchServerDetails(serverId, openModal = true) {
    try {
      const token = localStorage.getItem('admin_token');
      if (!token) {
        throw new Error("Authentication token not found. Please log in again.");
      }

      console.log(`Fetching server details for ID: ${serverId}`);

      // Fetch server details from API
      const response = await fetch(`${API_BASE_URL}/api_admin_inventory.php?f=get_server_by_id`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          server_id: serverId
        })
      });

      const data = await response.json();
      console.log('Server details response:', data);

      if (!response.ok) {
        // Handle 404 Not Found with a specific message
        if (response.status === 404) {
          throw new Error(data.error || 'Server not found');
        } else {
          throw new Error(`HTTP error ${response.status}: ${data.error || 'Unknown error'}`);
        }
      }

      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch server details');
      }

      // Set the server data
      setSelectedServer(data.server);

      // Only open the modal if requested
      if (openModal) {
        setServerDetailsOpen(true);
      }

      // Update the service state with the server label
      if (data.server && data.server.label) {
        setService(prevService => ({
          ...prevService,
          server_label: data.server.label
        }));
      }
    } catch (err) {
      console.error("Error fetching server details:", err);
      if (openModal) {
        alert(`Failed to load server details: ${err.message}`);
      }
    }
  }
};

export default ServiceDetailModal;