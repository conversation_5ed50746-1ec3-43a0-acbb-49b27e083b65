// iDracVersionDetector.js
/**
 * Utility to detect iDRAC version from server model or manual detection
 */

/**
 * Detect iDRAC version from server model information
 * @param {Object} server - Server object with model, CPU, or notes information
 * @returns {number|null} - Returns 8 or 9 for detected version, 8 as default if unsure
 */
import { API_URL } from '../config';
export const detectIdracVersionFromModel = (server) => {
  if (!server) return 8; // Default to 8 if no server info available
  
  // Combine information for pattern matching
  const serverInfo = [
    server.label || '',
    server.cpu || '',
    server.notes || ''
  ].join(' ').toLowerCase();
  
  // Define patterns for different iDRAC versions
  const patterns = [
    // iDRAC 9 patterns (14th-19th generation Dell servers)
    { pattern: /r[0-9]40/i, version: 9 },    // R740, R640, R440, etc.
    { pattern: /r[0-9]50/i, version: 9 },    // R750, R650, R550, etc.
    { pattern: /poweredge\s*1[4-9]/i, version: 9 },  // PowerEdge 14G+
    { pattern: /g1[4-9]/i, version: 9 },     // Dell G14+ servers
    { pattern: /idrac ?9/i, version: 9 },    // Explicit iDRAC 9 mention
    { pattern: /icy ?scales/i, version: 9 }, // iDRAC 9 UI name
    
    // iDRAC 8 patterns (12th-13th generation Dell servers)
    { pattern: /r[0-9]30/i, version: 8 },    // R730, R630, R530, etc.
    { pattern: /r[0-9]20/i, version: 8 },    // R720, R620, R520, etc.
    { pattern: /poweredge\s*1[23]/i, version: 8 },   // PowerEdge 12G-13G
    { pattern: /g1[23]/i, version: 8 },      // Dell G12-G13 servers
    { pattern: /idrac ?8/i, version: 8 },    // Explicit iDRAC 8 mention
  ];
  
  // Check against patterns
  for (const { pattern, version } of patterns) {
    if (pattern.test(serverInfo)) {
      return version;
    }
  }
  
  // Default to iDRAC 8 if no pattern matches
  return 8;
};

/**
 * Detect iDRAC version by trying to connect to specific API endpoints
 * @param {string} ipmiAddress - The IP address of the iDRAC interface
 * @returns {Promise<number>} - Returns 8 or 9 for detected version
 */
export const detectIdracVersionFromApi = async (ipmiAddress) => {
  if (!ipmiAddress) return 8; // Default to 8 if no address provided
  
  const cleanAddress = ipmiAddress.replace(/^https?:\/\//, '');
  
  try {
    // Try iDRAC 9 Redfish API first (more recent)
    const response = await fetch(`https://${cleanAddress}/redfish/v1/Systems/System.Embedded.1`, {
      method: 'GET',
      headers: { 'Accept': 'application/json' },
      // This is important for cross-origin requests
      mode: 'no-cors',
      // Set a timeout to avoid long waits
      signal: AbortSignal.timeout(5000)
    });
    
    // If response is successful, likely iDRAC 9
    if (response.ok) {
      return 9;
    }
  } catch (error) {
    console.log('Error detecting iDRAC 9:', error);
  }
  
  try {
    // Try iDRAC 8 API
    const response = await fetch(`https://${cleanAddress}/sysmgmt/2015/server/inventory`, {
      method: 'GET',
      headers: { 'Accept': 'application/json' },
      mode: 'no-cors',
      signal: AbortSignal.timeout(5000)
    });
    
    // If response is successful, likely iDRAC 8
    if (response.ok) {
      return 8;
    }
  } catch (error) {
    console.log('Error detecting iDRAC 8:', error);
  }
  
  // If we couldn't determine, default to iDRAC 8
  return 8;
};

/**
 * Detect iDRAC version using server-side SSH check
 * @param {string} ipmiAddress - The IP address of the iDRAC interface
 * @param {string} username - The username for iDRAC
 * @param {string} password - The password for iDRAC
 * @returns {Promise<number>} - Returns detected version (8 or 9)
 */
export const detectIdracVersionFromSsh = async (ipmiAddress, username = 'root', password = '') => {
  if (!ipmiAddress) return 8;
  
  try {
    const token = localStorage.getItem('admin_token');
    
    const response = await fetch('${API_URL}/api.php?f=detect_idrac_version', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: token,
        ipmi_address: ipmiAddress,
        ipmi_username: username,
        ipmi_password: password
      })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }
    
    const data = await response.json();
    
    if (data.success) {
      return data.idrac_version;
    } else {
      console.error("Error detecting iDRAC version:", data.error);
      // Fall back to default
      return 8;
    }
  } catch (error) {
    console.error("Error in SSH version detection:", error);
    return 8;
  }
};