import React, { useState, useEffect } from 'react';
import { XCircle, AlertCircle, CheckCircle } from 'lucide-react';
import axios from 'axios';

const AddSubnetModal = ({ onClose, onAddSubnet }) => {
  // State for form data
  const [formData, setFormData] = useState({
    ipVersion: 'ipv4',  // Default to IPv4
    subnet: '',
    country_id: '',
    city_id: '',
    is_public: 'no',    // Radio button for public/privat
    subnet_type: 'root' // Radio button for subnet type (root, customer, etc.)
  });
  
  // Additional state
  const [countries, setCountries] = useState([]);
  const [cities, setCities] = useState([]);
  const [filteredCities, setFilteredCities] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [validationMessage, setValidationMessage] = useState('');
  const [isValid, setIsValid] = useState(false);
  
  // Fetch countries on component mount
  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const token = localStorage.getItem('admin_token');
        if (!token) {
          console.error('No admin token found');
          setError('Authentication required. Please log in again.');
          return;
        }
        
        const response = await axios.post(`${API_URL}/api_admin_inventory.php?f=get_countries', {
          token: token
        });
        
        if (Array.isArray(response.data)) {
          setCountries(response.data);
        } else if (response.data && typeof response.data === 'object') {
          // If response is an object but not an array, convert it to an array
          const countriesArray = Object.values(response.data);
          // Make sure each item is actually a country object with an id
          const validCountries = countriesArray.filter(item => 
            item && typeof item === 'object' && item.id
          );
          setCountries(validCountries);
        } else {
          console.error('Unexpected countries data format:', response.data);
          setCountries([]);
        }
      } catch (err) {
        console.error('Error fetching countries:', err);
        setError('Failed to load countries. Please try again.');
        setCountries([]);
      }
    };
    
    fetchCountries();
  }, []);
  
  // Fetch cities on component mount
  useEffect(() => {
    const fetchCities = async () => {
      try {
        const token = localStorage.getItem('admin_token');
        if (!token) {
          console.error('No admin token found');
          setError('Authentication required. Please log in again.');
          return;
        }
        
        const response = await axios.post(`${API_URL}/api_admin_inventory.php?f=get_cities', {
          token: token
        });
        
        if (Array.isArray(response.data)) {
          setCities(response.data);
        } else if (response.data && typeof response.data === 'object') {
          // If response is an object but not an array, convert it to an array
          const citiesArray = Object.values(response.data);
          // Make sure each item is actually a city object with an id
          const validCities = citiesArray.filter(item => 
            item && typeof item === 'object' && item.id
          );
          setCities(validCities);
        } else {
          console.error('Unexpected cities data format:', response.data);
          setCities([]);
        }
      } catch (err) {
        console.error('Error fetching cities:', err);
        setError('Failed to load cities. Please try again.');
        setCities([]);
      }
    };
    
    fetchCities();
  }, []);
  
  // Filter cities when country changes
  useEffect(() => {
    if (formData.country_id) {
      const filtered = cities.filter(city => city.country_id === parseInt(formData.country_id));
      setFilteredCities(filtered);
    } else {
      setFilteredCities([]);
    }
  }, [formData.country_id, cities]);
  
  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === 'checkbox' ? checked : value;
    
    setFormData({
      ...formData,
      [name]: newValue
    });
    
    // Validate subnet when it changes
    if (name === 'subnet') {
      validateSubnet(value, formData.ipVersion);
    }
  };
  
  // Handle IP version change
  const handleIpVersionChange = (e) => {
    const newVersion = e.target.value;
    setFormData({
      ...formData,
      ipVersion: newVersion,
      subnet: '' // Reset subnet when changing version
    });
    setValidationMessage('');
    setIsValid(false);
  };
  
  // Validate subnet CIDR notation
  const validateSubnet = (subnet, ipVersion) => {
    if (!subnet) {
      setValidationMessage('Subnet is required');
      setIsValid(false);
      return;
    }
    
    if (ipVersion === 'ipv4') {
      // IPv4 validation
      const cidrPattern = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})\/(\d{1,2})$/;
      const match = subnet.match(cidrPattern);
      
      if (!match) {
        setValidationMessage('Invalid IPv4 CIDR notation (e.g., ***********/24)');
        setIsValid(false);
        return;
      }
      
      // Validate octets
      for (let i = 1; i <= 4; i++) {
        const octet = parseInt(match[i], 10);
        if (octet < 0 || octet > 255) {
          setValidationMessage(`Invalid IP address octet: ${octet}`);
          setIsValid(false);
          return;
        }
      }
      
      // Validate subnet mask
      const mask = parseInt(match[5], 10);
      if (mask < 1 || mask > 32) {
        setValidationMessage('IPv4 subnet mask must be between 1 and 32');
        setIsValid(false);
        return;
      }
      
      // Calculate expected network address
      const ip = [match[1], match[2], match[3], match[4]].map(Number);
      const netmaskBits = parseInt(match[5], 10);
      
      const fullMask = netmaskBits === 32 ? 0xffffffff : ~((1 << (32 - netmaskBits)) - 1);
      const ipNum = (ip[0] << 24) | (ip[1] << 16) | (ip[2] << 8) | ip[3];
      const networkAddr = ipNum & fullMask;
      
      if (networkAddr !== ipNum) {
        const a = (networkAddr >> 24) & 0xff;
        const b = (networkAddr >> 16) & 0xff;
        const c = (networkAddr >> 8) & 0xff;
        const d = networkAddr & 0xff;
        setValidationMessage(`Not a network address. Did you mean ${a}.${b}.${c}.${d}/${netmaskBits}?`);
        setIsValid(false);
        return;
      }
    } else {
      // IPv6 validation - simplified version
      const ipv6CidrPattern = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}\/(\d{1,3})$|^([0-9a-fA-F:]+)\/(\d{1,3})$/;
      const match = subnet.match(ipv6CidrPattern);
      
      if (!match) {
        setValidationMessage('Invalid IPv6 CIDR notation (e.g., 2001:db8::/64)');
        setIsValid(false);
        return;
      }
      
      // Find mask in the match groups
      let mask;
      for (let i = 2; i < match.length; i++) {
        if (match[i] !== undefined && !isNaN(parseInt(match[i]))) {
          mask = parseInt(match[i], 10);
          break;
        }
      }
      
      // Validate subnet mask
      if (!mask || mask < 1 || mask > 128) {
        setValidationMessage('IPv6 subnet mask must be between 1 and 128');
        setIsValid(false);
        return;
      }
    }
    
    setValidationMessage('Valid subnet');
    setIsValid(true);
  };
  
  // Normalize subnet to network address
  const normalizeSubnet = (subnet, ipVersion) => {
    if (ipVersion === 'ipv4') {
      const [ip, mask] = subnet.split('/');
      const octets = ip.split('.').map(Number);
      const prefixLength = parseInt(mask, 10);
      
      // Convert IP to 32-bit number
      const ipNum = (octets[0] << 24) | (octets[1] << 16) | (octets[2] << 8) | octets[3];
      
      // Apply netmask
      const fullMask = prefixLength === 32 ? 0xffffffff : ~((1 << (32 - prefixLength)) - 1);
      const networkAddr = ipNum & fullMask;
      
      // Convert back to dotted notation
      const a = (networkAddr >> 24) & 0xff;
      const b = (networkAddr >> 16) & 0xff;
      const c = (networkAddr >> 8) & 0xff;
      const d = networkAddr & 0xff;
      
      return `${a}.${b}.${c}.${d}/${prefixLength}`;
    } else {
      // For IPv6, we'll just return the original subnet
      // A proper IPv6 normalization is complex and beyond this implementation
      return subnet;
    }
  };
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!isValid) {
      return;
    }
    
    // Normalize subnet to ensure it's the network address
    const normalizedSubnet = normalizeSubnet(formData.subnet, formData.ipVersion);
    
    // Prepare data for submission
    const submitData = {
      ...formData,
      subnet: normalizedSubnet,
      is_root_subnet: formData.subnet_type === 'root' ? 1 : 0,
      is_customer_subnet: formData.subnet_type === 'customer' ? 1 : 0,
      is_managmenet_subnet: formData.subnet_type === 'management' ? 1 : 0,
      is_transit_subnet: formData.subnet_type === 'transit' ? 1 : 0,
      is_public: formData.is_public === 'yes' ? 1 : 0,
    };
    
    setLoading(true);
    setError('');
    
    try {
      await onAddSubnet(submitData);
      onClose();
    } catch (err) {
      setError(err.response?.data?.error || 'An error occurred while adding the subnet');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg max-w-md w-full">
        <div className="p-6 border-b flex justify-between items-center">
          <h2 className="text-xl font-bold text-gray-800">Add New Subnet</h2>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <XCircle className="w-5 h-5" />
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {error && (
            <div className="p-3 bg-red-50 text-red-700 rounded-md flex items-center">
              <AlertCircle className="w-5 h-5 mr-2" />
              {error}
            </div>
          )}
          
          {/* IP Version Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              IP Version *
            </label>
            <select
              name="ipVersion"
              value={formData.ipVersion}
              onChange={handleIpVersionChange}
              className="w-full p-2 border rounded-md"
              required
            >
              <option value="ipv4">IPv4</option>
              <option value="ipv6">IPv6</option>
            </select>
          </div>
          
          {/* Subnet Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Subnet (CIDR notation) *
            </label>
            <input
              type="text"
              name="subnet"
              value={formData.subnet}
              onChange={handleInputChange}
              placeholder={formData.ipVersion === 'ipv4' ? 'e.g., ***********/24' : 'e.g., 2001:db8::/64'}
              className={`w-full p-2 border rounded-md ${
                formData.subnet && (isValid ? 'border-green-500' : 'border-red-500')
              }`}
              required
            />
            {formData.subnet && (
              <div className={`mt-1 text-sm flex items-center ${
                isValid ? 'text-green-600' : 'text-red-600'
              }`}>
                {isValid ? (
                  <CheckCircle className="w-4 h-4 mr-1" />
                ) : (
                  <AlertCircle className="w-4 h-4 mr-1" />
                )}
                {validationMessage}
              </div>
            )}
          </div>
          
          {/* Country Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Country
            </label>
            <select
              name="country_id"
              value={formData.country_id}
              onChange={handleInputChange}
              className="w-full p-2 border rounded-md"
            >
              <option value="">-- Select Country --</option>
              {countries.map(country => (
                <option key={country.id} value={country.id}>
                  {country.country}
                </option>
              ))}
            </select>
          </div>
          
          {/* City Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              City *
            </label>
            <select
              name="city_id"
              value={formData.city_id}
              onChange={handleInputChange}
              className="w-full p-2 border rounded-md"
              required
              disabled={!formData.country_id}
            >
              <option value="">-- Select City --</option>
              {filteredCities.map(city => (
                <option key={city.id} value={city.id}>
                  {city.city} ({city.datacenter})
                </option>
              ))}
            </select>
          </div>
          
          {/* Public/Private Radio */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Public Subnet
            </label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="is_public"
                  value="yes"
                  checked={formData.is_public === 'yes'}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-indigo-600 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">Yes</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="is_public"
                  value="no"
                  checked={formData.is_public === 'no'}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-indigo-600 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">No</span>
              </label>
            </div>
          </div>
          

          
          {/* Subnet Type Radio */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Subnet Type
            </label>
            <div className="grid grid-cols-2 gap-2">

              <label className="flex items-center">
                <input
                  type="radio"
                  name="subnet_type"
                  value="root"
                  checked={formData.subnet_type === 'root'}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-indigo-600 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">Root</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="subnet_type"
                  value="customer"
                  checked={formData.subnet_type === 'customer'}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-indigo-600 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">Customer</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="subnet_type"
                  value="management"
                  checked={formData.subnet_type === 'management'}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-indigo-600 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">Management</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="subnet_type"
                  value="transit"
                  checked={formData.subnet_type === 'transit'}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-indigo-600 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">Transit</span>
              </label>
            </div>
          </div>
          
          <div className="pt-4 border-t flex justify-end">
            <button
              type="button"
              onClick={onClose}
              className="mr-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className={`px-4 py-2 text-sm font-medium text-white rounded-md ${
                isValid && !loading
                  ? 'bg-indigo-700 hover:bg-indigo-800'
                  : 'bg-indigo-400 cursor-not-allowed'
              }`}
              disabled={!isValid || loading}
            >
              {loading ? 'Adding...' : 'Add Subnet'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddSubnetModal;