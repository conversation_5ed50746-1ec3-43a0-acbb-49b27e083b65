import React, { useState, useEffect } from 'react';
import { X, Plus, Trash2, X<PERSON>ircle, Save, RefreshCw, Eye, EyeOff, Network, Wifi, CheckCircle,
  AlertTriangle, ChevronLeft, ChevronRight, Copy, Server, HardDrive, Cpu, Layout, Globe, Memory, Link } from 'lucide-react';
import SwitchPortsManager from './SwitchPortsManager'; // Import the new component
import { API_URL } from '../config';
const BulkDeviceAddition = ({
  isOpen,
  onClose,
  deviceType, // 'blade', 'dedicated', 'chassis', or 'switch'
  onAddItems,
  cities = [],
  racks = [],
  chassis = [],
  switches = [],
  storageConfigurations = []
}) => {
  // State definitions
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [templateItem, setTemplateItem] = useState(null);
  const [errorMessages, setErrorMessages] = useState({});
  const [cpuOptions, setCpuOptions] = useState([]);
  const [ramConfigurations, setRamConfigurations] = useState([]);
  const [passwordVisibility, setPasswordVisibility] = useState({});
  const [activeTab, setActiveTab] = useState("basic");
  const [availablePorts, setAvailablePorts] = useState({});
  const [showAddSwitchModelModal, setShowAddSwitchModelModal] = useState(false);
  const [newSwitchModel, setNewSwitchModel] = useState({ name: '', size: 1 });
  const [selectedDeviceForPorts, setSelectedDeviceForPorts] = useState(null);
  const [processingMacs, setProcessingMacs] = useState(false);
  const [macResults, setMacResults] = useState({});
  const [currentPage, setCurrentPage] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [chassisModels, setChassisModels] = useState([])
  const [switchModels, setSwitchModels] = useState([]);
  const [showAddChassisModelModal, setShowAddChassisModelModal] = useState(false);
  const [newChassisModel, setNewChassisModel] = useState({ name: '', size: 2, bay_count: 4 });
  const [fetchingData, setFetchingData] = useState({
    cpus: false,
    switches: false,
    chassis: false,
    chassisModels: false,
    switchModels: false,
    ramConfigurations: false
  });

  // New state for add new modals
  const [showAddCpuModal, setShowAddCpuModal] = useState(false);
  const [newCpuModel, setNewCpuModel] = useState('');
  const [showAddRamModal, setShowAddRamModal] = useState(false);
  const [newRamConfig, setNewRamConfig] = useState({ size: '', description: '' });


  function fetchChassisModels() {
    console.log('Fetching chassis models...');
    setFetchingData(prev => ({ ...prev, chassisModels: true }));

    const token = localStorage.getItem('admin_token');

    fetch(`${API_URL}/api_admin_inventory.php?f=get_inventory_chassis_models`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ token })
    })
    .then(response => {
      if (!response.ok) throw new Error(`HTTP error ${response.status}`);
      return response.json();
    })
    .then(data => {
      console.log('Chassis models received:', data);
      if (Array.isArray(data)) {
        setChassisModels(data);
      } else {
        console.warn('Data is not an array, using fallback data');
        // Fall back to some common chassis models
        setChassisModels([
          { id: 1, name: 'PowerEdge c6320', size: 2, bay_count: 8 },
          { id: 2, name: 'PowerEdge c6420', size: 2, bay_count: 8 },
          { id: 3, name: 'PowerEdge M1000e', size: 10, bay_count: 16 }
        ]);
      }
      setFetchingData(prev => ({ ...prev, chassisModels: false }));
    })
    .catch(error => {
      console.error("Error fetching chassis models:", error);
      // Set fallback data
      setChassisModels([
        { id: 1, name: 'PowerEdge c6320', size: 2, bay_count: 8 },
        { id: 2, name: 'PowerEdge c6420', size: 2, bay_count: 8 },
        { id: 3, name: 'PowerEdge M1000e', size: 10, bay_count: 16 }
      ]);
      setFetchingData(prev => ({ ...prev, chassisModels: false }));
    });
  }
  // Function to fetch available ports for a switch
// Function to fetch available ports for a switch
const fetchAvailablePorts = async (switchId) => {
  if (!switchId) return;

  console.log("Fetching ports for switch ID:", switchId);

  try {
    setLoading(true);
    const token = localStorage.getItem('admin_token');

    const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_switch_ports', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        token,
        switch_id: switchId
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }

    const data = await response.json();
    console.log(`Fetched ${data.length} ports for switch ${switchId}`, data);

    // Filter only available ports
    const availablePortsForSwitch = data.filter(port => port.status === 'Available');
    console.log(`Found ${availablePortsForSwitch.length} available ports`, availablePortsForSwitch);

    setAvailablePorts(prev => ({
      ...prev,
      [switchId]: availablePortsForSwitch
    }));

    setLoading(false);
  } catch (error) {
    console.error("Error fetching available ports:", error);
    setAvailablePorts(prev => ({
      ...prev,
      [switchId]: []
    }));
    setLoading(false);
  }
};
const handleAddChassisModel = async () => {
  try {
    if (!newChassisModel.name.trim()) {
      alert('Chassis model name is required');
      return;
    }

    if (!newChassisModel.size || newChassisModel.size < 1) {
      alert('Size must be at least 1U');
      return;
    }

    setLoading(true);
    const token = localStorage.getItem('admin_token');

    const response = await fetch(`${API_URL}/api_admin_inventory.php?f=add_chassis_model', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: token,
        name: newChassisModel.name,
        size: newChassisModel.size,
        bay_count: newChassisModel.bay_count
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }

    const result = await response.json();

    if (result.success) {
      setShowAddChassisModelModal(false);
      setNewChassisModel({ name: '', size: 2, bay_count: 8 });

      // Refresh chassis models
      await fetchChassisModels();

      alert('Chassis model added successfully');
    } else {
      alert(result.error || 'Failed to add chassis model');
    }

    setLoading(false);
  } catch (err) {
    setLoading(false);
    console.error("Error adding chassis model:", err);
    alert('Failed to add chassis model: ' + err.message);
  }
};

// Create a new separate function for handling switch selection
const handleSwitchSelection = (index, switchId) => {
  console.log(`Switch selected for row ${index}: ${switchId}`);

  // Update the item with the new switch ID
  const newItems = [...items];
  newItems[index] = {
    ...newItems[index],
    switch_id: switchId,
    // Reset port values
    port1: '',
    port2: '',
    port3: '',
    port4: '',
    port1_speed: '',
    port2_speed: '',
    port3_speed: '',
    port4_speed: ''
  };

  setItems(newItems);

  // Clear any error for this field if it exists
  if (errorMessages[index]?.switch_id) {
    const newErrors = {...errorMessages};
    delete newErrors[index]?.switch_id;
    if (Object.keys(newErrors[index] || {}).length === 0) {
      delete newErrors[index];
    }
    setErrorMessages(newErrors);
  }

  // Fetch available ports when switch is selected
  if (switchId) {
    console.log("Calling fetchAvailablePorts from handleSwitchSelection");
    fetchAvailablePorts(switchId);
  }
};

  // Function to fetch switch models from the database
  function fetchSwitchModels() {
    setFetchingData(prev => ({ ...prev, switchModels: true }));

    const token = localStorage.getItem('admin_token');

    fetch(`${API_URL}/api_admin_inventory.php?f=get_inventory_switch_models', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ token })
    })
    .then(response => {
      if (!response.ok) throw new Error(`HTTP error ${response.status}`);
      return response.json();
    })
    .then(data => {
      if (Array.isArray(data)) {
        setSwitchModels(data);
      } else {
        // Fall back to some common switch models
        setSwitchModels([
          { id: 1, name: 'Arista DCS-7504N', size: 1 },
          { id: 2, name: 'Cisco Nexus 9396TX', size: 2 },
          { id: 3, name: 'Juniper EX4600', size: 1 }
        ]);
      }
      setFetchingData(prev => ({ ...prev, switchModels: false }));
    })
    .catch(error => {
      console.error("Error fetching switch models:", error);
      // Set fallback data
      setSwitchModels([
        { id: 1, name: 'Arista DCS-7504N', size: 1 },
        { id: 2, name: 'Cisco Nexus 9396TX', size: 2 },
        { id: 3, name: 'Juniper EX4600', size: 1 }
      ]);
      setFetchingData(prev => ({ ...prev, switchModels: false }));
    });
  }

  // Helper function to create empty item based on device type
  function createEmptyItem() {
    // Common fields for all device types
    const baseItem = {
      label: '',
      status: 'Active',
      notes: '',
      city_id: '',
      rack_id: '',
      position: ''
    };

    // Add device-specific fields
    switch (deviceType) {
      case 'blade':
      case 'dedicated':
        return {
          ...baseItem,
          cpu: '',  // This is correct - should be empty string initially
          ram: ''   // This is correct - should be empty string initially
        };
      case 'chassis':
        return {
          ...baseItem,
          size_ur: '',
          model: '',
          model_id: '',
          max_power: '',
          psu_count: '2',
          bay_count: ''
        };
      case 'switch':
        return {
          ...baseItem,
          switch_ip: '',
          root_password: '',
          snmp_community: '',
          model: '',
          model_id: '',
          port_speed: '',
          manufacturer: ''
        };
      default:
        return baseItem;
    }
  }

  // Initialize items state with empty items when deviceType changes
  useEffect(() => {
    if (isOpen) {
      setItems(Array.from({ length: 10 }, () => createEmptyItem()));
    }
  }, [isOpen, deviceType]);

  // Function to add multiple rows
  function addRows(count = 1) {
    const newItems = [...items];
    for (let i = 0; i < count; i++) {
      if (templateItem) {
        newItems.push({...templateItem, label: ''});
      } else {
        newItems.push(createEmptyItem());
      }
    }
    setItems(newItems);
  }

  // Remove a row
  function removeRow(index) {
    if (items.length > 1) {
      const newItems = [...items];
      newItems.splice(index, 1);
      setItems(newItems);

      // Remove any errors for this row
      const newErrors = {...errorMessages};
      delete newErrors[index];
      setErrorMessages(newErrors);
    }
  }

  // Handle input change for a specific row and field
// 1. First, update your handleItemChange function to handle city selection
// Find your existing handleItemChange function and modify it to include this logic
function handleItemChange(index, field, value) {
  const newItems = [...items];

  // Special handling for foreign key fields
  if (['rack_id', 'city_id', 'country_id', 'cpu', 'ram', 'switch_id', 'model_id'].includes(field)) {
    if (value === '' || value === '0') {
      // Empty values should be empty strings, not numbers
      newItems[index] = {
        ...newItems[index],
        [field]: ''
      };
    } else {
      // Convert to number for non-empty foreign key fields
      newItems[index] = {
        ...newItems[index],
        [field]: parseInt(value, 10)
      };

      // Special handling when city changes - clear rack selection
      if (field === 'city_id') {
        newItems[index]['rack_id'] = '';
      }
    }
  } else {
    // For non-foreign key fields, just use the value as is
    newItems[index] = {
      ...newItems[index],
      [field]: value
    };
  }

  // Special handling for switch_id selection
  if (field === 'switch_id' && newItems[index]['switch_id'] !== value) {
    newItems[index]['port1'] = '';
    newItems[index]['port2'] = '';
    newItems[index]['port3'] = '';
    newItems[index]['port4'] = '';

    // Fetch available ports when switch is selected
    if (value) {
      fetchAvailablePorts(value);
    }
  }

  setItems(newItems);

  // Clear any error for this field if it exists
  if (errorMessages[index]?.[field]) {
    const newErrors = {...errorMessages};
    delete newErrors[index][field];
    if (Object.keys(newErrors[index]).length === 0) {
      delete newErrors[index];
    }
    setErrorMessages(newErrors);
  }
}

  // Set the current row as template for future rows
  function setRowAsTemplate(index) {
    setTemplateItem({...items[index]});
  }

  // Apply template to all existing rows
  function applyTemplateToAll() {
    if (templateItem) {
      const newItems = items.map(item => ({
        ...templateItem,
        label: item.label, // Preserve existing labels
      }));
      setItems(newItems);
    }
  }

  // Generate sequential names with a prefix
  function generateSequentialNames() {
    let defaultPrefix = '';
    switch (deviceType) {
      case 'blade': defaultPrefix = 'XR-BL'; break;
      case 'dedicated': defaultPrefix = 'XR-'; break;
      case 'chassis': defaultPrefix = 'XR-BL'; break;
      case 'switch': defaultPrefix = 'BUC-AR'; break;
    }

    const prefix = prompt("Enter device name prefix:", defaultPrefix);
    if (!prefix) return;

    const newItems = items.map((item, index) => {
      const paddedNum = String(index + 1).padStart(2, '0');
      return {
        ...item,
        label: `${prefix}${paddedNum}`
      };
    });

    setItems(newItems);
  }

  // Toggle password visibility
  function togglePasswordVisibility(index, field) {
    setPasswordVisibility(prev => ({
      ...prev,
      [`${index}-${field}`]: !prev[`${index}-${field}`]
    }));
  }

// Fixed function to handle adding a new switch model
const handleAddSwitchModel = async () => {
  try {
    if (!newSwitchModel.name.trim()) {
      alert('Switch model name is required');
      return;
    }

    // Make sure size is a valid number
    const size = parseInt(newSwitchModel.size);
    if (isNaN(size) || size < 1) {
      alert('Size must be a valid number (at least 1U)');
      return;
    }

    setLoading(true);
    const token = localStorage.getItem('admin_token');

    // Debug: Log what we're sending
    console.log("Sending switch model data:", {
      token: token ? "present" : "missing",
      name: newSwitchModel.name,
      size: size
    });

    const response = await fetch(`${API_URL}/api_admin_inventory.php?f=add_switch_model', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: token,
        name: newSwitchModel.name.trim(),
        size: size // Send as number, not string
      })
    });

    // Get the full response text for better debugging
    const responseText = await response.text();
    let result;

    try {
      // Try to parse as JSON
      result = JSON.parse(responseText);
    } catch (e) {
      console.error("Server response is not valid JSON:", responseText);
      throw new Error("Server returned invalid response format. Check your network tab for details.");
    }

    if (result.success) {
      setShowAddSwitchModelModal(false);
      setNewSwitchModel({ name: '', size: 1 });

      // Refresh switch models
      await fetchSwitchModels();

      alert('Switch model added successfully');
    } else {
      alert(result.error || 'Failed to add switch model');
    }

    setLoading(false);
  } catch (err) {
    setLoading(false);
    console.error("Error adding switch model:", err);
    alert('Failed to add switch model: ' + err.message);
  }
};

  // Detect MAC addresses for multiple servers
  const detectAllMacAddresses = async () => {
    if (deviceType !== 'blade' && deviceType !== 'dedicated') return;

    // Filter items that have IPMI addresses but no MAC
    const itemsToUpdate = items.filter(item =>
      item.ipmi && item.ipmi.trim() !== '' &&
      (!item.mac || item.mac.trim() === '')
    );

    if (itemsToUpdate.length === 0) {
      alert('No items with IPMI addresses and missing MAC addresses found');
      return;
    }

    if (!window.confirm(`Detect MAC addresses for ${itemsToUpdate.length} servers with IPMI addresses?`)) {
      return;
    }

    setProcessingMacs(true);
    setMacResults({}); // Clear previous results
    const token = localStorage.getItem('admin_token');
    const updatedItems = [...items];

    try {
      // Prepare tracking for all items being processed
      itemsToUpdate.forEach((item) => {
        const itemIndex = items.findIndex(i => i === item);
        if (itemIndex !== -1) {
          setMacResults(prev => ({
            ...prev,
            [`${itemIndex}-progress`]: true
          }));
        }
      });

      // Create an array of promises for all MAC address detections
      const detectionPromises = itemsToUpdate.map(async (item) => {
        const itemIndex = items.findIndex(i => i === item);
        if (itemIndex === -1) return null;

        try {
          const response = await fetch(`${API_URL}/api_admin_inventory.php?f=detect_mac_address', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              token: token,
              ipmi_address: item.ipmi,
              ipmi_username: 'root',
              ipmi_password: item.ipmi_root_pass || '',
              idrac_version: 8, // Default to iDRAC 8
              server_type: deviceType
            })
          });

          if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
          }

          const data = await response.json();

          // Enhanced MAC address processing
          if (data.success) {
            // Check if we have multiple MAC addresses
            if (data.all_mac_addresses && data.all_mac_addresses.length > 0) {
              // Filter out WWN/WWPN addresses
              const filteredMacs = data.all_mac_addresses.filter(mac => {
                const macAddress = mac.mac;
                const parts = macAddress.split(/[:-]/);

                if (parts.length === 8) return false;
                if (macAddress.startsWith('20:00:') ||
                    macAddress.startsWith('21:00:') ||
                    macAddress.startsWith('10:00:')) return false;

                return true;
              });

              // Look for PermanentMACAddress in raw output
              const rawOutput = data.raw_output || "";
              let permanentMacMatch = null;

              const permanentMacRegex = /PermanentMACAddress\s*=\s*([0-9A-Fa-f]{2}(?::[0-9A-Fa-f]{2}){5})/g;
              const macMatches = [...rawOutput.matchAll(permanentMacRegex)];

              if (macMatches.length > 0) {
                permanentMacMatch = macMatches[0][1];

                if (!filteredMacs.some(mac => mac.mac.toLowerCase() === permanentMacMatch.toLowerCase())) {
                  filteredMacs.unshift({
                    mac: permanentMacMatch,
                    interface: "PermanentMACAddress",
                    is_permanent: true,
                    link_status: "Unknown"
                  });
                }
              }

              // If no valid MAC addresses after filtering, use primary MAC if available
              if (filteredMacs.length === 0) {
                if (data.mac_address) {
                  return {
                    itemIndex,
                    success: true,
                    mac: data.mac_address,
                    error: null
                  };
                }
                return {
                  itemIndex,
                  success: false,
                  mac: null,
                  error: "No valid MAC addresses detected"
                };
              }

              // Find the best MAC to use
              let bestMacIndex = 0;

              // Try to use PermanentMACAddress if found
              if (permanentMacMatch) {
                const exactMatchIndex = filteredMacs.findIndex(
                  mac => mac.mac.toLowerCase() === permanentMacMatch.toLowerCase()
                );
                if (exactMatchIndex >= 0) {
                  bestMacIndex = exactMatchIndex;
                }
              }

              // Otherwise, try to find any permanent MAC
              if (!permanentMacMatch) {
                const permanentMacIndex = filteredMacs.findIndex(mac => mac.is_permanent);
                if (permanentMacIndex >= 0) {
                  bestMacIndex = permanentMacIndex;
                }
              }

              // If no permanent MAC, look for physical interfaces
              if (bestMacIndex === 0 && !filteredMacs[0].is_permanent) {
                for (let i = 0; i < filteredMacs.length; i++) {
                  const mac = filteredMacs[i];
                  const interfaceName = mac.interface?.toLowerCase() || '';

                  if (interfaceName.includes('physical') ||
                      interfaceName.includes('integrated') ||
                      interfaceName.includes('nic.integrated') ||
                      interfaceName.includes('embedded') ||
                      interfaceName.includes('lom') ||
                      (interfaceName.includes('nic') && !interfaceName.includes('virtual'))
                  ) {
                    bestMacIndex = i;
                    break;
                  }
                }
              }

              const bestMac = filteredMacs[bestMacIndex].mac;

              return {
                itemIndex,
                success: true,
                mac: bestMac,
                error: null,
                allMacs: filteredMacs
              };
            } else if (data.mac_address) {
              // Fall back to primary MAC if all_mac_addresses is not available
              return {
                itemIndex,
                success: true,
                mac: data.mac_address,
                error: null
              };
            }
          }

          // If we get here, no usable MAC address was found
          return {
            itemIndex,
            success: false,
            mac: null,
            error: data.error || "No MAC address detected"
          };
        } catch (e) {
          console.error(`Error detecting MAC for item at index ${itemIndex}:`, e);
          return {
            itemIndex,
            success: false,
            mac: null,
            error: e.message
          };
        }
      });

      // Wait for all promises to resolve
      const results = await Promise.all(detectionPromises);

      // Process all results
      let successCount = 0;
      let failCount = 0;

      results.forEach(result => {
        if (!result) return;

        const { itemIndex, success, mac, error, allMacs } = result;

        // Clear progress indicator
        setMacResults(prev => {
          const updated = {...prev};
          delete updated[`${itemIndex}-progress`];
          return updated;
        });

        if (success && mac) {
          // Update the item with the detected MAC
          updatedItems[itemIndex] = {
            ...updatedItems[itemIndex],
            mac: mac
          };

          // Update the results display
          setMacResults(prev => ({
            ...prev,
            [itemIndex]: {
              success: true,
              mac: mac,
              allMacs: allMacs
            }
          }));

          successCount++;
        } else {
          // Update failure state
          setMacResults(prev => ({
            ...prev,
            [itemIndex]: {
              success: false,
              error: error || 'No MAC address found'
            }
          }));

          failCount++;
        }
      });

      // Update all items at once
      setItems(updatedItems);

      // Show summary alert with results
      if (successCount > 0 || failCount > 0) {
        alert(`MAC address detection completed:
- Successfully detected: ${successCount}
- Failed: ${failCount}`);
      }
    } catch (e) {
      console.error('Error in bulk MAC detection:', e);
      alert('Error during MAC address detection: ' + e.message);
    } finally {
      setProcessingMacs(false);
    }
  };

  // New function to fetch RAM configurations
  function fetchRamConfigurations() {
    setFetchingData(prev => ({ ...prev, ramConfigurations: true }));

    const token = localStorage.getItem('admin_token');

    fetch(`${API_URL}/api_admin_inventory.php?f=get_ram_configurations', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ token })
    })
    .then(response => {
      if (!response.ok) throw new Error(`HTTP error ${response.status}`);
      return response.json();
    })
    .then(data => {
      if (Array.isArray(data)) {
        setRamConfigurations(data);
        console.log(`Loaded ${data.length} RAM configurations`);
      } else {
        // Fall back to some common RAM configurations
        setRamConfigurations([
          { id: 1, size: 16, description: '16GB (2x8GB) DDR4-2400' },
          { id: 2, size: 32, description: '32GB (2x16GB) DDR4-2400' },
          { id: 3, size: 64, description: '64GB (4x16GB) DDR4-2400' },
          { id: 4, size: 128, description: '128GB (8x16GB) DDR4-2666' },
          { id: 5, size: 256, description: '256GB (8x32GB) DDR4-2933' }
        ]);
      }
      setFetchingData(prev => ({ ...prev, ramConfigurations: false }));
    })
    .catch(error => {
      console.error("Error fetching RAM configurations:", error);
      // Set fallback data
      setRamConfigurations([
        { id: 1, size: 16, description: '16GB (2x8GB) DDR4-2400' },
        { id: 2, size: 32, description: '32GB (2x16GB) DDR4-2400' },
        { id: 3, size: 64, description: '64GB (4x16GB) DDR4-2400' },
        { id: 4, size: 128, description: '128GB (8x16GB) DDR4-2666' },
        { id: 5, size: 256, description: '256GB (8x32GB) DDR4-2933' }
      ]);
      setFetchingData(prev => ({ ...prev, ramConfigurations: false }));
    });
  }

  // Function to handle adding a new CPU model
  const handleAddCpuModel = async () => {
    try {
      if (!newCpuModel.trim()) {
        alert('CPU model name is required');
        return;
      }

      setLoading(true);
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=add_cpu_model', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          cpu: newCpuModel
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        setShowAddCpuModal(false);
        setNewCpuModel('');

        // Refresh CPU models
        await fetchCpuOptions();

        alert('CPU model added successfully');
      } else {
        alert(result.error || 'Failed to add CPU model');
      }

      setLoading(false);
    } catch (err) {
      setLoading(false);
      console.error("Error adding CPU model:", err);
      alert('Failed to add CPU model: ' + err.message);
    }
  };

  // Function to handle adding a new RAM configuration
  const handleAddRamConfig = async () => {
    try {
      if (!newRamConfig.size || !newRamConfig.description) {
        alert('RAM size and description are required');
        return;
      }

      setLoading(true);
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=add_ram_configuration', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          size: newRamConfig.size,
          description: newRamConfig.description
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        setShowAddRamModal(false);
        setNewRamConfig({ size: '', description: '' });

        // Refresh RAM configurations
        await fetchRamConfigurations();

        alert('RAM configuration added successfully');
      } else {
        alert(result.error || 'Failed to add RAM configuration');
      }

      setLoading(false);
    } catch (err) {
      setLoading(false);
      console.error("Error adding RAM configuration:", err);
      alert('Failed to add RAM configuration: ' + err.message);
    }
  };

  // Fetch required data
  useEffect(() => {
    if (isOpen) {
      if (deviceType === 'blade' || deviceType === 'dedicated') {
        fetchCpuOptions();
        fetchRamConfigurations();
      }
      if (deviceType === 'chassis') {
        fetchChassisModels();
      }
      if (deviceType === 'switch') {
        fetchSwitchModels();
      }
    }
  }, [isOpen, deviceType]);


  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setItems(Array.from({ length: 10 }, () => createEmptyItem()));
      setErrorMessages({});
      setTemplateItem(null);
      setPasswordVisibility({});
      setActiveTab("basic");
      setCurrentPage(0);

      // Set appropriate initial tab based on device type
      switch (deviceType) {
        case 'blade':
        case 'dedicated':
          setActiveTab("basic");
          break;
        case 'chassis':
          setActiveTab("basic");
          break;
        case 'switch':
          setActiveTab("basic");
          break;
      }
    }
  }, [isOpen, deviceType]);


// Update the handleChassisModelChange function to set bay_count too
function handleChassisModelChange(index, modelId) {
  const selectedModel = chassisModels.find(model => model.id.toString() === modelId.toString());
  if (selectedModel) {
    const newItems = [...items];
    newItems[index] = {
      ...newItems[index],
      model_id: modelId,
      model: selectedModel.name,
      size_ur: selectedModel.size, // Auto-set the size based on the model
      bay_count: selectedModel.bay_count // Auto-set the bay count based on the model
    };
    setItems(newItems);

    // Clear any error for model, size, or bay_count if it exists
    if (errorMessages[index]?.model_id || errorMessages[index]?.size_ur || errorMessages[index]?.bay_count) {
      const newErrors = {...errorMessages};
      delete newErrors[index]?.model_id;
      delete newErrors[index]?.size_ur;
      delete newErrors[index]?.bay_count;
      if (Object.keys(newErrors[index] || {}).length === 0) {
        delete newErrors[index];
      }
      setErrorMessages(newErrors);
    }
  }
}

  // Add a handler for model selection for switches
  function handleSwitchModelChange(index, modelId) {
    const selectedModel = switchModels.find(model => model.id.toString() === modelId.toString());
    if (selectedModel) {
      const newItems = [...items];
      newItems[index] = {
        ...newItems[index],
        model_id: modelId,
        model: selectedModel.name,
        size_ur: selectedModel.size // Auto-set the size based on the model
      };
      setItems(newItems);

      // Clear any error for model or size if it exists
      if (errorMessages[index]?.model_id || errorMessages[index]?.size_ur) {
        const newErrors = {...errorMessages};
        delete newErrors[index]?.model_id;
        delete newErrors[index]?.size_ur;
        if (Object.keys(newErrors[index] || {}).length === 0) {
          delete newErrors[index];
        }
        setErrorMessages(newErrors);
      }
    }
  }

  const getFilteredRacks = (cityId) => {
    if (!cityId) return [];
    return racks.filter(rack => {
      // Handle different data structures - some might use 'city' and others 'city_id'
      return rack.city == cityId || rack.city_id == cityId;
    });
  };

  // 4. You can use this function when you need to check if there are available racks
  const noRacksAvailable = (cityId) => {
    const filteredRacks = getFilteredRacks(cityId);
    return filteredRacks.length === 0;
  };

  // Function to fetch CPU options from the dedicated_cpu table
  function fetchCpuOptions() {
    setFetchingData(prev => ({ ...prev, cpus: true }));

    const token = localStorage.getItem('admin_token');

    fetch(`${API_URL}/api_admin_inventory.php?f=get_dedicated_cpus', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ token })
    })
    .then(response => {
      if (!response.ok) throw new Error(`HTTP error ${response.status}`);
      return response.json();
    })
    .then(data => {
      if (Array.isArray(data)) {
        setCpuOptions(data);
      } else {
        // Fall back to some common CPU options
        setCpuOptions([
          { id: 1, cpu: "Intel Xeon E5-2650 v4" },
          { id: 2, cpu: "Intel Xeon E5-2680 v4" },
          { id: 3, cpu: "AMD EPYC 7302" },
          { id: 4, cpu: "Intel Xeon Gold 6230R" },
          { id: 5, cpu: "AMD EPYC 7502" }
        ]);
      }
      setFetchingData(prev => ({ ...prev, cpus: false }));
    })
    .catch(error => {
      console.error("Error fetching CPU options:", error);
      setCpuOptions([
        { id: 1, cpu: "Intel Xeon E5-2650 v4" },
        { id: 2, cpu: "Intel Xeon E5-2680 v4" },
        { id: 3, cpu: "AMD EPYC 7302" },
        { id: 4, cpu: "Intel Xeon Gold 6230R" },
        { id: 5, cpu: "AMD EPYC 7502" }
      ]);
      setFetchingData(prev => ({ ...prev, cpus: false }));
    });
  }

  // Validate all items before submission
// Update the validateItems function to include validation for model_id
function validateItems() {
  const errors = {};
  let hasErrors = false;

  items.forEach((item, index) => {
    // Skip empty rows (they will be filtered out)
    if (!item.label || item.label.trim() === '') {
      return;
    }

    const rowErrors = {};

    // Required fields for all device types
    if (!item.label || item.label.trim() === '') {
      rowErrors.label = 'Required';
      hasErrors = true;
    }

    // Device-specific validation
    switch (deviceType) {
      case 'chassis':
        if (!item.city_id || item.city_id === '') {
          rowErrors.city_id = 'Required';
          hasErrors = true;
        }

        if (!item.rack_id || item.rack_id === '') {
          rowErrors.rack_id = 'Required';
          hasErrors = true;
        }

        // Now validate model_id instead of size_ur
        if (!item.model_id || item.model_id === '') {
          rowErrors.model_id = 'Required';
          hasErrors = true;
        }
        break;

      case 'switch':
        if (!item.city_id || item.city_id === '') {
          rowErrors.city_id = 'Required';
          hasErrors = true;
        }

        if (!item.rack_id || item.rack_id === '') {
          rowErrors.rack_id = 'Required';
          hasErrors = true;
        }

        if (!item.switch_ip || item.switch_ip === '') {
          rowErrors.switch_ip = 'Required';
          hasErrors = true;
        }

        // Also validate model_id for switches
        if (!item.model_id || item.model_id === '') {
          rowErrors.model_id = 'Required';
          hasErrors = true;
        }
        break;

      // Other cases remain unchanged
    }

    if (Object.keys(rowErrors).length > 0) {
      errors[index] = rowErrors;
    }
  });

  setErrorMessages(errors);
  return !hasErrors;
}

  // Handle form submission
  async function handleSubmit() {
    if (!validateItems()) {
      alert("Please fix the validation errors before submitting.");
      return;
    }

    // Filter out rows with empty labels
    const itemsToAdd = items.filter(item => item.label && item.label.trim() !== '');

    if (itemsToAdd.length === 0) {
      alert(`Please provide at least one ${deviceType} with a label`);
      return;
    }

    setLoading(true);
    try {
      await onAddItems(itemsToAdd, deviceType);
      onClose();
    } catch (error) {
      console.error(`Error adding ${deviceType}:`, error);
      alert(`Failed to add ${deviceType}: ${error.message}`);
    } finally {
      setLoading(false);
    }
  }

  // Get items for current page
  const getCurrentPageItems = () => {
    const start = currentPage * itemsPerPage;
    const end = start + itemsPerPage;
    return items.slice(start, end);
  };

  // Calculate page count
  const pageCount = Math.ceil(items.length / itemsPerPage);

  // Next/previous page handlers
  const goToNextPage = () => {
    if (currentPage < pageCount - 1) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPrevPage = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
    }
  };

  if (!isOpen) return null;

  // Helper function to get display name for device type
  const getDeviceTypeName = () => {
    switch (deviceType) {
      case 'blade': return 'Blade Server';
      case 'dedicated': return 'Dedicated Server';
      case 'chassis': return 'Chassis';
      case 'switch': return 'Switch';
      default: return 'Device';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-2">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-6xl max-h-[95vh] flex flex-col">
        {/* Header */}
        <div className="p-4 border-b flex justify-between items-center sticky top-0 bg-white z-10">
          <h2 className="text-lg font-bold text-gray-800">
            Bulk Add {getDeviceTypeName()}s
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <XCircle className="w-5 h-5" />
          </button>
        </div>

        {/* Loading state */}
        {(fetchingData.cpus || fetchingData.switches || fetchingData.chassis || fetchingData.ramConfigurations) && (
          <div className="bg-blue-50 text-blue-700 p-2 text-sm flex items-center">
            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            Loading reference data...
          </div>
        )}

        {/* Tabs */}
<div className="flex border-b">
  <button
    className={`py-2 px-4 font-medium text-sm ${activeTab === 'basic'
      ? 'text-indigo-700 border-b-2 border-indigo-700'
      : 'text-gray-500 hover:text-gray-700'}`}
    onClick={() => setActiveTab('basic')}
  >
    {deviceType === 'switch' ? <Network className="w-4 h-4 inline mr-1" /> :
     deviceType === 'chassis' ? <HardDrive className="w-4 h-4 inline mr-1" /> :
     <Server className="w-4 h-4 inline mr-1" />}
    Basic Info
  </button>

  {(deviceType === 'blade' || deviceType === 'dedicated') && (
    <button
      className={`py-2 px-4 font-medium text-sm ${activeTab === 'network'
        ? 'text-indigo-700 border-b-2 border-indigo-700'
        : 'text-gray-500 hover:text-gray-700'}`}
      onClick={() => setActiveTab('network')}
    >
      <Network className="w-4 h-4 inline mr-1" />
      Network
    </button>
  )}

  {/* Only show Location tab for non-blade device types */}
  {deviceType !== 'blade' && (
    <button
      className={`py-2 px-4 font-medium text-sm ${activeTab === 'location'
        ? 'text-indigo-700 border-b-2 border-indigo-700'
        : 'text-gray-500 hover:text-gray-700'}`}
      onClick={() => setActiveTab('location')}
    >
      <Globe className="w-4 h-4 inline mr-1" />
      Location
    </button>
  )}

  {deviceType === 'blade' && (
    <button
      className={`py-2 px-4 font-medium text-sm ${activeTab === 'chassis'
        ? 'text-indigo-700 border-b-2 border-indigo-700'
        : 'text-gray-500 hover:text-gray-700'}`}
      onClick={() => setActiveTab('chassis')}
    >
      <HardDrive className="w-4 h-4 inline mr-1" />
      Chassis
    </button>
  )}

  {(deviceType === 'blade' || deviceType === 'dedicated') && (
    <button
      className={`py-2 px-4 font-medium text-sm ${activeTab === 'ipmi'
        ? 'text-indigo-700 border-b-2 border-indigo-700'
        : 'text-gray-500 hover:text-gray-700'}`}
      onClick={() => setActiveTab('ipmi')}
    >
      <Cpu className="w-4 h-4 inline mr-1" />
      IPMI
    </button>
  )}

{deviceType === 'switch' && (
    <button
      className={`py-2 px-4 font-medium text-sm ${activeTab === 'snmp'
        ? 'text-indigo-700 border-b-2 border-indigo-700'
        : 'text-gray-500 hover:text-gray-700'}`}
      onClick={() => setActiveTab('snmp')}
    >
      <Wifi className="w-4 h-4 inline mr-1" />
      SNMP Settings
    </button>
  )}

</div>
{/* Table Toolbar */}
<div className="bg-gray-50 p-3 border-b flex flex-wrap justify-between items-center">
  <div className="flex flex-wrap gap-2 items-center">
    <button
      onClick={() => addRows(1)}
      className="px-3 py-1.5 bg-indigo-700 text-white rounded-md flex items-center text-sm hover:bg-indigo-800"
    >
      <Plus className="w-4 h-4 mr-1" />
      Add Row
    </button>

    <button
      onClick={() => addRows(5)}
      className="px-3 py-1.5 bg-indigo-600 text-white rounded-md flex items-center text-sm hover:bg-indigo-700"
    >
      <Layout className="w-4 h-4 mr-1" />
      Add 5 Rows
    </button>

    {/* Only show Generate Sequential Names button in the Basic Info tab */}
    {activeTab === 'basic' && (
      <button
        onClick={generateSequentialNames}
        className="px-3 py-1.5 border border-gray-300 bg-white rounded-md flex items-center text-sm hover:bg-gray-50"
      >
        Generate Sequential Names
      </button>
    )}

    {(deviceType === 'blade' || deviceType === 'dedicated') && activeTab === 'ipmi' && (
      <button
        onClick={detectAllMacAddresses}
        disabled={processingMacs}
        className={`px-3 py-1.5 border ${processingMacs ? 'bg-gray-100 text-gray-500' : 'bg-white text-gray-700 hover:bg-gray-50'} rounded-md flex items-center text-sm`}
      >
        {processingMacs ?
          <RefreshCw className="w-4 h-4 mr-1 animate-spin" /> :
          <Wifi className="w-4 h-4 mr-1" />
        }
        Detect MAC Addresses
      </button>
    )}

    {templateItem && (
      <div className="flex gap-2">
        <button
          onClick={applyTemplateToAll}
          className="px-3 py-1.5 border border-gray-300 bg-white rounded-md flex items-center text-sm hover:bg-gray-50"
        >
          <Copy className="w-4 h-4 mr-1" />
          Apply Template to All
        </button>

        <div className="px-3 py-1.5 text-xs text-gray-600 bg-gray-100 rounded flex items-center">
          <span className="font-medium">Template Active</span>
        </div>
      </div>
    )}
  </div>

  {/* Pagination Controls */}
  {items.length > itemsPerPage && (
    <div className="flex items-center text-sm text-gray-500">
      <span className="mr-2">Page</span>
      <button
        onClick={goToPrevPage}
        disabled={currentPage === 0}
        className={`p-1 rounded ${currentPage === 0 ? 'text-gray-400' : 'text-gray-700 hover:bg-gray-200'}`}
      >
        <ChevronLeft className="w-4 h-4" />
      </button>
      <span className="mx-1 font-medium">{currentPage + 1}</span>
      <button
        onClick={goToNextPage}
        disabled={currentPage === pageCount - 1}
        className={`p-1 rounded ${currentPage === pageCount - 1 ? 'text-gray-400' : 'text-gray-700 hover:bg-gray-200'}`}
      >
        <ChevronRight className="w-4 h-4" />
      </button>
      <span className="ml-1">of {pageCount}</span>
      <span className="ml-2 text-gray-400">({items.length} items)</span>
    </div>
  )}
</div>

        {/* Table Container */}
        <div className="overflow-auto flex-grow p-4">
          {/* Basic Info Tab - Different for each device type */}
          {activeTab === 'basic' && (
            <table className="w-full">
              <thead>
                <tr className="text-xs text-gray-500 border-b">
                  <th className="p-2 text-left font-medium w-12">#</th>
                  <th className="p-2 text-left font-medium">Label*</th>

                  {/* Device-specific columns */}
                  {(deviceType === 'blade' || deviceType === 'dedicated') && (
                    <>
                      <th className="p-2 text-left font-medium">CPU*</th>
                      <th className="p-2 text-left font-medium">RAM</th>
                    </>
                  )}

                  {deviceType === 'chassis' && (
  <>
    <th className="p-2 text-left font-medium">Model*</th>

  </>
)}

                  {deviceType === 'switch' && (
  <>
    <th className="p-2 text-left font-medium">IP Address*</th>
    <th className="p-2 text-left font-medium">Password*</th>
    <th className="p-2 text-left font-medium">Model*</th>
  </>
)}


                  <th className="p-2 text-left font-medium">Status</th>
                  <th className="p-2 text-left font-medium w-20">Actions</th>
                </tr>
              </thead>
              <tbody>
                {getCurrentPageItems().map((item, index) => {
                  const itemIndex = currentPage * itemsPerPage + index;
                  return (
                    <tr key={itemIndex} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} h-14`}>
                      <td className="p-2">{itemIndex + 1}</td>
                      <td className="p-2">
                        <input
                          type="text"
                          value={item.label}
                          onChange={(e) => handleItemChange(itemIndex, 'label', e.target.value)}
                          className={`w-full p-1.5 border ${
                            errorMessages[itemIndex]?.label ? 'border-red-500' : 'border-gray-300'
                          } rounded-md text-sm`}
                          placeholder={`${getDeviceTypeName()} Label`}
                        />
                        {errorMessages[itemIndex]?.label && (
                          <div className="text-xs text-red-500 mt-1">{errorMessages[itemIndex].label}</div>
                        )}
                      </td>

                      {/* Device-specific fields */}
                      {(deviceType === 'blade' || deviceType === 'dedicated') && (
                        <>
                          <td className="p-2">
                            <div className="flex">
                            <select
  value={item.cpu || ''}
  onChange={(e) => handleItemChange(itemIndex, 'cpu', e.target.value)}
  className={`w-full p-1.5 border ${
    errorMessages[itemIndex]?.cpu ? 'border-red-500' : 'border-gray-300'
  } rounded-md text-sm`}
>
  <option value="">Select CPU Model</option>
  {cpuOptions.map(cpu => (
    <option key={cpu.id} value={cpu.id}>{cpu.cpu}</option>
  ))}
</select>
                              <button
                                type="button"
                                onClick={() => setShowAddCpuModal(true)}
                                className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
                                title="Add New CPU Model"
                              >
                                <Plus className="w-4 h-4" />
                              </button>
                            </div>
                            {errorMessages[itemIndex]?.cpu && (
                              <p className="text-xs text-red-500 mt-1">{errorMessages[itemIndex].cpu}</p>
                            )}
                          </td>
                          <td className="p-2">
                            <div className="flex">
                            <select
  value={item.ram || ''}
  onChange={(e) => handleItemChange(itemIndex, 'ram', e.target.value)}
  className="w-full p-1.5 border border-gray-300 rounded-md text-sm"
>
  <option value="">Select RAM Configuration</option>
  {ramConfigurations.map(ram => (
    <option key={ram.id} value={ram.id}>{ram.description}</option>
  ))}
</select>
                              <button
                                type="button"
                                onClick={() => setShowAddRamModal(true)}
                                className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
                                title="Add New RAM Configuration"
                              >
                                <Plus className="w-4 h-4" />
                              </button>
                            </div>
                          </td>
                        </>
                      )}

                      {deviceType === 'chassis' && (
  <>
    <td className="p-2">

{/* Replace your existing chassis model dropdown with this */}
<div className="flex">
  <select
    value={item.model_id || ''}
    onChange={(e) => handleChassisModelChange(itemIndex, e.target.value)}
    className={`w-full p-1.5 border ${
      errorMessages[itemIndex]?.model_id ? 'border-red-500' : 'border-gray-300'
    } rounded-md text-sm`}
  >
    <option value="">Select Chassis Model</option>
    {fetchingData.chassisModels ? (
      <option disabled>Loading models...</option>
    ) : chassisModels.length > 0 ? (
      chassisModels.map(model => (
        <option key={model.id} value={model.id}>
          {model.name} ({model.size}U, {model.bay_count} bays)
        </option>
      ))
    ) : (
      <option disabled>No models available</option>
    )}
  </select>
  <button
    type="button"
    onClick={() => setShowAddChassisModelModal(true)}
    className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
    title="Add New Chassis Model"
  >
    <Plus className="w-4 h-4" />
  </button>
</div>
    </td>

  </>
)}

                      {deviceType === 'switch' && (
  <>
    <td className="p-2">
      <input
        type="text"
        value={item.switch_ip}
        onChange={(e) => handleItemChange(itemIndex, 'switch_ip', e.target.value)}
        className={`w-full p-1.5 border ${
          errorMessages[itemIndex]?.switch_ip ? 'border-red-500' : 'border-gray-300'
        } rounded-md text-sm`}
        placeholder="e.g. ***********"
      />
      {errorMessages[itemIndex]?.switch_ip && (
        <p className="text-xs text-red-500 mt-1">{errorMessages[itemIndex].switch_ip}</p>
      )}
    </td>
    {/* Add this new field */}

    <td className="p-2">
      <input
        type="text"
        value={item.root_password}
        onChange={(e) => handleItemChange(itemIndex, 'root_password', e.target.value)}
        className={`w-full p-1.5 border ${
          errorMessages[itemIndex]?.root_password ? 'border-red-500' : 'border-gray-300'
        } rounded-md text-sm`}

      />
      {errorMessages[itemIndex]?.root_password && (
        <p className="text-xs text-red-500 mt-1">{errorMessages[itemIndex].root_password}</p>
      )}
    </td>


    <td className="p-2">
  <div className="flex">
    <select
      value={item.model_id || ''}
      onChange={(e) => handleSwitchModelChange(itemIndex, e.target.value)}
      className={`w-full p-1.5 border ${
        errorMessages[itemIndex]?.model_id ? 'border-red-500' : 'border-gray-300'
      } rounded-md text-sm`}
    >
      <option value="">Select Switch Model</option>
      {fetchingData.switchModels ? (
        <option disabled>Loading models...</option>
      ) : switchModels.length > 0 ? (
        switchModels.map(model => (
          <option key={model.id} value={model.id}>
            {model.name} ({model.size}U)
          </option>
        ))
      ) : (
        <option disabled>No models available</option>
      )}
    </select>
    <button
      type="button"
      onClick={() => setShowAddSwitchModelModal(true)}
      className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
      title="Add New Switch Model"
    >
      <Plus className="w-4 h-4" />
    </button>
  </div>
  {errorMessages[itemIndex]?.model_id && (
    <p className="text-xs text-red-500 mt-1">{errorMessages[itemIndex].model_id}</p>
  )}
</td>

  </>
)}

                      <td className="p-2">
                        <select
                          value={item.status}
                          onChange={(e) => handleItemChange(itemIndex, 'status', e.target.value)}
                          className="w-full p-1.5 border border-gray-300 rounded-md text-sm"
                        >
                          <option value="Available">Available</option>
                          <option value="In Use">In Use</option>
                          <option value="Defect">Defect</option>


                        </select>
                      </td>
                      <td className="p-2">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => setRowAsTemplate(itemIndex)}
                            className="p-1 text-indigo-700 hover:text-indigo-900 transition-colors"
                            title="Use as template for new rows"
                          >
                            <Copy className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => removeRow(itemIndex)}
                            className="p-1 text-red-600 hover:text-red-800 transition-colors"
                            disabled={items.length === 1}
                            title="Remove row"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          )}


          {activeTab === 'network' && (deviceType === 'blade' || deviceType === 'dedicated') && (
  <>
    <table className="w-full">
      <thead>
        <tr className="text-xs text-gray-500 border-b">
          <th className="p-2 text-left font-medium w-12">#</th>
          <th className="p-2 text-left font-medium">Label</th>
          <th className="p-2 text-left font-medium">Switch</th>
          <th className="p-2 text-left font-medium">Ports</th>
          <th className="p-2 text-left font-medium w-20">Actions</th>
        </tr>
      </thead>
      <tbody>
        {getCurrentPageItems().map((item, index) => {
          const itemIndex = currentPage * itemsPerPage + index;
          return (
            <tr key={itemIndex} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} h-14`}>
              <td className="p-2">{itemIndex + 1}</td>
              <td className="p-2 font-medium">{item.label || `Device ${itemIndex + 1}`}</td>
              <td className="p-2">
  <select
    value={item.switch_id || ''}
    onChange={(e) => handleSwitchSelection(itemIndex, e.target.value)}
    className="w-full p-1.5 border border-gray-300 rounded-md text-sm"
  >
    <option value="">Select Switch</option>
    {switches.map(switchItem => (
      <option key={switchItem.id} value={switchItem.id}>
        {switchItem.label} {switchItem.switch_ip ? `(${switchItem.switch_ip})` : ''}
      </option>
    ))}
  </select>
</td>
<td className="p-2">
  <div className="flex flex-col gap-2">
    {/* Display the selected port if exists */}


{/* Display the selected port if exists */}
{item.port1 ? (
  <div className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs flex items-center" style={{ width: "150px" }}>
    Port {item.port1}
    {item.port1_speed && <span className="ml-1 text-blue-600">({item.port1_speed}M)</span>}
    <button
      className="ml-auto text-blue-500 hover:text-blue-700"
      onClick={() => {
        const newItems = [...items];
        newItems[itemIndex] = {
          ...newItems[itemIndex],
          port1: '',
          port1_speed: ''
        };
        setItems(newItems);
      }}
    >
      <X className="w-3 h-3" />
    </button>
  </div>
) : (
  /* Show port selection dropdown only if no port is selected */
  !item.switch_id ? (
    <div className="text-xs text-gray-500" style={{ width: "150px" }}>Select a switch first</div>
  ) : availablePorts[item.switch_id] === undefined ? (
    <div className="flex items-center gap-2" style={{ width: "150px" }}>
      <span className="text-xs text-gray-500">Loading ports...</span>
      <RefreshCw className="w-3 h-3 animate-spin text-indigo-600" />
    </div>
  ) : availablePorts[item.switch_id]?.length === 0 ? (
    <div className="text-xs text-amber-600 flex items-center gap-2" style={{ width: "150px" }}>
      <span>No available ports</span>
      <button
        onClick={() => fetchAvailablePorts(item.switch_id)}
        className="p-1 text-gray-500 hover:text-indigo-700 rounded"
        title="Refresh available ports"
      >
        <RefreshCw className="w-3 h-3" />
      </button>
    </div>
  ) : (
    <div className="flex items-center gap-2" style={{ width: "150px" }}>
      <select
        className="p-1.5 border border-gray-300 rounded-md text-sm"
        style={{ width: "120px" }}
        onChange={(e) => {
          if (!e.target.value) return;

          // Get port details
          const portId = e.target.value;
          const port = availablePorts[item.switch_id].find(p => p.id.toString() === portId);

          if (!port) {
            console.error("Port not found:", portId);
            return;
          }

          console.log("Selected port:", port);

          // Update the item with port details
          const newItems = [...items];
          newItems[itemIndex] = {
            ...newItems[itemIndex],
            port1: port.port_number,
            port1_speed: port.max_speed
          };
          setItems(newItems);

          // Reset dropdown selection
          e.target.value = '';

          // Refresh ports list since one is now used
          fetchAvailablePorts(item.switch_id);
        }}
      >
        <option value="">Select Port</option>
        {availablePorts[item.switch_id]
          // Filter out ports that are already selected by ANY server in this form
          .filter(port => {
            // Get all port numbers selected across all servers for this switch
            const selectedPortNumbers = items
              .filter(server => server.switch_id === item.switch_id)
              .flatMap(server => [
                server.port1,
                server.port2,
                server.port3,
                server.port4
              ])
              .filter(portNum => portNum); // Remove nulls/undefined/empty strings

            // Only include this port if it's not already selected
            return !selectedPortNumbers.includes(port.port_number);
          })
          .map(port => (
            <option key={port.id} value={port.id}>
              {port.port_name || `Port ${port.port_number}`} {port.max_speed ? `(${port.max_speed} Mbps)` : ''}
            </option>
          ))}
      </select>
      <button
        onClick={() => fetchAvailablePorts(item.switch_id)}
        className="p-1 text-gray-500 hover:text-indigo-700 rounded"
        title="Refresh available ports"
      >
        <RefreshCw className={`w-3 h-3 ${loading ? 'animate-spin' : ''}`} />
      </button>
    </div>
  )
)}
  </div>
</td>
              <td className="p-2">
                <div className="flex space-x-2">
                  <button
                    onClick={() => setSelectedDeviceForPorts(itemIndex)}
                    className="p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200"
                    disabled={!item.switch_id}
                    title={!item.switch_id ? "Select a switch first" : "Select ports"}
                  >
                    <Link className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setRowAsTemplate(itemIndex)}
                    className="p-1 text-indigo-700 hover:text-indigo-900 transition-colors"
                    title="Use as template for new rows"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => removeRow(itemIndex)}
                    className="p-1 text-red-600 hover:text-red-800 transition-colors"
                    disabled={items.length === 1}
                    title="Remove row"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </td>
            </tr>
          );
        })}
      </tbody>
    </table>

    {/* Port Selection Modal */}
    {selectedDeviceForPorts !== null && (
  <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-2">
    <div className="bg-white rounded-lg shadow-lg w-full max-w-3xl max-h-[95vh] flex flex-col">
      <div className="p-4 border-b flex justify-between items-center">
        <h3 className="font-bold">
          Select Ports for {items[selectedDeviceForPorts]?.label || `Device ${selectedDeviceForPorts + 1}`}
        </h3>
        <button
          onClick={() => setSelectedDeviceForPorts(null)}
          className="text-gray-500 hover:text-gray-700"
        >
          <XCircle className="w-5 h-5" />
        </button>
      </div>

      <div className="p-4 flex-grow overflow-auto">
        <SwitchPortsManager
          switchId={items[selectedDeviceForPorts]?.switch_id}
          onPortSelected={(port) => {
            // Determine which port field to use based on what's already assigned
            const currentItem = items[selectedDeviceForPorts];
            let portField = 'port1';
            let portSpeedField = 'port1_speed';



            // Check if this port is already assigned to ANY server
            const isPortAlreadyAssigned = items.some(item => {
              // Skip the current item we're editing
              if (item === items[selectedDeviceForPorts]) return false;

              // Check all port fields
              return [item.port1, item.port2, item.port3, item.port4].includes(port.port_number);
            });

            if (isPortAlreadyAssigned) {
              alert(`Port ${port.port_number} is already assigned to another server in this form.`);
              return;
            }

            // Update the item with the selected port
            const newItems = [...items];
            newItems[selectedDeviceForPorts] = {
              ...newItems[selectedDeviceForPorts],
              [portField]: port.port_number,
              [portSpeedField]: port.max_speed
            };
            setItems(newItems);

            // Optionally close the modal if all required ports are assigned
            const updatedItem = newItems[selectedDeviceForPorts];
            if (
              (deviceType === 'blade' && updatedItem.port1 && updatedItem.port2) ||
              (deviceType === 'dedicated' && updatedItem.port1 && updatedItem.port2 && updatedItem.port3 && updatedItem.port4)
            ) {
              setSelectedDeviceForPorts(null);
            }
          }}
          // Pass already assigned ports to be filtered out
          alreadyAssignedPorts={items.flatMap(item =>
            [item.port1, item.port2, item.port3, item.port4].filter(Boolean)
          )}
        />
      </div>

      <div className="p-4 border-t">
        <div className="flex justify-end">
          <button
            onClick={() => setSelectedDeviceForPorts(null)}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
)}
  </>
)}
          {/* SNMP Settings Tab - Only for switches */}
{activeTab === 'snmp' && deviceType === 'switch' && (
  <table className="w-full">
    <thead>
      <tr className="text-xs text-gray-500 border-b">
        <th className="p-2 text-left font-medium w-12">#</th>
        <th className="p-2 text-left font-medium">Label</th>

        <th className="p-2 text-left font-medium">SNMP Community</th>
        <th className="p-2 text-left font-medium">SNMP Version</th>
        <th className="p-2 text-left font-medium w-20">Actions</th>
      </tr>
    </thead>
    <tbody>
      {getCurrentPageItems().map((item, index) => {
        const itemIndex = currentPage * itemsPerPage + index;
        return (
          <tr key={itemIndex} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} h-14`}>
            <td className="p-2">{itemIndex + 1}</td>
            <td className="p-2 font-medium">{item.label || `Switch ${itemIndex + 1}`}</td>

            <td className="p-2">
              <div className="relative">
                <input
                  type={passwordVisibility[`${itemIndex}-snmp_community`] ? 'text' : 'password'}
                  value={item.snmp_community || ''}
                  onChange={(e) => handleItemChange(itemIndex, 'snmp_community', e.target.value)}
                  className="w-full p-1.5 border border-gray-300 rounded-md text-sm pr-8"
                  placeholder="Private Community String"
                />
                <button
                  type="button"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-indigo-700"
                  onClick={() => togglePasswordVisibility(itemIndex, 'snmp_community')}
                >
                  {passwordVisibility[`${itemIndex}-snmp_community`] ? (
                    <Eye className="w-4 h-4" />
                  ) : (
                    <EyeOff className="w-4 h-4" />
                  )}
                </button>
              </div>

            </td>
            <td className="p-2">
              <select
                value={item.snmp_version || '2c'}
                onChange={(e) => handleItemChange(itemIndex, 'snmp_version', e.target.value)}
                className="w-full p-1.5 border border-gray-300 rounded-md text-sm"
              >
                <option value="1">Version 1</option>
                <option value="2c">Version 2c</option>
                <option value="3">Version 3</option>
              </select>
            </td>
            <td className="p-2">
              <div className="flex space-x-2">
                <button
                  onClick={() => setRowAsTemplate(itemIndex)}
                  className="p-1 text-indigo-700 hover:text-indigo-900 transition-colors"
                  title="Use as template for new rows"
                >
                  <Copy className="w-4 h-4" />
                </button>
                <button
                  onClick={() => removeRow(itemIndex)}
                  className="p-1 text-red-600 hover:text-red-800 transition-colors"
                  disabled={items.length === 1}
                  title="Remove row"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </td>
          </tr>
        );
      })}
    </tbody>
  </table>
)}
          {/* Location Tab - For all except blade servers */}
          {activeTab === 'location' && (
            <table className="w-full">
              <thead>
                <tr className="text-xs text-gray-500 border-b">
                  <th className="p-2 text-left font-medium w-12">#</th>
                  <th className="p-2 text-left font-medium">Label</th>
                  <th className="p-2 text-left font-medium">City*</th>
                  <th className="p-2 text-left font-medium">Rack*</th>
                  <th className="p-2 text-left font-medium">Top Position</th>
                  {(deviceType === 'dedicated') && (
                  <th className="p-2 text-left font-medium">Size U</th>

                  )}
                  <th className="p-2 text-left font-medium w-20">Actions</th>
                </tr>
              </thead>
              <tbody>
                {getCurrentPageItems().map((item, index) => {
                  const itemIndex = currentPage * itemsPerPage + index;
                  return (
                    <tr key={itemIndex} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} h-14`}>
                      <td className="p-2">{itemIndex + 1}</td>
                      <td className="p-2 font-medium">{item.label || `Device ${itemIndex + 1}`}</td>
                      <td className="p-2">
                        <select
                          value={item.city_id}
                          onChange={(e) => handleItemChange(itemIndex, 'city_id', e.target.value)}
                          className={`w-full p-1.5 border ${
                            errorMessages[itemIndex]?.city_id ? 'border-red-500' : 'border-gray-300'
                          } rounded-md text-sm`}
                          disabled={deviceType === 'blade'} // Disabled for blade servers, they inherit from chassis
                        >
                          <option value="">Select City</option>
                          {cities.map(city => (
                            <option key={city.id} value={city.id}>
                              {city.city} {city.datacenter ? `(${city.datacenter})` : ''}
                            </option>
                          ))}
                        </select>
                        {errorMessages[itemIndex]?.city_id && (
                          <p className="text-xs text-red-500 mt-1">{errorMessages[itemIndex].city_id}</p>
                        )}
                        {deviceType === 'blade' && (
                          <p className="text-xs text-gray-500 mt-1">Inherits from chassis</p>
                        )}
                      </td>
{/* Rack dropdown with city filtering */}
<td className="p-2">
  <select
    value={item.rack_id || ''}
    onChange={(e) => handleItemChange(itemIndex, 'rack_id', e.target.value)}
    className={`w-full p-1.5 border ${
      errorMessages[itemIndex]?.rack_id ? 'border-red-500' : 'border-gray-300'
    } rounded-md text-sm`}
    disabled={!item.city_id} // Disable if no city is selected
  >
    <option value="">Select Rack</option>
    {!item.city_id ? (
      <option disabled>Please select a city first</option>
    ) : (
      racks
        .filter(rack => rack.city == item.city_id) // Filter racks by selected city
        .map(rack => (
          <option key={rack.id} value={rack.id}>{rack.rack_name}</option>
        ))
    )}
  </select>
  {errorMessages[itemIndex]?.rack_id && (
    <p className="text-xs text-red-500 mt-1">{errorMessages[itemIndex].rack_id}</p>
  )}

</td>
                      <td className="p-2">
                        <input
                          type="text"
                          value={item.position}
                          onChange={(e) => handleItemChange(itemIndex, 'position', e.target.value)}
                          className="w-full p-1.5 border border-gray-300 rounded-md text-sm"
                          placeholder={deviceType === 'switch' ? "e.g. 38" : "e.g. 10"}
                          disabled={deviceType === 'blade'} // Disabled for blade servers, they are in chassis
                        />
                        {deviceType === 'blade' && (
                          <p className="text-xs text-gray-500 mt-1">Defined by chassis bay</p>
                        )}
                      </td>
                      {(deviceType === 'dedicated') && (
                         <td className="p-2">
                <input
                type="text"
                value={item.size}
                onChange={(e) => handleItemChange(itemIndex, 'size', e.target.value)}
                className="w-full p-1.5 border border-gray-300 rounded-md text-sm"
                placeholder={'e.g. 2 '}

              />
 </td>
                  )}
                      <td className="p-2">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => setRowAsTemplate(itemIndex)}
                            className="p-1 text-indigo-700 hover:text-indigo-900 transition-colors"
                            title="Use as template for new rows"
                          >
                            <Copy className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => removeRow(itemIndex)}
                            className="p-1 text-red-600 hover:text-red-800 transition-colors"
                            disabled={items.length === 1}
                            title="Remove row"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          )}

          {/* Chassis Tab - Only for blade servers */}
          {activeTab === 'chassis' && deviceType === 'blade' && (
            <table className="w-full">
              <thead>
                <tr className="text-xs text-gray-500 border-b">
                  <th className="p-2 text-left font-medium w-12">#</th>
                  <th className="p-2 text-left font-medium">Label</th>
                  <th className="p-2 text-left font-medium">Chassis*</th>
                  <th className="p-2 text-left font-medium w-20">Actions</th>
                </tr>
              </thead>
              <tbody>
                {getCurrentPageItems().map((item, index) => {
                  const itemIndex = currentPage * itemsPerPage + index;
                  return (
                    <tr key={itemIndex} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} h-14`}>
                      <td className="p-2">{itemIndex + 1}</td>
                      <td className="p-2 font-medium">{item.label || `Server ${itemIndex + 1}`}</td>
                      <td className="p-2">
                        <select
                          value={item.chassis_id}
                          onChange={(e) => handleItemChange(itemIndex, 'chassis_id', e.target.value)}
                          className={`w-full p-1.5 border ${
                            errorMessages[itemIndex]?.chassis_id ? 'border-red-500' : 'border-gray-300'
                          } rounded-md text-sm`}
                        >
                          <option value="">Select Chassis</option>
                          {chassis.length > 0 ? (
                            chassis.map(c => {
                              const freeBays = 4 - [c.bay1, c.bay2, c.bay3, c.bay4]
                                .filter(bay => bay && bay !== '0' && bay !== 0).length;
                              return (
                                <option key={c.id} value={c.id}>
                                  {c.label} {c.city_name && `(${c.city_name})`} - {freeBays} free bay{freeBays !== 1 ? 's' : ''}
                                </option>
                              );
                            })
                          ) : (
                            <option value="" disabled>No chassis available</option>
                          )}
                        </select>
                        {errorMessages[itemIndex]?.chassis_id && (
                          <p className="text-xs text-red-500 mt-1">{errorMessages[itemIndex].chassis_id}</p>
                        )}
                      </td>
                      <td className="p-2">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => setRowAsTemplate(itemIndex)}
                            className="p-1 text-indigo-700 hover:text-indigo-900 transition-colors"
                            title="Use as template for new rows"
                          >
                            <Copy className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => removeRow(itemIndex)}
                            className="p-1 text-red-600 hover:text-red-800 transition-colors"
                            disabled={items.length === 1}
                            title="Remove row"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          )}

          {/* IPMI Tab - Only for servers */}
          {activeTab === 'ipmi' && (deviceType === 'blade' || deviceType === 'dedicated') && (
            <table className="w-full">
              <thead>
                <tr className="text-xs text-gray-500 border-b">
                  <th className="p-2 text-left font-medium w-12">#</th>
                  <th className="p-2 text-left font-medium">Label</th>
                  <th className="p-2 text-left font-medium">IPMI Address</th>
                  <th className="p-2 text-left font-medium">IPMI Root Password</th>
                  <th className="p-2 text-left font-medium">MAC Address</th>
                  <th className="p-2 text-left font-medium w-20">Actions</th>
                </tr>
              </thead>
              <tbody>
                {getCurrentPageItems().map((item, index) => {
                  const itemIndex = currentPage * itemsPerPage + index;
                  return (
                    <tr key={itemIndex} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} h-14`}>
                      <td className="p-2">{itemIndex + 1}</td>
                      <td className="p-2 font-medium">{item.label || `Device ${itemIndex + 1}`}</td>
                      <td className="p-2">
                        <input
                          type="text"
                          value={item.ipmi}
                          onChange={(e) => handleItemChange(itemIndex, 'ipmi', e.target.value)}
                          className="w-full p-1.5 border border-gray-300 rounded-md text-sm"
                          placeholder="IPMI Address"
                        />
                      </td>
                      <td className="p-2">
                        <div className="relative">
                          <input
                            type={passwordVisibility[`${itemIndex}-ipmi_root_pass`] ? 'text' : 'password'}
                            value={item.ipmi_root_pass || ''}
                            onChange={(e) => handleItemChange(itemIndex, 'ipmi_root_pass', e.target.value)}
                            className="w-full p-1.5 border border-gray-300 rounded-md text-sm pr-8"
                            placeholder="Root Password"
                          />
                          <button
                            type="button"
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-indigo-700"
                            onClick={() => togglePasswordVisibility(itemIndex, 'ipmi_root_pass')}
                          >
                            {passwordVisibility[`${itemIndex}-ipmi_root_pass`] ? (
                              <Eye className="w-4 h-4" />
                            ) : (
                              <EyeOff className="w-4 h-4" />
                            )}
                          </button>
                        </div>
                      </td>
                      <td className="p-2">
                        <div className="relative">
                          <input
                            type="text"
                            value={item.mac}
                            onChange={(e) => handleItemChange(itemIndex, 'mac', e.target.value)}
                            className={`w-full p-1.5 border ${
                              macResults[itemIndex]?.success ? 'border-green-500 bg-green-50' :
                              macResults[itemIndex]?.error ? 'border-red-300 bg-red-50' :
                              'border-gray-300'
                            } rounded-md text-sm`}
                            placeholder="MAC Address"
                          />

                          {/* Status indicators */}
                          {macResults[`${itemIndex}-progress`] && (
                            <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center">
                              <RefreshCw className="w-4 h-4 animate-spin text-blue-600" />
                            </div>
                          )}

                          {macResults[itemIndex]?.success && (
                            <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center">
                              <CheckCircle className="w-4 h-4 text-green-600" />
                            </div>
                          )}

                          {macResults[itemIndex]?.error && (
                            <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center"
                                title={macResults[itemIndex].error}>
                              <AlertTriangle className="w-4 h-4 text-red-500" />
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="p-2">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => setRowAsTemplate(itemIndex)}
                            className="p-1 text-indigo-700 hover:text-indigo-900 transition-colors"
                            title="Use as template for new rows"
                          >
                            <Copy className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => removeRow(itemIndex)}
                            className="p-1 text-red-600 hover:text-red-800 transition-colors"
                            disabled={items.length === 1}
                            title="Remove row"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          )}
        </div>

        {/* Footer Actions */}
        <div className="p-4 border-t flex justify-between items-center bg-white sticky bottom-0">
          <div className="text-sm text-gray-500">
            {items.filter(item => item.label.trim() !== '').length} {getDeviceTypeName()}{items.filter(item => item.label.trim() !== '').length !== 1 ? 's' : ''} configured
          </div>

          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleSubmit}
              disabled={loading}
              className="px-4 py-2 bg-indigo-700 text-white rounded-md text-sm hover:bg-indigo-800 flex items-center"
            >
              {loading ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Adding...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Add {items.filter(item => item.label.trim() !== '').length} {getDeviceTypeName()}{items.filter(item => item.label.trim() !== '').length !== 1 ? 's' : ''}
                </>
              )}
            </button>
          </div>
        </div>

        {/* Add CPU Model Modal */}
        {showAddCpuModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-bold">Add New CPU Model</h3>
                <button
                  onClick={() => {
                    setShowAddCpuModal(false);
                    setNewCpuModel('');
                  }}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <XCircle className="w-5 h-5" />
                </button>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">CPU Model Name</label>
                <input
                  type="text"
                  value={newCpuModel}
                  onChange={(e) => setNewCpuModel(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="e.g. Intel Xeon E5-2680 v4 (14 cores, 2.4GHz)"
                />
              </div>

              <div className="flex justify-end space-x-3 pt-4 border-t">
                <button
                  type="button"
                  onClick={() => {
                    setShowAddCpuModal(false);
                    setNewCpuModel('');
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
                  disabled={loading}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleAddCpuModel}
                  className="px-4 py-2 bg-indigo-700 text-white rounded-md text-sm hover:bg-indigo-800 flex items-center"
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Adding...
                    </>
                  ) : (
                    <>
                      <Plus className="w-4 h-4 mr-2" />
                      Add CPU Model
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Add RAM Configuration Modal */}
        {showAddRamModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-bold">Add New RAM Configuration</h3>
                <button
                  onClick={() => {
                    setShowAddRamModal(false);
                    setNewRamConfig({ size: '', description: '' });
                  }}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <XCircle className="w-5 h-5" />
                </button>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">RAM Size (GB)</label>
                <input
                  type="number"
                  value={newRamConfig.size}
                  onChange={(e) => setNewRamConfig({...newRamConfig, size: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="e.g. 64"
                />
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <input
                  type="text"
                  value={newRamConfig.description}
                  onChange={(e) => setNewRamConfig({...newRamConfig, description: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="e.g. 64GB DDR4"
                />
              </div>

              <div className="flex justify-end space-x-3 pt-4 border-t">
                <button
                  type="button"
                  onClick={() => {
                    setShowAddRamModal(false);
                    setNewRamConfig({ size: '', description: '' });
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
                  disabled={loading}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleAddRamConfig}
                  className="px-4 py-2 bg-indigo-700 text-white rounded-md text-sm hover:bg-indigo-800 flex items-center"
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Adding...
                    </>
                  ) : (
                    <>
                      <Plus className="w-4 h-4 mr-2" />
                      Add RAM Configuration
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Add Switch Model Modal */}
{showAddSwitchModelModal && (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold">Add New Switch Model</h3>
        <button
          onClick={() => {
            setShowAddSwitchModelModal(false);
            setNewSwitchModel({ name: '', size: 1 });
          }}
          className="text-gray-500 hover:text-gray-700"
        >
          <XCircle className="w-5 h-5" />
        </button>
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">Switch Model Name</label>
        <input
          type="text"
          value={newSwitchModel.name}
          onChange={(e) => setNewSwitchModel({...newSwitchModel, name: e.target.value})}
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
          placeholder="e.g. Cisco Nexus 9396TX"
        />
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">Size (U)</label>
        <input
          type="number"
          min="1"
          max="10"
          value={newSwitchModel.size}
          onChange={(e) => setNewSwitchModel({...newSwitchModel, size: parseInt(e.target.value)})}
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
          placeholder="e.g. 1"
        />
      </div>

      <div className="flex justify-end space-x-3 pt-4 border-t">
        <button
          type="button"
          onClick={() => {
            setShowAddSwitchModelModal(false);
            setNewSwitchModel({ name: '', size: 1 });
          }}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
          disabled={loading}
        >
          Cancel
        </button>
        <button
          type="button"
          onClick={handleAddSwitchModel}
          className="px-4 py-2 bg-indigo-700 text-white rounded-md text-sm hover:bg-indigo-800 flex items-center"
          disabled={loading}
        >
          {loading ? (
            <>
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              Adding...
            </>
          ) : (
            <>
              <Plus className="w-4 h-4 mr-2" />
              Add Switch Model
            </>
          )}
        </button>
      </div>
    </div>
  </div>
)}
      </div>

      {/* Add this modal component at the end of your component, near the other modals */}
{showAddChassisModelModal && (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold">Add New Chassis Model</h3>
        <button
          onClick={() => {
            setShowAddChassisModelModal(false);
            setNewChassisModel({ name: '', size: 2, bay_count: 8 });
          }}
          className="text-gray-500 hover:text-gray-700"
        >
          <XCircle className="w-5 h-5" />
        </button>
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">Chassis Model Name</label>
        <input
          type="text"
          value={newChassisModel.name}
          onChange={(e) => setNewChassisModel({...newChassisModel, name: e.target.value})}
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
          placeholder="e.g. PowerEdge M1000e"
        />
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">Size (U)</label>
        <input
          type="number"
          min="1"
          max="42"
          value={newChassisModel.size}
          onChange={(e) => setNewChassisModel({...newChassisModel, size: parseInt(e.target.value)})}
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
          placeholder="e.g. 10"
        />
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">Bay Count</label>
        <input
          type="number"
          min="1"
          max="32"
          value={newChassisModel.bay_count}
          onChange={(e) => setNewChassisModel({...newChassisModel, bay_count: parseInt(e.target.value)})}
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
          placeholder="e.g. 16"
        />
      </div>

      <div className="flex justify-end space-x-3 pt-4 border-t">
        <button
          type="button"
          onClick={() => {
            setShowAddChassisModelModal(false);
            setNewChassisModel({ name: '', size: 2, bay_count: 8 });
          }}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
          disabled={loading}
        >
          Cancel
        </button>
        <button
          type="button"
          onClick={handleAddChassisModel}
          className="px-4 py-2 bg-indigo-700 text-white rounded-md text-sm hover:bg-indigo-800 flex items-center"
          disabled={loading}
        >
          {loading ? (
            <>
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              Adding...
            </>
          ) : (
            <>
              <Plus className="w-4 h-4 mr-2" />
              Add Chassis Model
            </>
          )}
        </button>
      </div>
    </div>
  </div>
)}
    </div>
  );
};

export default BulkDeviceAddition;