import React, { useState, useEffect } from 'react';
import { ChevronRight, ChevronDown, Network, Shield, Link, CheckCircle, AlertCircle, Tag, RefreshCw, List, Unlink, Server, Cpu, Eye, EyeOff } from 'lucide-react';
import axios from 'axios';
import { API_URL } from '../config';

// Modified to use passed subnets prop instead of fetching independently
const EnhancedSubnetTree = ({
  subnets = [],
  currentSubnetId,
  onSubnetSelect,
  onGenerateIps,
  onAllocateIp,
  onDeallocateIp,
  refreshTrigger = 0 // Add a refresh trigger prop
}) => {
  const [subnetRelationships, setSubnetRelationships] = useState({});
  const [expandedNodes, setExpandedNodes] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [subnetIps, setSubnetIps] = useState({});
  const [loadingIps, setLoadingIps] = useState({});
  const [generatingIps, setGeneratingIps] = useState({});
  const indentSize = 20; // pixels per level

  // Build subnet relationships when subnets array changes
  useEffect(() => {
    if (subnets.length > 0) {
      console.log('CompactSubnetTree received subnets:', subnets);
      buildSubnetRelationships(subnets);
    } else {
      console.warn('CompactSubnetTree received empty subnets array');
    }
  }, [subnets]);

  // When currentSubnetId changes, ensure it's expanded
  useEffect(() => {
    if (currentSubnetId && subnets.length > 0) {
      expandCurrentSubnetPath(subnets, currentSubnetId);
    }
  }, [currentSubnetId, subnets]);

  // We're not auto-loading IPs for expanded subnets anymore
  // Instead, we'll add a separate state to track which IP sections are expanded
  const [expandedIpSections, setExpandedIpSections] = useState({});

  // Load IPs only for subnets with explicitly expanded IP sections
  useEffect(() => {
    if (Object.keys(expandedIpSections).length > 0 && subnets.length > 0) {
      console.log('Loading IPs for subnets with expanded IP sections');

      Object.keys(expandedIpSections).forEach(nodeId => {
        if (expandedIpSections[nodeId]) {
          const subnet = subnets.find(s => s.id === nodeId);
          if (subnet && subnet.cidr && subnet.cidr.includes('.')) {
            console.log(`Loading IPs for subnet ${nodeId} with expanded IP section`);
            fetchSubnetIps(subnet);
          }
        }
      });
    }
  }, [expandedIpSections, subnets]);

  // Check for IPs for all expanded subnets
  useEffect(() => {
    if (Object.keys(expandedNodes).length > 0 && subnets.length > 0) {
      console.log('Checking for IPs for all expanded subnets');

      Object.keys(expandedNodes).forEach(nodeId => {
        if (expandedNodes[nodeId]) {
          const subnet = subnets.find(s => s.id === nodeId);
          if (subnet && subnet.cidr && subnet.cidr.includes('.') && !subnetIps[subnet.id] && !loadingIps[subnet.id]) {
            console.log(`Checking if subnet ${nodeId} has IPs`);
            fetchSubnetIps(subnet);
          }
        }
      });
    }
  }, [expandedNodes, subnets]);

  // Find and expand the path to the current subnet
  const expandCurrentSubnetPath = (subnetsData, targetId) => {
    // Find all potential parent subnets
    const currentSubnet = subnetsData.find(s => s.id === targetId);
    if (!currentSubnet) return;

    const currentCidr = currentSubnet.cidr;
    const toExpand = {};

    // Mark the current subnet to be expanded
    toExpand[targetId] = true;

    // Find and expand all parent subnets
    subnetsData.forEach(subnet => {
      if (subnet.id !== targetId && isParentSubnet(subnet.cidr, currentCidr)) {
        toExpand[subnet.id] = true;
      }
    });

    // Update expanded nodes without auto-loading IPs
    setExpandedNodes(prev => {
      const newExpandedNodes = {
        ...prev,
        ...toExpand
      };

      return newExpandedNodes;
    });
  };

  // Check if parentCidr is a parent of childCidr
  const isParentSubnet = (parentCidr, childCidr) => {
    // Handle undefined or invalid CIDR
    if (!parentCidr || !childCidr || !parentCidr.includes('/') || !childCidr.includes('/')) {
      console.warn('Invalid CIDR format:', { parentCidr, childCidr });
      return false;
    }

    // Extract network addresses and masks
    const [parentNetwork, parentMask] = parentCidr.split('/');
    const [childNetwork, childMask] = childCidr.split('/');

    // Validate IP addresses
    if (!parentNetwork || !childNetwork) {
      console.warn('Invalid network address:', { parentNetwork, childNetwork });
      return false;
    }

    // Parse masks as integers
    const parentMaskInt = parseInt(parentMask, 10);
    const childMaskInt = parseInt(childMask, 10);

    // Child must have a larger mask (smaller network)
    if (childMaskInt <= parentMaskInt) {
      return false;
    }

    try {
      // Convert to binary for subnet comparison
      const parentBinary = ipToBinary(parentNetwork);
      const childBinary = ipToBinary(childNetwork);

      // The first parentMask bits should be identical for the child to be in the parent subnet
      return childBinary.substring(0, parentMaskInt) === parentBinary.substring(0, parentMaskInt);
    } catch (error) {
      console.error('Error comparing subnets:', error, { parentCidr, childCidr });
      return false;
    }
  };

  // Helper to convert IP to binary string
  const ipToBinary = (ip) => {
    if (!ip || typeof ip !== 'string') {
      throw new Error(`Invalid IP address: ${ip}`);
    }

    // Handle IPv4 addresses
    if (ip.includes('.')) {
      const octets = ip.split('.');
      if (octets.length !== 4) {
        throw new Error(`Invalid IPv4 address format: ${ip}`);
      }

      return octets
        .map(octet => {
          const num = parseInt(octet, 10);
          if (isNaN(num) || num < 0 || num > 255) {
            throw new Error(`Invalid IPv4 octet: ${octet} in ${ip}`);
          }
          return num.toString(2).padStart(8, '0');
        })
        .join('');
    }

    // For IPv6 addresses, we'll just return a placeholder
    // This is a simplified implementation as IPv6 is more complex
    return '0'.repeat(128);
  };

  // Calculate mask size from CIDR notation
  const getMaskSize = (cidr) => {
    const parts = cidr.split('/');
    return parts.length === 2 ? parseInt(parts[1], 10) : 0;
  };

  // Builds parent-child relationships - key to subnet hierarchy
  const buildSubnetRelationships = (subnetsData) => {
    console.log('Building relationships for', subnetsData.length, 'subnets');

    // Validate subnet data
    const validSubnets = subnetsData.filter(subnet => {
      if (!subnet || !subnet.cidr || !subnet.id) {
        console.warn('Invalid subnet data:', subnet);
        return false;
      }
      return true;
    });

    console.log('Valid subnets for relationship building:', validSubnets.length);

    const relationships = {};

    // Expand all subnets by default (not just root subnets)
    const expandAllSubnets = {};
    validSubnets.forEach(subnet => {
      expandAllSubnets[subnet.id] = true;
    });
    setExpandedNodes(prev => ({
      ...prev,
      ...expandAllSubnets
    }));

    console.log('All subnets to auto-expand:', validSubnets.length);

    validSubnets.forEach(childSubnet => {
      // Find the most specific parent for each subnet
      let bestParentId = null;
      let bestParentMaskSize = -1;

      validSubnets.forEach(potentialParent => {
        if (childSubnet.id !== potentialParent.id) { // Don't compare subnet to itself
          try {
            if (isParentSubnet(potentialParent.cidr, childSubnet.cidr)) {
              // Find parent with largest mask (most specific)
              const parentMaskSize = getMaskSize(potentialParent.cidr);
              if (parentMaskSize > bestParentMaskSize) {
                bestParentId = potentialParent.id;
                bestParentMaskSize = parentMaskSize;
              }
            }
          } catch (error) {
            console.error('Error checking parent-child relationship:', error, {
              parent: potentialParent.cidr,
              child: childSubnet.cidr
            });
          }
        }
      });

      // Add child to its parent's children list
      if (bestParentId !== null) {
        relationships[bestParentId] = relationships[bestParentId] || [];
        relationships[bestParentId].push(childSubnet.id);
      }
    });

    console.log('Built relationships:', relationships);
    console.log('Number of parent subnets:', Object.keys(relationships).length);

    // Count total children
    let totalChildren = 0;
    Object.values(relationships).forEach(children => {
      totalChildren += children.length;
    });
    console.log('Total child subnets:', totalChildren);

    setSubnetRelationships(relationships);
  };

  // Toggle node expansion
  const toggleNode = (e, nodeId) => {
    e.stopPropagation();
    const isExpanding = !expandedNodes[nodeId];

    console.log(`${isExpanding ? 'Expanding' : 'Collapsing'} node ${nodeId}`);

    setExpandedNodes(prev => ({
      ...prev,
      [nodeId]: isExpanding
    }));
  };

  // Toggle IP section expansion
  const toggleIpSection = (e, nodeId) => {
    e.stopPropagation();
    const isExpanding = !expandedIpSections[nodeId];

    console.log(`${isExpanding ? 'Expanding' : 'Collapsing'} IP section for node ${nodeId}`);

    setExpandedIpSections(prev => ({
      ...prev,
      [nodeId]: isExpanding
    }));

    // If we're expanding the IP section, fetch the IPs
    if (isExpanding) {
      const subnet = subnets.find(s => s.id === nodeId);
      if (subnet && subnet.cidr && subnet.cidr.includes('.')) {
        console.log(`Fetching IPs for subnet ${nodeId} on IP section expand`);
        fetchSubnetIps(subnet, true);
      }
    }
  };

  // Fetch IP addresses for a subnet
  const fetchSubnetIps = async (subnet, forceRefresh = false) => {
    try {
      if (!subnet || !subnet.id) {
        console.error('Invalid subnet passed to fetchSubnetIps:', subnet);
        return;
      }

      // Extract subnet ID from SUB-xxxx format if needed
      const subnetId = typeof subnet.id === 'string' && subnet.id.startsWith('SUB-')
        ? parseInt(subnet.id.replace('SUB-', ''))
        : subnet.id;

      console.log(`fetchSubnetIps called for subnet ${subnet.id} (ID: ${subnetId}), force=${forceRefresh}`);
      console.log('Current subnetIps state:', subnetIps);
      console.log('Current loadingIps state:', loadingIps);
      console.log('Current expandedNodes state:', expandedNodes);

      // Skip if we already have IPs for this subnet and not forcing refresh
      if (!forceRefresh && subnetIps[subnet.id] && !loadingIps[subnet.id]) {
        console.log(`Using cached IPs for subnet ${subnet.id}`);
        return;
      }

      // Mark as loading
      setLoadingIps(prev => {
        const newState = {
          ...prev,
          [subnet.id]: true
        };
        console.log('Setting loadingIps state:', newState);
        return newState;
      });

      console.log(`Fetching IPs for subnet ${subnet.id}${forceRefresh ? ' (forced refresh)' : ''}`);

      // Make the API request
      const response = await axios.post(
        `${API_URL}/api_admin_subnets.php?f=get_subnet_ips`,
        {
          subnet_id: subnetId,
          token: localStorage.getItem('admin_token')
        }
      );

      // Check if the response indicates success
      if (response.data && response.data.success) {
        console.log(`Received ${response.data.ips.length} IPs for subnet ${subnet.id}:`, response.data.ips);

        // Store the IPs in state
        setSubnetIps(prev => {
          const newState = {
            ...prev,
            [subnet.id]: response.data.ips
          };
          console.log('Setting subnetIps state:', newState);
          return newState;
        });
      } else {
        console.error('API returned without success:', response.data);
      }
    } catch (err) {
      console.error('Error fetching subnet IPs:', err);
    } finally {
      // Mark as not loading
      setLoadingIps(prev => {
        const newState = {
          ...prev,
          [subnet.id]: false
        };
        console.log('Setting loadingIps state (in finally):', newState);
        return newState;
      });
    }
  };

  // We've removed the refreshIps function and now use fetchSubnetIps directly

  // Use refreshTrigger to refresh subnets with expanded IP sections when it changes
  useEffect(() => {
    if (refreshTrigger > 0) {
      console.log('Refresh trigger activated in CompactSubnetTree, value:', refreshTrigger);

      // Refresh all subnets with IPv4 addresses, regardless of whether their IP sections are expanded
      // This ensures we catch any changes to IP addresses, even for subnets that aren't currently expanded
      subnets.forEach(subnet => {
        if (subnet && subnet.cidr && subnet.cidr.includes('.')) {
          console.log(`Refreshing IPs for subnet ${subnet.id} due to refresh trigger`);
          fetchSubnetIps(subnet, true); // Force refresh
        }
      });

      // Also expand the IP section for the currently selected subnet if it has one
      if (currentSubnetId) {
        const currentSubnet = subnets.find(s => s.id === currentSubnetId);
        if (currentSubnet && currentSubnet.cidr && currentSubnet.cidr.includes('.')) {
          console.log(`Auto-expanding IP section for current subnet ${currentSubnetId} due to refresh trigger`);
          setExpandedIpSections(prev => ({
            ...prev,
            [currentSubnetId]: true
          }));

          // Also expand the node itself
          setExpandedNodes(prev => ({
            ...prev,
            [currentSubnetId]: true
          }));

          // Force refresh the IPs for this subnet
          fetchSubnetIps(currentSubnet, true);
        }
      }
    }
  }, [refreshTrigger]);

  // Render tree branches
  const TreeBranch = ({ level, isLast }) => {
    if (level === 0) return null;

    return (
      <>
        {/* Vertical line */}
        <div
          className="absolute border-l border-gray-300"
          style={{
            left: `${(level-1) * indentSize + 9}px`,
            top: 0,
            bottom: isLast ? '50%' : 0,
            width: '1px'
          }}
        />

        {/* Horizontal line */}
        <div
          className="absolute border-t border-gray-300"
          style={{
            left: `${(level-1) * indentSize + 9}px`,
            width: `${indentSize/2}px`,
            top: '50%'
          }}
        />
      </>
    );
  };

  // Render status indicator with server name if assigned
  const renderStatusIndicator = (subnet) => {
    // Check if this subnet has any allocated IPs
    const hasAllocatedIps = subnetIps[subnet.id] && subnetIps[subnet.id].some(ip => ip.is_used === '1' || ip.is_used === 1);

    if (subnet.status === 'Available') {
      return (
        <span className="ml-1 text-xs text-green-600 flex items-center">
          <CheckCircle className="w-3 h-3 mr-1" />
          Available
        </span>
      );
    } else if (subnet.status === 'Assigned') {
      return (
        <span className="ml-1 text-xs text-blue-600 flex items-center">
          <Link className="w-3 h-3 mr-1" />
          {subnet.assignedTo && subnet.assignedTo !== 'Unassigned'
            ? subnet.assignedTo
            : 'Assigned'}
        </span>
      );
    } else if (subnet.status === 'Allocated') {
      return (
        <span className="ml-1 text-xs text-purple-600 flex items-center" title={subnet.description || 'Allocated'}>
          <Tag className="w-3 h-3 mr-1" />
          {subnet.description ?
            (subnet.description.length > 15 ? subnet.description.substring(0, 15) + '...' : subnet.description)
            : 'Allocated'}
        </span>
      );
    } else if (subnet.status === 'Unavailable') {
      // If the subnet has allocated IPs but is marked as unavailable, show a different message
      if (hasAllocatedIps) {
        return (
          <span className="ml-1 text-xs text-blue-600 flex items-center" title="This subnet has some allocated IPs but is still available for more IP allocations">
            <Tag className="w-3 h-3 mr-1" />
            Has IPs
          </span>
        );
      }

      return (
        <span className="ml-1 text-xs text-orange-600 flex items-center" title="This subnet is a parent of allocated subnets">
          <AlertCircle className="w-3 h-3 mr-1" />
          Parent
        </span>
      );
    } else {
      return null;
    }
  };

  // Render a subnet node
  const renderSubnetNode = (subnetId, level = 0, isLastChild = false) => {
    const subnet = subnets.find(s => s.id === subnetId);
    if (!subnet) return null;

    const children = subnetRelationships[subnetId] || [];
    const hasChildren = children.length > 0;
    const isExpanded = !!expandedNodes[subnetId];
    const isCurrentSubnet = subnetId === currentSubnetId;

    return (
      <div key={subnetId} className="relative">
        {/* Tree branch lines */}
        <TreeBranch level={level} isLast={isLastChild} />

        {/* Node content */}
        <div
          className={`flex items-center py-1 my-1 rounded-md transition-colors relative ${isCurrentSubnet ? 'bg-indigo-50 border border-indigo-100' : 'hover:bg-gray-50'} cursor-pointer`}
          style={{ paddingLeft: `${level * indentSize}px` }}
          onClick={() => {
            if (onSubnetSelect) {
              onSubnetSelect(subnet);
            }
          }}
        >
          <div
            className="flex-shrink-0 mr-1 p-0.5"
            onClick={(e) => hasChildren && toggleNode(e, subnetId)}
          >
            {hasChildren ? (
              isExpanded ? (
                <ChevronDown className="w-3 h-3 text-gray-500" />
              ) : (
                <ChevronRight className="w-3 h-3 text-gray-500" />
              )
            ) : (
              <div className="w-3 h-3" /> // Spacer for alignment
            )}
          </div>

          <div className="flex items-center justify-between w-full">
            <div className={`flex items-center text-xs ${isCurrentSubnet ? 'font-semibold' : ''}`}>
              {subnet.category === 'Root' && <Shield className="w-3 h-3 text-indigo-700 mr-1" />}
              <Network className={`w-3 h-3 mr-1 ${subnet.category === 'Root' ? 'text-indigo-700' : 'text-gray-600'}`} />
              <span className="font-mono">{subnet.cidr}</span>
              {renderStatusIndicator(subnet)}
            </div>

      
          </div>
        </div>

        {/* Add IP section toggle button only if it's an IPv4 subnet AND has IPs */}
        {isExpanded && subnet.cidr && subnet.cidr.includes('.') && (
          <div className="relative">
            {/* Only show IP toggle button if subnet has IPs */}
            {(subnetIps[subnet.id] && subnetIps[subnet.id].length > 0) && (
              <div
                className="flex items-center py-1 my-1 text-xs font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
                style={{ paddingLeft: `${level * indentSize + 20}px` }}
                onClick={(e) => toggleIpSection(e, subnet.id)}
              >
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center">
                    {expandedIpSections[subnet.id] ? (
                      <ChevronDown className="w-3 h-3 mr-1 text-gray-500" />
                    ) : (
                      <ChevronRight className="w-3 h-3 mr-1 text-gray-500" />
                    )}
                    <Network className="w-3 h-3 mr-1 text-indigo-600" />
                    <span>IP Addresses ({subnetIps[subnet.id].length})</span>
                  </div>
                  <div className="flex items-center">
                    {expandedIpSections[subnet.id] ? (
                      <EyeOff className="w-3 h-3 mr-2 text-gray-500" />
                    ) : (
                      <Eye className="w-3 h-3 mr-2 text-gray-500" />
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Show loading indicator when checking for IPs */}
            {loadingIps[subnet.id] && !subnetIps[subnet.id] && (
              <div
                className="flex items-center py-2"
                style={{ paddingLeft: `${level * indentSize + 20}px` }}
              >
                <RefreshCw className="w-3 h-3 mr-1 animate-spin text-indigo-600" />
                <span className="text-xs text-gray-600">Checking for IP addresses...</span>
              </div>
            )}

            {/* Render IP addresses if IP section is expanded */}
            {expandedIpSections[subnet.id] && (
              <div className="relative">
                {console.log('Rendering IP section for subnet:', subnet.id, 'Loading:', loadingIps[subnet.id], 'IPs:', subnetIps[subnet.id])}

                {loadingIps[subnet.id] ? (
                  <div
                    className="flex items-center py-2"
                    style={{ paddingLeft: `${level * indentSize + 40}px` }}
                  >
                    <RefreshCw className="w-3 h-3 mr-1 animate-spin text-indigo-600" />
                    <span className="text-xs text-gray-600">Loading IP addresses...</span>
                  </div>
                ) : subnetIps[subnet.id] && subnetIps[subnet.id].length > 0 ? (
                  <div className="max-h-60 overflow-y-auto">
                    {subnetIps[subnet.id].map((ip, idx) => (
                      <div
                        key={idx}
                        className="flex items-center py-1 hover:bg-gray-50 cursor-pointer relative group"
                        style={{ paddingLeft: `${level * indentSize + 40}px` }}
                      >
                        {/* Tree branch line for IP */}
                        <div
                          className="absolute border-l border-gray-300"
                          style={{
                            left: `${level * indentSize + 20}px`,
                            top: 0,
                            height: '100%',
                            width: '1px'
                          }}
                        />

                        {/* Horizontal connector line */}
                        <div
                          className="absolute border-t border-gray-300"
                          style={{
                            left: `${level * indentSize + 20}px`,
                            top: '50%',
                            width: '20px',
                            height: '1px'
                          }}
                        />

                        <div className="flex items-center justify-between w-full">
                          <div className="flex items-center">
                            {/* Show different icon based on allocation type */}
                            {(ip.is_used === '1' || ip.is_used === 1) && ip.assigned_to && ip.assigned_to.includes('IPMI for') ? (
                              <Server className="w-3 h-3 mr-1 text-indigo-600" />
                            ) : (
                              <Network className="w-3 h-3 mr-1 text-gray-400" />
                            )}

                            <span className={`font-mono text-xs ${ip.is_used === '1' || ip.is_used === 1 ? 'text-indigo-600' : 'text-gray-600'}`}>
                              {ip.ip_address}
                            </span>

                            {(ip.is_used === '1' || ip.is_used === 1) && (
                              <span className="ml-1 text-xs text-gray-500">
                                {ip.assigned_to && ip.assigned_to.includes('IPMI for') ? (
                                  <span className="text-indigo-600 font-medium">{ip.assigned_to}</span>
                                ) : (
                                  `(${ip.assigned_to || 'Used'})`
                                )}
                              </span>
                            )}
                          </div>

                          <div className="hidden group-hover:flex items-center mr-2">
                            {(ip.is_used === '1' || ip.is_used === 1) ? (
                              <button
                                className="ml-2 p-1 text-xs text-red-500 hover:text-red-700 rounded hover:bg-gray-100"
                                title="Deallocate IP"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onDeallocateIp && onDeallocateIp(subnet, ip.ip_address);
                                }}
                              >
                                <Unlink className="w-3 h-3" />
                              </button>
                            ) : (
                              <button
                                className="ml-2 p-1 text-xs text-indigo-500 hover:text-indigo-700 rounded hover:bg-gray-100"
                                title="Allocate IP"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onAllocateIp && onAllocateIp(subnet, ip.ip_address);
                                }}
                              >
                                <Tag className="w-3 h-3" />
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
           null
                )}
              </div>
            )}
          </div>
        )}

        {/* Render children if expanded */}
        {isExpanded && hasChildren && (
          <div className="relative">
            {/* Draw additional vertical line from parent to last child */}
            <div
              className="absolute border-l border-gray-300"
              style={{
                left: `${level * indentSize + 9}px`,
                top: 0,
                height: '100%',
                width: '1px'
              }}
            />

            {children.map((childId, idx) =>
              renderSubnetNode(
                childId,
                level + 1,
                idx === children.length - 1 // isLastChild
              )
            )}
          </div>
        )}
      </div>
    );
  };

  // Find the current subnet
  const currentSubnet = subnets.find(s => s.id === currentSubnetId);

  // Find the root subnet that contains the current subnet
  let relevantRootSubnetIds = [];

  if (currentSubnet) {
    if (currentSubnet.category === 'Root') {
      // If current subnet is already a root subnet, only show it
      relevantRootSubnetIds = [currentSubnet.id];
    } else {
      // Find root subnets that contain the current subnet based on CIDR
      relevantRootSubnetIds = subnets
        .filter(subnet =>
          subnet.category === 'Root' &&
          (subnet.id === currentSubnetId ||
           (currentSubnet && isParentSubnet(subnet.cidr, currentSubnet.cidr)))
        )
        .map(subnet => subnet.id);
    }
  } else {
    // If no subnet is selected, show all root subnets
    relevantRootSubnetIds = subnets
      .filter(subnet => subnet.category === 'Root')
      .map(subnet => subnet.id);
  }

  // Find all subnets that are in the hierarchy tree of relevant root subnets
  const relevantSubnetIds = new Set(relevantRootSubnetIds);

  // Helper function to recursively find all children
  const addAllChildren = (subnetId) => {
    const children = subnetRelationships[subnetId] || [];
    children.forEach(childId => {
      relevantSubnetIds.add(childId);
      addAllChildren(childId);
    });
  };

  // Add all children of relevant root subnets
  relevantRootSubnetIds.forEach(rootId => addAllChildren(rootId));

  // For orphaned subnets, only show the current one if it's orphaned
  const orphanSubnetIds = [];
  if (currentSubnet && !relevantSubnetIds.has(currentSubnetId)) {
    // We don't need to create allSubnetIds since we're not using it
    const childrenSubnetIds = new Set();

    // Collect all children
    Object.values(subnetRelationships).forEach(children => {
      children.forEach(id => childrenSubnetIds.add(id));
    });

    // Check if current subnet is orphaned
    if (!childrenSubnetIds.has(currentSubnetId)) {
      orphanSubnetIds.push(currentSubnetId);
    }
  }

  // Find all orphaned subnets (not just the current one)
  const findAllOrphanedSubnets = () => {
    const allSubnetIds = subnets.map(s => s.id);
    const childrenIds = new Set();

    // Collect all children
    Object.values(subnetRelationships).forEach(children => {
      children.forEach(id => childrenIds.add(id));
    });

    // Return subnets that are not children of any other subnet
    return allSubnetIds.filter(id => !childrenIds.has(id));
  };

  // Get all orphaned subnets that aren't root subnets
  const allOrphanedNonRootIds = findAllOrphanedSubnets().filter(id => {
    const subnet = subnets.find(s => s.id === id);
    return subnet && subnet.category !== 'Root';
  });

  // Add these to our relevant subnets
  allOrphanedNonRootIds.forEach(id => {
    orphanSubnetIds.push(id);
  });

  // Debug information
  console.log('Subnet tree stats:', {
    totalSubnets: subnets.length,
    rootSubnets: subnets.filter(s => s.category === 'Root').length,
    relevantRootSubnets: relevantRootSubnetIds.length,
    orphanedSubnets: orphanSubnetIds.length,
    parentSubnets: Object.keys(subnetRelationships).length,
  });

  return (
    <div className="enhanced-subnet-tree p-2 max-h-96 overflow-y-auto border border-gray-200 rounded-md bg-white">
      {isLoading ? (
        <div className="text-center text-xs text-gray-500 py-2">
          Loading subnet hierarchy...
        </div>
      ) : error ? (
        <div className="text-center text-xs text-red-500 py-2">
          {error}
        </div>
      ) : subnets.length === 0 ? (
        <div className="text-center text-xs text-gray-500 py-2">
          No subnet data available
        </div>
      ) : (
        <>


          <div className="relative">
            {/* Relevant Root subnets */}
            {relevantRootSubnetIds.length > 0 ? (
              <>
                <div className="text-xs font-semibold text-gray-700 pb-1">Root Subnets:</div>
                {relevantRootSubnetIds.map((id, idx) =>
                  renderSubnetNode(
                    id,
                    0,
                    idx === relevantRootSubnetIds.length - 1 && orphanSubnetIds.length === 0
                  )
                )}
              </>
            ) : (
              <div className="text-center text-xs text-gray-500 py-2">
                No root subnets available
              </div>
            )}

            {/* Orphaned subnets (if any) */}
            {orphanSubnetIds.length > 0 && (
              <>
                {relevantRootSubnetIds.length > 0 && <div className="border-t my-2"></div>}
                <div className="text-xs font-semibold text-gray-700 pb-1">Unassigned Subnets:</div>
                {orphanSubnetIds.map((id, idx) =>
                  renderSubnetNode(
                    id,
                    0,
                    idx === orphanSubnetIds.length - 1
                  )
                )}
              </>
            )}

            {/* Show message if no subnets are displayed */}
            {relevantRootSubnetIds.length === 0 && orphanSubnetIds.length === 0 && (
              <div className="text-center text-xs text-gray-500 py-2">
                No subnet hierarchy available
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default EnhancedSubnetTree;