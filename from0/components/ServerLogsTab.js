import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  RefreshCw,
  AlertTriangle,
  Search,
  Database,
  Filter,
  ArrowUp,
  ArrowDown,
  FileText,
  Clock,
  Activity,
  ChevronDown,
  ChevronRight,
  ChevronLeft,
  X,
  Download,
  Server,
  Lock
} from 'lucide-react';
import { API_URL } from '../config';

const LOG_TYPES = {
  ALL: 'all',
  SYSTEM: 'system',
  HARDWARE: 'hardware',
  SECURITY: 'security',
  APPLICATION: 'application'
};

const LOG_LEVELS = {
  ALL: 'all',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
  DEBUG: 'debug'
};

// Memoized helper functions
const getTypeIcon = React.memo(({ type }) => {
  switch (type) {
    case LOG_TYPES.SYSTEM:
      return <Database className="w-4 h-4" />;
    case LOG_TYPES.HARDWARE:
      return <Activity className="w-4 h-4" />;
    case LOG_TYPES.SECURITY:
      return <AlertTriangle className="w-4 h-4" />;
    case LOG_TYPES.APPLICATION:
      return <FileText className="w-4 h-4" />;
    default:
      return <Database className="w-4 h-4" />;
  }
});

const LevelBadge = React.memo(({ level }) => {
  const getLevelColor = (level) => {
    switch (level) {
      case LOG_LEVELS.ERROR:
        return 'bg-red-100 text-red-800';
      case LOG_LEVELS.WARNING:
        return 'bg-yellow-100 text-yellow-800';
      case LOG_LEVELS.INFO:
        return 'bg-blue-100 text-blue-800';
      case LOG_LEVELS.DEBUG:
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getLevelColor(level)}`}>
      {level}
    </span>
  );
});

// LogRow component to optimize rendering
const LogRow = React.memo(({ log, onClick, formatTimestamp }) => {
  return (
    <tr className="hover:bg-gray-50 cursor-pointer" onClick={() => onClick(log)}>
      <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
        <div className="flex items-center">
          <Clock className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400" />
          {formatTimestamp(log.timestamp)}
        </div>
      </td>
      <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
        <div className="flex items-center">
          <span className="flex-shrink-0 mr-1.5 text-gray-500">
            <getTypeIcon type={log.type} />
          </span>
          <span className="capitalize">{log.type}</span>
        </div>
      </td>
      <td className="px-3 py-2 whitespace-nowrap text-sm">
        <LevelBadge level={log.level} />
      </td>
      <td className="px-3 py-2 text-sm text-gray-500 truncate max-w-xs">
        {log.message}
      </td>
    </tr>
  );
});

// PaginationButton component
const PaginationButton = React.memo(({ page, currentPage, onClick, children }) => {
  const isActive = page === currentPage;
  return (
    <button
      onClick={() => onClick(page)}
      className={`relative inline-flex items-center px-4 py-2 border ${
        isActive
          ? 'bg-indigo-50 border-indigo-500 text-indigo-600 z-10'
          : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50'
      } text-sm font-medium`}
    >
      {children || page}
    </button>
  );
});

const ServerLogsTab = ({ server, serverType }) => {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [logsPerPage, setLogsPerPage] = useState(25);

  // Filtering
  const [searchTerm, setSearchTerm] = useState('');
  const [logType, setLogType] = useState(LOG_TYPES.ALL);
  const [logLevel, setLogLevel] = useState(LOG_LEVELS.ALL);
  const [timeRange, setTimeRange] = useState('all_time');
  const [dateFrom, setDateFrom] = useState(null);
  const [dateTo, setDateTo] = useState(null);

  // Sorting
  const [sortField, setSortField] = useState('timestamp');
  const [sortDirection, setSortDirection] = useState('desc'); // desc = newest first

  // Log details
  const [selectedLog, setSelectedLog] = useState(null);
  const [showLogDetails, setShowLogDetails] = useState(false);

  // Custom date filter
  const [showCustomDateFilter, setShowCustomDateFilter] = useState(false);

  // iDRAC state
  const [idracConnected, setIdracConnected] = useState(false);
  const [idracVersion, setIdracVersion] = useState(8);
  const [idracRawOutput, setIdracRawOutput] = useState('');

  // Default credentials - memoized
  const idracCredentials = useMemo(() => ({
    username: 'root',
    password: server?.ipmi_root_pass || ''
  }), [server?.ipmi_root_pass]);

  // Initialize date range based on time selection - optimized with useCallback
  const updateDateRange = useCallback(() => {
    const now = new Date();
    let fromDate = new Date(now);

    switch (timeRange) {
      case 'all_time':
        fromDate = new Date(0); // January 1, 1970
        break;
      case '1h':
        fromDate.setHours(now.getHours() - 1);
        break;
      case '6h':
        fromDate.setHours(now.getHours() - 6);
        break;
      case '24h':
        fromDate.setDate(now.getDate() - 1);
        break;
      case '7d':
        fromDate.setDate(now.getDate() - 7);
        break;
      case '30d':
        fromDate.setDate(now.getDate() - 30);
        break;
      case 'custom':
        // Custom date range is handled separately
        return;
      default:
        fromDate = new Date(0); // Default to all time
    }

    setDateFrom(fromDate);
    setDateTo(now);
  }, [timeRange]);

  useEffect(() => {
    updateDateRange();
  }, [updateDateRange]);

  // Auto-connect on mount
  useEffect(() => {
    if (server?.ipmi) {
      connectToIdrac();
    }
  }, [server?.id, server?.ipmi]);

  // Execute iDRAC command - memoized
  const executeIdracCommand = useCallback(async (command) => {
    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=execute_idrac_command`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token,
          ipmi_address: server?.ipmi,
          ipmi_username: idracCredentials.username,
          ipmi_password: idracCredentials.password,
          idrac_version: idracVersion,
          server_id: server?.id,
          server_type: serverType,
          command
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      return await response.json();
    } catch (err) {
      console.error(`Error executing command '${command}':`, err);
      throw err;
    }
  }, [server?.id, server?.ipmi, idracCredentials, idracVersion, serverType]);

  // Connect to iDRAC - optimized
  const connectToIdrac = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      if (!idracCredentials.username || !idracCredentials.password) {
        throw new Error("iDRAC username and password are not available");
      }

      if (!server?.ipmi) {
        throw new Error("iDRAC address is not configured for this server");
      }

      // First detect iDRAC version
      const token = localStorage.getItem('admin_token');

      const versionResponse = await fetch(`${API_URL}/api_admin_inventory.php?f=detect_idrac_version`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token,
          ipmi_address: server.ipmi,
          ipmi_username: idracCredentials.username,
          ipmi_password: idracCredentials.password
        })
      });

      if (!versionResponse.ok) {
        throw new Error(`HTTP error ${versionResponse.status}`);
      }

      const versionResult = await versionResponse.json();

      if (!versionResult.success) {
        throw new Error(versionResult.error || 'Failed to detect iDRAC version');
      }

      // Set the detected iDRAC version
      const detectedVersion = versionResult.idrac_version || 8;
      setIdracVersion(detectedVersion);

      // Test connection with a simple command
      const testResult = await executeIdracCommand('racadm version');

      if (!testResult.success) {
        throw new Error(testResult.error || 'Failed to connect to iDRAC');
      }

      setIdracConnected(true);

      // Fetch iDRAC logs
      await fetchIdracLogs();

    } catch (err) {
      console.error("Error connecting to iDRAC:", err);
      setError(`Failed to connect to iDRAC: ${err.message}`);
      setIdracConnected(false);
    } finally {
      setLoading(false);
    }
  }, [server, idracCredentials, executeIdracCommand]);

  // Disconnect from iDRAC
  const disconnectFromIdrac = useCallback(() => {
    setIdracConnected(false);
    setLogs([]);
  }, []);

  // Process iDRAC logs - optimized and memoized
  const processIdracLogs = useCallback((rawLogs) => {
    // Use Set to keep track of processed IDs
    const processedIds = new Set();
    const processedLogs = [];

    // Ensure rawLogs is an array
    const logsArray = Array.isArray(rawLogs) ? rawLogs : [rawLogs];

    // If empty array, return empty result
    if (logsArray.length === 0) return [];

    // Join all logs and split by separator, or just use direct lines if no separator
    let logEntries = logsArray
      .join('\n')
      .split('-------------------------------------------------------------------------------')
      .filter(entry => entry.trim() !== '');

    // If no entries found, try splitting by newline
    if (logEntries.length === 0) {
      logEntries = logsArray.filter(line => line.trim() !== '');
    }

    // Prepare regular expressions for better performance
    const recordRegex = /Record:\s*(\d+)/i;
    const dateTimeRegex = /Date\/Time:\s*(.*)/i;
    const sourceRegex = /Source:\s*(.*)/i;
    const severityRegex = /Severity:\s*(.*)/i;
    const descriptionRegex = /Description:\s*(.*)/i;

    logEntries.forEach((entry, index) => {
      try {
        // Parse each log entry
        const lines = entry.trim().split('\n').map(line => line.trim());

        // Extract record number
        const recordMatch = entry.match(recordRegex);
        const id = recordMatch ? recordMatch[1].trim() : (index + 1).toString();

        // Skip if we've already processed this ID
        if (processedIds.has(id)) return;
        processedIds.add(id);

        // Extract date/time
        const dateTimeMatch = entry.match(dateTimeRegex);

        let timestamp = new Date().toISOString();
        if (dateTimeMatch) {
          const dateTimeValue = dateTimeMatch[1].trim();
          // Try parsing different timestamp formats
          const parsedDate = new Date(dateTimeValue);
          if (!isNaN(parsedDate.getTime())) {
            timestamp = parsedDate.toISOString();
          } else {
            // Fallback to original value if parsing fails
            timestamp = dateTimeValue;
          }
        }

        // Extract source
        const sourceMatch = entry.match(sourceRegex);
        const source = sourceMatch ? sourceMatch[1].trim() : 'system';

        // Extract severity
        const severityMatch = entry.match(severityRegex);
        const severity = severityMatch ? severityMatch[1].trim().toLowerCase() : 'ok';

        // Extract description
        const descriptionMatch = entry.match(descriptionRegex);
        const message = descriptionMatch ? descriptionMatch[1].trim() : 'No description';

        // Map severity to log levels
        let level = LOG_LEVELS.INFO;
        if (severity === 'critical') {
          level = LOG_LEVELS.ERROR;
        } else if (severity === 'warning') {
          level = LOG_LEVELS.WARNING;
        } else if (severity === 'ok') {
          level = LOG_LEVELS.INFO;
        }

        // Determine log type - using regex for better performance
        let type = LOG_TYPES.SYSTEM;
        const messageLower = message.toLowerCase();

        if (/power|supply|hardware|fan|temperature/i.test(messageLower)) {
          type = LOG_TYPES.HARDWARE;
        } else if (/security|auth|login|password/i.test(messageLower)) {
          type = LOG_TYPES.SECURITY;
        }

        processedLogs.push({
          id,
          timestamp,
          type,
          level,
          source,
          message,
          details: entry.trim()
        });
      } catch (parseError) {
        console.error('Error parsing log entry:', parseError);
      }
    });

    // Sort logs by record number (newest first)
    return processedLogs.sort((a, b) => {
      const idA = parseInt(a.id, 10) || 0;
      const idB = parseInt(b.id, 10) || 0;
      return idB - idA;
    });
  }, []);

  // Fetch logs from iDRAC
  const fetchIdracLogs = useCallback(async () => {
    if (!server || !server.ipmi || !idracConnected) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Execute the getsel command to get System Event Logs
      const logCommand = idracVersion >= 9 ? 'racadm getsel' : 'racadm getsel';

      const result = await executeIdracCommand(logCommand);

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch iDRAC logs');
      }

      // Store raw output for debugging
      setIdracRawOutput(result.raw_output?.join('\n') || '');

      // Process the logs
      const processedLogs = processIdracLogs(result.raw_output || []);

      setLogs(processedLogs);
      setLastUpdated(new Date());

    } catch (err) {
      console.error("Error fetching iDRAC logs:", err);
      setError(`Failed to fetch iDRAC logs: ${err.message}`);

      // If fetching fails, try using getraclog as a fallback
      try {
        const fallbackResult = await executeIdracCommand('racadm getraclog');
        if (fallbackResult.success) {
          // Process logs from fallback command
          const processedLogs = processIdracLogs(fallbackResult.raw_output || []);
          setLogs(processedLogs);
          setLastUpdated(new Date());
          // Clear the error since we got logs from fallback
          setError(null);
        }
      } catch (fallbackErr) {
        console.error("Fallback log fetch also failed:", fallbackErr);
        // Keep the original error
      }
    } finally {
      setLoading(false);
    }
  }, [server, idracConnected, idracVersion, executeIdracCommand, processIdracLogs]);

  // Effect to fetch logs when connected
  useEffect(() => {
    if (idracConnected) {
      fetchIdracLogs();
    }
  }, [idracConnected, fetchIdracLogs]);

  // Memoized filtered and sorted logs
  const filteredLogs = useMemo(() => {
    if (!logs.length) return [];

    // Apply all filters and sorting in one pass
    let result = logs.filter(log => {
      // Type filter
      if (logType !== LOG_TYPES.ALL && log.type !== logType) {
        return false;
      }

      // Level filter
      if (logLevel !== LOG_LEVELS.ALL && log.level !== logLevel) {
        return false;
      }

      // Date range filtering
      if (dateFrom && dateTo) {
        try {
          const logTime = new Date(log.timestamp);
          if (!isNaN(logTime.getTime())) {
            const fromTime = dateFrom.getTime();
            const toTime = dateTo.getTime();
            if (logTime.getTime() < fromTime || logTime.getTime() > toTime) {
              return false;
            }
          }
        } catch (err) {
          // Keep the log if date parsing fails
        }
      }

      // Search term filter
      if (searchTerm.trim() !== '') {
        const term = searchTerm.toLowerCase();
        return (
          log.message.toLowerCase().includes(term) ||
          log.source.toLowerCase().includes(term) ||
          (log.details && log.details.toLowerCase().includes(term))
        );
      }

      return true;
    });

    // Apply sorting
    result = result.sort((a, b) => {
      let valueA, valueB;

      // Handle different field types
      if (sortField === 'timestamp') {
        valueA = new Date(a[sortField]).getTime();
        valueB = new Date(b[sortField]).getTime();

        // Handle invalid dates
        if (isNaN(valueA)) valueA = 0;
        if (isNaN(valueB)) valueB = 0;
      } else {
        valueA = a[sortField];
        valueB = b[sortField];
      }

      // Handle string comparisons
      if (typeof valueA === 'string') {
        valueA = valueA.toLowerCase();
      }
      if (typeof valueB === 'string') {
        valueB = valueB.toLowerCase();
      }

      // Apply sort direction
      return sortDirection === 'asc'
        ? valueA > valueB ? 1 : -1
        : valueA < valueB ? 1 : -1;
    });

    return result;
  }, [logs, searchTerm, logType, logLevel, dateFrom, dateTo, sortField, sortDirection]);

  // Memoize totalPages calculation
  const totalPages = useMemo(() => {
    return Math.ceil(filteredLogs.length / logsPerPage);
  }, [filteredLogs.length, logsPerPage]);

  // Memoize currentPageLogs
  const currentPageLogs = useMemo(() => {
    const startIndex = (currentPage - 1) * logsPerPage;
    return filteredLogs.slice(startIndex, startIndex + logsPerPage);
  }, [filteredLogs, currentPage, logsPerPage]);

  // Format timestamp for display - memoized
  const formatTimestamp = useCallback((timestamp) => {
    try {
      const date = new Date(timestamp);
      if (isNaN(date.getTime())) {
        return timestamp; // Return original if not valid date
      }
      return date.toLocaleString();
    } catch (e) {
      return timestamp; // Return original on error
    }
  }, []);

  // Handle sort change
  const handleSortChange = useCallback((field) => {
    setSortDirection(prevDirection => {
      if (field === sortField) {
        // If clicking on the same field, toggle direction
        return prevDirection === 'asc' ? 'desc' : 'asc';
      } else {
        // If clicking on a different field, sort by that field in descending order
        setSortField(field);
        return 'desc';
      }
    });
  }, [sortField]);

  // Handle page change
  const handlePageChange = useCallback((page) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
  }, [totalPages]);

  // Handle log click
  const handleLogClick = useCallback((log) => {
    setSelectedLog(log);
    setShowLogDetails(true);
  }, []);

  // Export logs as CSV
  const exportLogs = useCallback(() => {
    // Prepare CSV content
    const headers = ['Timestamp', 'Type', 'Level', 'Source', 'Message'];
    const csvContent = [
      headers.join(','),
      ...filteredLogs.map(log => [
        log.timestamp,
        log.type,
        log.level,
        log.source,
        `"${log.message.replace(/"/g, '""')}"`
      ].join(','))
    ].join('\n');

    // Create download link
    const encodedUri = encodeURI('data:text/csv;charset=utf-8,' + csvContent);
    const link = document.createElement('a');
    link.setAttribute('href', encodedUri);
    link.setAttribute('download', `idrac_logs_${server?.id || 'server'}_${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);

    // Trigger download
    link.click();

    // Clean up
    document.body.removeChild(link);
  }, [filteredLogs, server?.id]);

  // Generate pagination buttons
  const paginationButtons = useMemo(() => {
    if (totalPages <= 1) return null;

    const buttons = [];
    let startPage, endPage;

    if (totalPages <= 5) {
      // Show all pages
      startPage = 1;
      endPage = totalPages;
    } else if (currentPage <= 3) {
      // Near start
      startPage = 1;
      endPage = 5;
    } else if (currentPage >= totalPages - 2) {
      // Near end
      startPage = totalPages - 4;
      endPage = totalPages;
    } else {
      // Middle
      startPage = currentPage - 2;
      endPage = currentPage + 2;
    }

    for (let i = startPage; i <= endPage; i++) {
      buttons.push(
        <PaginationButton
          key={i}
          page={i}
          currentPage={currentPage}
          onClick={handlePageChange}
        />
      );
    }

    return buttons;
  }, [totalPages, currentPage, handlePageChange]);


  // Loading state
  if (loading && logs.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-4 flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="w-12 h-12 animate-spin mx-auto text-indigo-700 mb-3" />
          <p className="text-lg text-gray-600">Connecting to iDRAC and loading logs...</p>
        </div>
      </div>
    );
  }

  // Error state with retry button
  if (error && logs.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-4">
        <div className="text-center max-w-md mx-auto">
          <div className="bg-red-100 p-4 rounded-lg mb-4">
            <AlertTriangle className="w-12 h-12 text-red-600 mx-auto mb-3" />
            <p className="text-lg text-red-700 font-medium">{error}</p>
          </div>
          <p className="text-sm text-gray-600 mb-4">
            Unable to automatically connect to iDRAC. This could be due to incorrect credentials or network issues.
          </p>
          <button
            onClick={connectToIdrac}
            className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // If not connected, show loading indicator
  if (!idracConnected && !error) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-4 flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="w-12 h-12 animate-spin mx-auto text-indigo-700 mb-3" />
          <p className="text-lg text-gray-600">Connecting to iDRAC...</p>
        </div>
      </div>
    );
  }

  // Display logs when connected
  return (
    <div className="space-y-3">
      {/* Header with filters */}
      <div className="bg-white rounded-lg shadow-sm p-3">
        <div className="flex flex-wrap justify-between items-center">
          <h4 className="text-lg font-semibold text-gray-800 mb-2 md:mb-0">
            iDRAC System Logs
            <span className="ml-2 text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded">
              iDRAC Connected
            </span>
          </h4>
          <div className="flex space-x-2">
            <button
              onClick={disconnectFromIdrac}
              className="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md flex items-center text-sm transition-colors"
            >
              <Lock className="w-4 h-4 mr-1.5" />
              Disconnect
            </button>
            <button
              onClick={fetchIdracLogs}
              className="p-2 text-gray-500 hover:text-indigo-700 bg-gray-100 hover:bg-gray-200 rounded-full transition-colors"
              title="Refresh Logs"
              disabled={loading}
            >
              <RefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
            </button>
            <button
              onClick={exportLogs}
              className="p-2 text-gray-500 hover:text-indigo-700 bg-gray-100 hover:bg-gray-200 rounded-full transition-colors"
              title="Export Logs"
            >
              <Download className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Last updated timestamp */}
        {lastUpdated && (
          <div className="text-xs text-gray-500 mt-1">
            Last updated: {lastUpdated.toLocaleString()}
          </div>
        )}

        {/* Filters */}
        <div className="mt-3 grid grid-cols-1 md:grid-cols-3 gap-2">
          {/* Search filter */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search logs..."
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
          </div>

          {/* Type and Level filters */}
          <div className="flex space-x-2">
            <select
              value={logType}
              onChange={(e) => setLogType(e.target.value)}
              className="block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            >
              <option value={LOG_TYPES.ALL}>All Types</option>
              <option value={LOG_TYPES.SYSTEM}>System</option>
              <option value={LOG_TYPES.HARDWARE}>Hardware</option>
              <option value={LOG_TYPES.SECURITY}>Security</option>
              <option value={LOG_TYPES.APPLICATION}>Application</option>
            </select>

            <select
              value={logLevel}
              onChange={(e) => setLogLevel(e.target.value)}
              className="block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            >
              <option value={LOG_LEVELS.ALL}>All Levels</option>
              <option value={LOG_LEVELS.ERROR}>Error</option>
              <option value={LOG_LEVELS.WARNING}>Warning</option>
              <option value={LOG_LEVELS.INFO}>Info</option>
              <option value={LOG_LEVELS.DEBUG}>Debug</option>
            </select>
          </div>

          {/* Time range filter */}
          <div className="flex space-x-2">
            <select
              value={timeRange}
              onChange={(e) => {
                setTimeRange(e.target.value);
                setShowCustomDateFilter(e.target.value === 'custom');
              }}
              className="block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            >
              <option value="all_time">All Time</option>
              <option value="1h">Last Hour</option>
              <option value="6h">Last 6 Hours</option>
              <option value="24h">Last 24 Hours</option>
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
              <option value="custom">Custom Range</option>
            </select>
          </div>
        </div>

        {/* Custom date range filter */}
        {showCustomDateFilter && (
          <div className="mt-2 grid grid-cols-1 md:grid-cols-2 gap-2">
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">From Date</label>
              <input
                type="datetime-local"
                value={dateFrom ? dateFrom.toISOString().slice(0, 16) : ''}
                onChange={(e) => setDateFrom(new Date(e.target.value))}
                className="block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">To Date</label>
              <input
                type="datetime-local"
                value={dateTo ? dateTo.toISOString().slice(0, 16) : ''}
                onChange={(e) => setDateTo(new Date(e.target.value))}
                className="block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            </div>
          </div>
        )}
      </div>



      {/* Logs table */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th
                  scope="col"
                  className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSortChange('timestamp')}
                >
                  <div className="flex items-center">
                    <span>Timestamp</span>
                    {sortField === 'timestamp' && (
                      sortDirection === 'asc' ?
                        <ArrowUp className="ml-1 h-4 w-4" /> :
                        <ArrowDown className="ml-1 h-4 w-4" />
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSortChange('type')}
                >
                  <div className="flex items-center">
                    <span>Type</span>
                    {sortField === 'type' && (
                      sortDirection === 'asc' ?
                        <ArrowUp className="ml-1 h-4 w-4" /> :
                        <ArrowDown className="ml-1 h-4 w-4" />
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSortChange('level')}
                >
                  <div className="flex items-center">
                    <span>Level</span>
                    {sortField === 'level' && (
                      sortDirection === 'asc' ?
                        <ArrowUp className="ml-1 h-4 w-4" /> :
                        <ArrowDown className="ml-1 h-4 w-4" />
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Message
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentPageLogs.length > 0 ? (
                // Display paginated logs
                currentPageLogs.map((log) => (
                  <LogRow
                    key={log.id}
                    log={log}
                    onClick={handleLogClick}
                    formatTimestamp={formatTimestamp}
                  />
                ))
              ) : (
                <tr>
                  <td colSpan="4" className="px-6 py-10 text-center text-sm text-gray-500">
                    {logs.length > 0 ?
                      'No logs match your filters' :
                      'No logs available. Try refreshing or adjusting filters.'}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing <span className="font-medium">{filteredLogs.length > 0 ? ((currentPage - 1) * logsPerPage) + 1 : 0}</span> to{' '}
                  <span className="font-medium">
                    {Math.min(currentPage * logsPerPage, filteredLogs.length)}
                  </span>{' '}
                  of <span className="font-medium">{filteredLogs.length}</span> results
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">Previous</span>
                    <ChevronLeft className="h-5 w-5" aria-hidden="true" />
                  </button>

                  {paginationButtons}

                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">Next</span>
                    <ChevronRight className="h-5 w-5" aria-hidden="true" />
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Log details modal */}
      {showLogDetails && selectedLog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-3xl max-h-[80vh] overflow-hidden">
            <div className="flex justify-between items-center px-4 py-3 border-b">
              <h3 className="text-lg font-semibold text-gray-800">Log Details</h3>
              <button
                onClick={() => setShowLogDetails(false)}
                className="p-1 rounded-full hover:bg-gray-200"
              >
                <X className="w-5 h-5 text-gray-500" />
              </button>
            </div>
            <div className="p-4 overflow-y-auto max-h-[calc(80vh-7rem)]">
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Timestamp</h4>
                    <p className="mt-1 text-sm text-gray-900">{formatTimestamp(selectedLog.timestamp)}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Source</h4>
                    <p className="mt-1 text-sm text-gray-900">{selectedLog.source}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Type</h4>
                    <p className="mt-1 text-sm text-gray-900 capitalize">{selectedLog.type}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Level</h4>
                    <p className="mt-1">
                      <LevelBadge level={selectedLog.level} />
                    </p>
                  </div>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Message</h4>
                  <p className="mt-1 text-sm text-gray-900">{selectedLog.message}</p>
                </div>
                {selectedLog.details && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Details</h4>
                    <pre className="mt-1 p-3 bg-gray-50 rounded text-sm text-gray-900 whitespace-pre-wrap">
                      {selectedLog.details}
                    </pre>
                  </div>
                )}
              </div>
            </div>
            <div className="px-4 py-3 bg-gray-50 flex justify-end">
              <button
                onClick={() => setShowLogDetails(false)}
                className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ServerLogsTab;