import React, { useState, useEffect } from 'react';
import {
  CreditCard,
  Clock,
  MessageSquare,
  Ban
} from 'lucide-react';

const MetricCards = ({ taskStats = {} }) => {
  const [metrics, setMetrics] = useState([
    {
      label: 'Earnings Today',
      value: '€0',
      iconComponent: <CreditCard
        className="text-success"
        size={40}
        strokeWidth={2}
      />,
      shadowClass: 'icon-dropshadow-success',
    },
    {
      label: 'Pending Tasks',
      value: '0',
      iconComponent: <Clock
        className="text-warning"
        size={40}
        strokeWidth={2}
      />,
      shadowClass: 'icon-dropshadow-warning',
    },
    {
      label: 'Active Tasks',
      value: '0',
      iconComponent: <MessageSquare
        className="text-info"
        size={40}
        strokeWidth={2}
      />,
      shadowClass: 'icon-dropshadow-info',
    },
    {
      label: 'Completed Tasks',
      value: '0',
      iconComponent: <Ban
        className="text-success"
        size={40}
        strokeWidth={2}
      />,
      shadowClass: 'icon-dropshadow-success',
    }
  ]);

  // Update metrics when taskStats change
  useEffect(() => {
    setMetrics([
      {
        label: 'Total Tasks',
        value: taskStats.total || '0',
        iconComponent: <CreditCard
          className="text-success"
          size={40}
          strokeWidth={2}
        />,
        shadowClass: 'icon-dropshadow-success',
      },
      {
        label: 'Pending Tasks',
        value: taskStats.pending || '0',
        iconComponent: <Clock
          className="text-warning"
          size={40}
          strokeWidth={2}
        />,
        shadowClass: 'icon-dropshadow-warning',
      },
      {
        label: 'In Progress',
        value: taskStats.inProgress || '0',
        iconComponent: <MessageSquare
          className="text-info"
          size={40}
          strokeWidth={2}
        />,
        shadowClass: 'icon-dropshadow-info',
      },
      {
        label: 'Completed Tasks',
        value: taskStats.completed || '0',
        iconComponent: <Ban
          className="text-success"
          size={40}
          strokeWidth={2}
        />,
        shadowClass: 'icon-dropshadow-success',
      }
    ]);
  }, [taskStats]);

  return (
    <>
      {metrics.map((metric) => (
        <div
          key={metric.label}
          className="bg-white p-6 shadow-sm flex items-center justify-between border rounded metric-card"
          style={{
            transition: 'transform 0.2s, box-shadow 0.2s',
          }}
        >
          <div>
            <div className="text-sm text-gray-700">{metric.label}</div>
            <div className="text-2xl font-bold mt-1">{metric.value}</div>
          </div>
          <div className={`card-custom-icon ${metric.shadowClass}`}>
            {metric.iconComponent}
          </div>
        </div>
      ))}
    </>
  );
};

export default MetricCards;