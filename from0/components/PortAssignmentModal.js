import React, { useState, useEffect } from 'react';
import { 
  <PERSON>C<PERSON><PERSON>, 
  RefreshCw, 
  Search, 
  Link, 
  AlertTriangle,
  CheckCircle,
  Network,
  Server,
  Layers,
  Filter
} from 'lucide-react';
import { API_URL } from '../config';
// Port Assignment Modal Component
const PortAssignmentModal = ({ 
  isOpen, 
  onClose, 
  device = null, // The device to assign ports to (if starting from device)
  deviceType = 'dedicated', // 'dedicated' or 'blade'
  switchId = null, // The switch ID to assign ports from (if starting from switch)
  onAssignmentComplete = () => {} // Callback when assignment is completed
}) => {
  // State variables
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [switches, setSwitches] = useState([]);
  const [selectedSwitch, setSelectedSwitch] = useState(switchId);
  const [switchPorts, setSwitchPorts] = useState([]);
  const [selectedPort, setSelectedPort] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('Available');
  const [servers, setServers] = useState([]);
  const [selectedServer, setSelectedServer] = useState(device ? device.id : null);
  const [fetchingPorts, setFetchingPorts] = useState(false);
  
  // Reset status filter when modal opens
  useEffect(() => {
    // Make sure the status filter is set to "Available" by default
    setStatusFilter("Available");
    console.log("Status filter set to Available");
    
    // Reset the selectedPort when the modal opens
    setSelectedPort(null);
  }, [isOpen]); // Reset when the modal opens
  
  // Fetch switches on component mount
  useEffect(() => {
    if (isOpen) {
      fetchSwitches();
      if (device) {
        setSelectedServer(device.id);
      } else {
        fetchServers();
      }
      
      if (selectedSwitch || switchId) {
        fetchSwitchPorts(selectedSwitch || switchId);
      }
    }
  }, [isOpen, switchId, device]);
  
  // Debug port statuses
  useEffect(() => {
    if (switchPorts.length > 0) {
      // Log all unique statuses to help diagnose issues
      const uniqueStatuses = [...new Set(switchPorts.map(port => port.status))];
      console.log("Available port statuses:", uniqueStatuses);
      
      // Count ports by status
      const statusCounts = {};
      switchPorts.forEach(port => {
        if (!statusCounts[port.status]) {
          statusCounts[port.status] = 0;
        }
        statusCounts[port.status]++;
      });
      console.log("Ports by status:", statusCounts);
      
      // Log ports with unusual status values
      const unusualPorts = switchPorts.filter(
        port => !['Available', 'Used', 'Disabled'].includes(port.status)
      );
      if (unusualPorts.length > 0) {
        console.log("Ports with unusual status values:", unusualPorts);
      }
    }
  }, [switchPorts]);
  
  // Function to fetch switches
  const fetchSwitches = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin_token');
      
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_inventory_switches`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      
      const data = await response.json();
      setSwitches(data);
      
      // If no switch is selected yet and we have switches, select the first one
      if (!selectedSwitch && data.length > 0 && !switchId) {
        setSelectedSwitch(data[0].id);
        fetchSwitchPorts(data[0].id);
      }
      
      setLoading(false);
    } catch (err) {
      console.error("Error fetching switches:", err);
      setError("Failed to load switches. Please try again.");
      setLoading(false);
    }
  };
  
  // Function to fetch servers (either dedicated or blade)
  const fetchServers = async () => {
    try {
      const token = localStorage.getItem('admin_token');
      const endpoint = deviceType === 'blade' 
        ? 'get_blade_server_inventory' 
        : 'get_inventory_dedicated';
      
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      
      const data = await response.json();
      setServers(data);
      
    } catch (err) {
      console.error(`Error fetching ${deviceType} servers:`, err);
      setError(`Failed to load ${deviceType} servers. Please try again.`);
    }
  };
  
  // Function to fetch switch ports
  const fetchSwitchPorts = async (switchId) => {
    if (!switchId) return;
    
    try {
      setFetchingPorts(true);
      setError(null);
      const token = localStorage.getItem('admin_token');
      
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_switch_ports`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          token,
          switch_id: switchId
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      
      const data = await response.json();
      setSwitchPorts(data);
      setFetchingPorts(false);
    } catch (err) {
      console.error("Error fetching switch ports:", err);
      setError("Failed to load switch ports. Please try again.");
      setFetchingPorts(false);
    }
  };
  
  // Function to discover ports via SNMP
// Function to discover ports via SNMP
const discoverPorts = async () => {
  if (!selectedSwitch) {
    setError("Please select a switch first");
    return;
  }
  
  // Get the switch details
  const switchDetails = switches.find(s => s.id.toString() === selectedSwitch.toString());
  
  if (!switchDetails) {
    setError("Switch not found");
    return;
  }
  
  // Check if any ports are already assigned to servers
  try {
    // Check if any ports for this switch are assigned to servers
    const token = localStorage.getItem('admin_token');
    
    const portsResponse = await fetch(`${API_URL}/api_admin_inventory.php?f=get_switch_ports`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        token,
        switch_id: selectedSwitch
      })
    });
    
    if (!portsResponse.ok) {
      throw new Error(`HTTP error ${portsResponse.status}`);
    }
    
    const ports = await portsResponse.json();
    
    // Check if any ports have a connected_device_id
    const portsAssigned = ports.some(port => port.connected_device_id);
    
    if (portsAssigned) {
      setError(
        "Some ports on this switch are assigned to servers. " +
        "Rediscovery is not allowed to prevent data loss. " +
        "Please unassign all ports from servers before rediscovering."
      );
      return; // Stop here
    }
  } catch (checkError) {
    console.error("Error checking port assignments:", checkError);
    // Continue with discovery, but log the error
  }
  
  // Ask for SNMP community string
  const snmpCommunity = prompt("Enter SNMP community string:", "private");
  
  if (!snmpCommunity) {
    return; // User cancelled
  }
  
  try {
    setLoading(true);
    setError(null);
    
    const token = localStorage.getItem('admin_token');
    
    const response = await fetch(`${API_URL}/api_admin_inventory.php?f=discover_switch_ports`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token,
        switch_id: selectedSwitch,
        snmp_community: snmpCommunity,
        snmp_version: 2 // Default to SNMP v2c
      })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }
    
    const data = await response.json();
    
    if (data.success) {
      setSuccess(`Ports successfully discovered. Found ${data.ports_detected || 'multiple'} ports.`);
      // Refresh ports list
      fetchSwitchPorts(selectedSwitch);
    } else {
      throw new Error(data.error || "Failed to discover ports");
    }
    
    setLoading(false);
  } catch (err) {
    console.error("Error discovering ports:", err);
    setError(`Failed to discover ports: ${err.message}`);
    setLoading(false);
  }
};
  
  // Filter ports based on search and status filter
  const filteredPorts = switchPorts.filter(port => {
    // More robust search matching
    const matchesSearch = searchQuery === '' || 
      (port.port_name && port.port_name.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (port.port_number && port.port_number.toString().toLowerCase().includes(searchQuery.toLowerCase()));
    
    // More robust status filtering with case-insensitive comparison
    const matchesStatus = statusFilter === 'All' || 
      (port.status && port.status.toLowerCase() === statusFilter.toLowerCase());
    
    return matchesSearch && matchesStatus;
  });
  
  // Function to assign port to server
  const assignPortToServer = async () => {
    console.log("Starting port assignment with:", {
      port: selectedPort, 
      server: selectedServer,
      switch: selectedSwitch
    });
    
    // Basic validation
    if (!selectedPort) {
      alert("Please select a port first.");
      setError("Please select a port first.");
      return;
    }
    
    if (!selectedServer) {
      alert("Please select a server first.");
      setError("Please select a server first.");
      return;
    }
    
    if (!selectedSwitch) {
      alert("Please select a switch first.");
      setError("Please select a switch first.");
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      // Get the selected port details
      const port = filteredPorts.find(p => p.id === selectedPort);
      console.log("Selected port details:", port);
      
      const token = localStorage.getItem('admin_token');
      
      // Create request payload
      const payload = {
        token,
        port_id: selectedPort,
        device_id: selectedServer,
        device_type: deviceType,
        // Add extra details that might be needed
        switch_id: selectedSwitch,
        port_number: port?.port_number
      };
      
      console.log("Sending payload:", payload);
      
      // Make the API request
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=assign_port_to_server`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });
      
      // Handle potential HTTP errors
      if (!response.ok) {
        const errorText = await response.text();
        console.error("API error:", errorText);
        throw new Error(`HTTP error ${response.status}: ${errorText}`);
      }
      
      // Parse the response
      const result = await response.json();
      console.log("API response:", result);
      
      if (result.success) {
        setSuccess(`Port successfully assigned! ${result.port_field || 'Connection'} has been updated.`);
        
        // Refresh the ports list
        fetchSwitchPorts(selectedSwitch);
        
        // Trigger the data change event
        const event = new CustomEvent('inventory-data-change', {
          detail: {
            deviceType,
            deviceId: selectedServer,
            switchId: selectedSwitch,
            portId: selectedPort,
            timestamp: Date.now(),
            action: 'port-assigned'
          }
        });
        window.dispatchEvent(event);
        
        // Call the completion callback
        onAssignmentComplete();
      } else {
        throw new Error(result.error || "Unknown error occurred");
      }
    } catch (error) {
      console.error("Port assignment error:", error);
      setError(`Failed to assign port: ${error.message}`);
      alert(`Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };
  
  // Helper function to identify and log port selection issues
  const debugPortSelection = () => {
    console.group("🔍 Port Selection Debug Info");
    console.log("Selected Port ID:", selectedPort);
    console.log("Status Filter:", statusFilter);
    console.log("Total Ports:", switchPorts.length);
    console.log("Filtered Ports:", filteredPorts.length);
    
    // Count ports by status
    const statusCounts = {};
    switchPorts.forEach(port => {
      if (!statusCounts[port.status]) {
        statusCounts[port.status] = 0;
      }
      statusCounts[port.status]++;
    });
    console.log("Ports by Status:", statusCounts);
    
    // Check for any unusual status values
    const unusualStatuses = Object.keys(statusCounts).filter(
      status => !['Available', 'Used', 'Disabled'].includes(status)
    );
    
    if (unusualStatuses.length > 0) {
      console.warn("⚠️ Unusual port statuses detected:", unusualStatuses);
      console.log("Ports with unusual status:", switchPorts.filter(
        port => !['Available', 'Used', 'Disabled'].includes(port.status)
      ));
    }
    
    // Log all available ports for verification
    const availablePorts = filteredPorts.filter(p => p.status === 'Available');
    console.log("Available Ports:", availablePorts.length);
    if (availablePorts.length > 0) {
      console.log("First 5 available ports:", availablePorts.slice(0, 5));
    } else {
      console.warn("❌ No available ports found with current filter!");
    }
    
    console.groupEnd();
  };

  // Call debug function when the modal opens and when the filter changes
  useEffect(() => {
    if (isOpen && switchPorts.length > 0) {
      debugPortSelection();
    }
  }, [isOpen, switchPorts, statusFilter]);
  
  // If modal is not open, don't render anything
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-4xl mx-4 flex flex-col max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="p-4 border-b flex justify-between items-center bg-gray-50 rounded-t-lg">
          <h2 className="text-lg font-bold text-gray-800">
            {device ? `Assign Switch Port to ${device.label}` : 'Assign Device to Switch Port'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <XCircle className="w-5 h-5" />
          </button>
        </div>
        
        {/* Messages */}
        {error && (
          <div className="m-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-md flex items-center">
            <AlertTriangle className="w-5 h-5 mr-2 flex-shrink-0" />
            <p>{error}</p>
          </div>
        )}
        
        {success && (
          <div className="m-4 p-3 bg-green-50 border border-green-200 text-green-700 rounded-md flex items-center">
            <CheckCircle className="w-5 h-5 mr-2 flex-shrink-0" />
            <p>{success}</p>
          </div>
        )}
        
        {/* Main content */}
        <div className="p-4 overflow-y-auto flex-grow">
          <div className="space-y-4 mb-4">
            {/* Switch selection - simplified and more direct */}
            <div className="border p-4 rounded-md bg-gray-50">
              <label className="block text-sm font-medium text-gray-700 mb-2">1. Select Switch</label>
              <div className="flex">
                <select
                  value={selectedSwitch || ''}
                  onChange={(e) => {
                    const newSwitchId = e.target.value;
                    console.log("Switch selected:", newSwitchId);
                    setSelectedSwitch(newSwitchId);
                    // Clear port selection when switch changes
                    setSelectedPort(null);
                    if (newSwitchId) {
                      fetchSwitchPorts(newSwitchId);
                    }
                  }}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                  disabled={loading}
                >
                  <option value="">Select a Switch</option>
                  {switches.map((switchItem) => (
                    <option key={switchItem.id} value={switchItem.id}>
                      {switchItem.label} {switchItem.switch_ip ? `(${switchItem.switch_ip})` : ''}
                    </option>
                  ))}
                </select>
                <button
                  onClick={() => {
                    fetchSwitches();
                    setSelectedPort(null);
                  }}
                  className="ml-2 p-2 text-gray-500 hover:text-indigo-700 border border-gray-300 rounded-md hover:bg-gray-50"
                  disabled={loading}
                  title="Refresh Switches"
                >
                  <RefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
                </button>
              </div>
            </div>
            
            {/* Server selection - only show if not starting from a device */}
            {!device && (
              <div className="border p-4 rounded-md bg-gray-50">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  2. Select {deviceType === 'blade' ? 'Blade Server' : 'Dedicated Server'}
                </label>
                <div className="flex">
                  <select
                    value={selectedServer || ''}
                    onChange={(e) => {
                      const newServerId = e.target.value;
                      console.log("Server selected:", newServerId);
                      setSelectedServer(newServerId);
                    }}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                    disabled={loading}
                  >
                    <option value="">Select a Server</option>
                    {servers.map((server) => (
                      <option key={server.id} value={server.id}>
                        {server.label}
                      </option>
                    ))}
                  </select>
                  <button
                    onClick={fetchServers}
                    className="ml-2 p-2 text-gray-500 hover:text-indigo-700 border border-gray-300 rounded-md hover:bg-gray-50"
                    disabled={loading}
                    title="Refresh Servers"
                  >
                    <RefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
                  </button>
                </div>
              </div>
            )}

            {/* Selected port visual confirmation */}
            {selectedPort && (
              <div className="border p-4 rounded-md bg-green-50 text-green-700">
                <div className="flex items-start">
                  <CheckCircle className="w-5 h-5 mr-2 flex-shrink-0" />
                  <div>
                    <h3 className="font-medium">Port Selected</h3>
                    <p className="text-sm">
                      {filteredPorts.find(p => p.id === selectedPort)?.port_name || 
                       `Port ${filteredPorts.find(p => p.id === selectedPort)?.port_number || selectedPort}`}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
          
          {/* Ports section */}
          {selectedSwitch && (
            <div className="border rounded-md overflow-hidden mt-4">
              <div className="p-3 bg-gray-50 border-b flex justify-between items-center">
                <h3 className="font-medium">3. Select a Port</h3>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => fetchSwitchPorts(selectedSwitch)}
                    className="p-2 text-gray-500 hover:text-indigo-700 border border-gray-300 rounded-md hover:bg-gray-50"
                    disabled={loading || fetchingPorts}
                    title="Refresh Ports"
                  >
                    <RefreshCw className={`w-5 h-5 ${fetchingPorts ? 'animate-spin' : ''}`} />
                  </button>
                </div>
              </div>
              
              {/* Ports filter */}
              <div className="p-3 border-b bg-gray-50 flex flex-wrap gap-2">
                <div className="relative flex-grow max-w-xs">
                  <input
                    type="text"
                    placeholder="Search ports..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-9 pr-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                  />
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                </div>
                
                {/* Status filter with debugging info */}
                <div className="relative">
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="pl-3 pr-8 py-2 border border-gray-300 rounded-md text-sm focus:ring-indigo-500 focus:border-indigo-500 appearance-none"
                  >
                    <option value="All">All Status</option>
                    <option value="Available">Available</option>
                    <option value="Used">Used</option>
                    <option value="Disabled">Disabled</option>
                    {/* Add any unusual statuses found in the data */}
                    {switchPorts
                      .map(port => port.status)
                      .filter((status, index, self) => 
                        status && !['Available', 'Used', 'Disabled'].includes(status) && self.indexOf(status) === index
                      )
                      .map(status => (
                        <option key={status} value={status}>{status}</option>
                      ))
                    }
                  </select>
                  <Filter className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 pointer-events-none" />
                </div>

                {/* Display debugging information for admins */}
                <div className="text-xs text-gray-500 ml-2">
                  Found: {filteredPorts.length} ports
                  {filteredPorts.length > 0 && (
                    <span> - Available: {filteredPorts.filter(p => p.status === 'Available').length}</span>
                  )}
                </div>
              </div>
              
              {/* Ports list with direct selection */}
              <div className="max-h-96 overflow-y-auto p-2">
                {fetchingPorts ? (
                  <div className="flex justify-center items-center p-6">
                    <RefreshCw className="w-6 h-6 animate-spin text-indigo-600 mr-3" />
                    <p className="text-gray-600">Loading ports...</p>
                  </div>
                ) : filteredPorts.length === 0 ? (
                  <div className="text-center p-6">
                    <p className="text-gray-500">No ports found. Try another filter or discover ports via SNMP.</p>
                  </div>
                ) : (
                  <div>
                    <table className="w-full border-collapse text-sm">
                      <thead>
                        <tr className="bg-gray-100">
                          <th className="p-2 text-left">Select</th>
                          <th className="p-2 text-left">Port</th>
                          <th className="p-2 text-left">Status</th>
                          <th className="p-2 text-left">Description</th>
                        </tr>
                      </thead>
                      <tbody>
                        {filteredPorts.map((port) => {
                          const isAvailable = port.status === 'Available' || port.status === '0';
                          const isSelected = selectedPort === port.id;
                          
                          return (
                            <tr 
                              key={port.id}
                              className={`border-b hover:bg-gray-50 ${!isAvailable ? 'opacity-60' : ''} ${isSelected ? 'bg-indigo-50' : ''}`}
                            >
                              <td className="p-2">
                                <input
                                  type="radio"
                                  name="selectedPort"
                                  checked={isSelected}
                                  onChange={(e) => {
                                    // Prevent event propagation to stop the row click from firing
                                    e.stopPropagation();
                                    if (isAvailable) {
                                      console.log("Directly setting selectedPort to:", port.id);
                                      setSelectedPort(port.id);
                                    }
                                  }}
                                  disabled={!isAvailable}
                                  className="h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500"
                                  // Add extra styles to make the clickable area larger
                                  style={{ cursor: 'pointer', padding: '8px' }}
                                />
                              </td>
                              <td className="p-2" onClick={(e) => {
                                e.stopPropagation();
                                if (isAvailable) {
                                  setSelectedPort(port.id);
                                }
                              }}>
                                <div className="font-medium">{port.port_number}</div>
                                {port.max_speed && (
                                  <div className="text-xs text-gray-500">{port.max_speed} Mbps</div>
                                )}
                              </td>
                              <td className="p-2">
                                <div className={`
                                  px-2 py-1 rounded-full text-xs font-medium inline-flex items-center
                                  ${port.status === 'Available' 
                                    ? 'bg-green-100 text-green-800' 
                                    : port.status === 'Used' 
                                      ? 'bg-blue-100 text-blue-800' 
                                      : 'bg-red-100 text-red-800'
                                  }
                                `}>
                                  <span className={`
                                    w-2 h-2 rounded-full mr-1
                                    ${port.status === 'Available' ? 'bg-green-500' : 
                                      port.status === 'Used' ? 'bg-blue-500' : 'bg-red-500'}
                                  `}></span>
                                  {port.status}
                                </div>
                              </td>
                              <td className="p-2">
                                {port.port_name || '-'}
                                {port.connected_device_id && (
                                  <div className="text-xs text-indigo-600 mt-1">
                                    Connected to device #{port.connected_device_id}
                                  </div>
                                )}
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
        
        {/* Footer */}
        <div className="p-4 border-t bg-gray-50 flex justify-end items-center gap-2">
          {/* Debug Button - only visible in development */}
          {process.env.NODE_ENV !== 'production' && (
            <button
              type="button"
              onClick={() => {
                debugPortSelection();
                alert(`Debug info logged to console.\nFiltered ports: ${filteredPorts.length}\nAvailable ports: ${filteredPorts.filter(p => p.status === 'Available').length}`);
              }}
              className="px-3 py-1.5 border border-amber-300 bg-amber-50 rounded-md text-amber-700 hover:bg-amber-100 mr-2"
            >
              Debug Ports
            </button>
          )}
          
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 bg-white rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
            disabled={loading}
          >
            Cancel
          </button>
          <button
            onClick={assignPortToServer}
            className={`
              px-4 py-2 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500
              ${loading || !selectedPort || !selectedServer
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-indigo-600 hover:bg-indigo-700'
              }
            `}
            disabled={loading || !selectedPort || !selectedServer}
          >
            {loading ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 inline animate-spin" />
                Assigning...
              </>
            ) : (
              'Assign Port'
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default PortAssignmentModal;