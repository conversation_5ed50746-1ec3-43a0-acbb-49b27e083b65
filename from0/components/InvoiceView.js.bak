import React, { useState, useEffect } from 'react';
import {
  Send,
  Printer,
  Download,
  Edit,
  CheckCircle,
  Clock,
  AlertCircle,
  File,
  Copy,
  ExternalLink,
  Mail,
  Activity,
  Calendar,
  FileText,
  DollarSign,
  RefreshCw,
  User,
  Settings
} from 'lucide-react';
import { API_URL } from '../config';
/**
 * Enhanced InvoiceView component with activity tracking and sticky header/footer
 *
 * @param {Object} props Component props
 * @param {Object} props.invoice The invoice data to display
 * @param {Function} props.onEdit Function to call for editing (optional)
 * @param {Function} props.onUpdateStatus Function to call when updating invoice status (optional)
 * @param {Function} props.onSend Function to call for sending the invoice (optional)
 * @param {Function} props.onPrint Function to call for printing the invoice (optional)
 * @param {Function} props.onDownload Function to call for downloading the invoice (optional)
 * @param {string} props.className Additional CSS classes (optional)
 * @param {boolean} props.hideActions Whether to hide action buttons (optional)
 * @param {boolean} props.hideHeader Whether to hide the header section (optional)
 * @param {string} props.defaultActiveTab Default active tab ('details' or 'activity') (optional)
 */
const InvoiceView = ({
  invoice,
  onEdit,
  onUpdateStatus,
  onSend,
  onPrint,
  onDownload,
  className = "",
  hideActions = false,
  hideHeader = false,
  defaultActiveTab = 'details'
}) => {
  // State for active tab in the invoice view
  const [activeTab, setActiveTab] = useState(defaultActiveTab);

  // State for activity history
  const [activityHistory, setActivityHistory] = useState([]);
  const [loadingActivity, setLoadingActivity] = useState(false);

  // Log invoice updates for debugging
  useEffect(() => {
    console.log("InvoiceView received invoice update:", {
      id: invoice?.id,
      isProforma: invoice?.isProforma,
      status: invoice?.status,
      paymentDate: invoice?.paymentDate
    });
  }, [invoice?.id, invoice?.isProforma, invoice?.status, invoice?.paymentDate]);

  // Fetch activity history when invoice changes or tab is switched to activity
  useEffect(() => {
    if (invoice && activeTab === 'activity') {
      fetchActivityHistory();
    }
  }, [invoice?.id, activeTab]);

  if (!invoice) return null;

  // Render activity type badge with enhanced styling and cleaner text display
  const renderActivityBadge = (type, description = "") => {
    // Normalize type string - convert to lowercase for consistent matching
    const normalizedType = typeof type === 'string' ? type.toLowerCase() : 'unknown';

    // Check if this is a status update to "Paid" based on description
    const isPaidStatusUpdate =
      (normalizedType === 'status' || normalizedType === 'status_update' || normalizedType === 'status_change') &&
      description &&
      description.toLowerCase().includes('paid');

    // If it's a status update to paid, override the type
    const effectiveType = isPaidStatusUpdate ? 'status_paid' : normalizedType;

    // Define badge colors for different activity types
    const badgeClasses = {
      // System actions
      'system': 'bg-blue-100 text-blue-800',

      // Status changes
      'status': 'bg-indigo-100 text-indigo-800',
      'status_update': 'bg-indigo-100 text-indigo-800',
      'status_change': 'bg-indigo-100 text-indigo-800',
      'status_paid': 'bg-green-100 text-green-800', // Special style for paid status

      // Payment related
      'payment': 'bg-green-100 text-green-800',
      'payment_received': 'bg-green-100 text-green-800',

      // Notifications
      'notification': 'bg-yellow-100 text-yellow-800',
      'send': 'bg-yellow-100 text-yellow-800',

      // Edits
      'edit': 'bg-gray-100 text-gray-800',
      'items_updated': 'bg-gray-100 text-gray-800',
      'invoice_updated': 'bg-gray-100 text-gray-800',

      // Creation
      'creation': 'bg-purple-100 text-purple-800',
      'proforma_created': 'bg-purple-100 text-purple-800',
      'invoice_created': 'bg-purple-100 text-purple-800',

      // Conversion
      'conversion': 'bg-orange-100 text-orange-800',
      'proforma_converted': 'bg-orange-100 text-orange-800'
    };

    // Define icons for each type
    const icons = {
      // System actions
      'system': <Settings className="w-4 h-4 mr-1" />,

      // Status changes
      'status': <RefreshCw className="w-4 h-4 mr-1" />,
      'status_update': <RefreshCw className="w-4 h-4 mr-1" />,
      'status_change': <RefreshCw className="w-4 h-4 mr-1" />,
      'status_paid': <CheckCircle className="w-4 h-4 mr-1" />, // Check icon for paid status

      // Payment related
      'payment': <DollarSign className="w-4 h-4 mr-1" />,
      'payment_received': <DollarSign className="w-4 h-4 mr-1" />,

      // Notifications
      'notification': <Mail className="w-4 h-4 mr-1" />,
      'send': <Mail className="w-4 h-4 mr-1" />,

      // Edits
      'edit': <Edit className="w-4 h-4 mr-1" />,
      'items_updated': <Edit className="w-4 h-4 mr-1" />,
      'invoice_updated': <Edit className="w-4 h-4 mr-1" />,

      // Creation
      'creation': <FileText className="w-4 h-4 mr-1" />,
      'proforma_created': <FileText className="w-4 h-4 mr-1" />,
      'invoice_created': <FileText className="w-4 h-4 mr-1" />,

      // Conversion
      'conversion': <RefreshCw className="w-4 h-4 mr-1" />,
      'proforma_converted': <RefreshCw className="w-4 h-4 mr-1" />
    };

    // Friendly display names for EVERY activity type (with clean text, no underscores)
    const displayNames = {
      // System actions
      'system': 'System',

      // Status changes - all have clean names without underscores
      'status': 'Status Change',
      'status_update': 'Status Update',
      'status_change': 'Status Change',
      'status_paid': 'Status Paid',

      // Payment related
      'payment': 'Payment',
      'payment_received': 'Payment Received',

      // Notifications
      'notification': 'Notification',
      'send': 'Email Sent',

      // Edits - all have clean names without underscores
      'edit': 'Edit',
      'items_updated': 'Items Updated',
      'invoice_updated': 'Invoice Updated',

      // Creation - all have clean names without underscores
      'creation': 'Created',
      'proforma_created': 'Proforma Created',
      'invoice_created': 'Invoice Created',

      // Conversion - all have clean names without underscores
      'conversion': 'Conversion',
      'proforma_converted': 'Proforma Converted'
    };

    // Get appropriate badge class or use a default
    const badgeClass = badgeClasses[effectiveType] || 'bg-gray-100 text-gray-800';

    // Get appropriate icon or use default Activity icon
    const icon = icons[effectiveType] || <Activity className="w-4 h-4 mr-1" />;

    // Get friendly display name or format the type string
    let displayName = displayNames[effectiveType];

    // If no mapping exists, format the string nicely WITHOUT underscores
    if (!displayName) {
      displayName = effectiveType
        .split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    }

    // Ensure all activity types are properly formatted with no underscores and capitalized first letters
    if (typeof displayName === 'string' && displayName.includes('_')) {
      displayName = displayName
        .split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    }

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit ${badgeClass}`}>
        {icon}
        {displayName}
      </span>
    );
  };

  const formatActionText = (action) => {
    if (!action) return '';

    // Remove any leading/trailing whitespace
    const trimmedAction = action.trim();

    // Split by underscore and capitalize each word
    return trimmedAction
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Helper Functions
  const formatDate = (dateString) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '';
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = date.getFullYear();
      return `${day}/${month}/${year}`;
    } catch (e) {
      console.error("Error formatting date:", e);
      return '';
    }
  };

  const formatDateTime = (dateString) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '';
      return date.toLocaleString();
    } catch (e) {
      console.error("Error formatting date time:", e);
      return '';
    }
  };

  const handleFinalizeProforma = async () => {
    try {
      // Confirm with the user
      const confirmFinalize = window.confirm(`Are you sure you want to finalize proforma invoice #${invoice.id}?`);
      if (!confirmFinalize) return;

      // Get the authentication token
      const token = localStorage.getItem('admin_token');
      if (!token) {
        throw new Error('Authentication token not found. Please log in again.');
      }

      // Clean the invoice ID
      let cleanInvoiceId = '';

      if (typeof invoice.id === 'string') {
        cleanInvoiceId = invoice.id.replace(/\D/g, '');
        cleanInvoiceId = parseInt(cleanInvoiceId, 10).toString();
      } else if (typeof invoice.id === 'number') {
        cleanInvoiceId = invoice.id.toString();
      }

      if (!cleanInvoiceId) {
        throw new Error('Invalid invoice ID');
      }

      console.log(`Finalizing proforma invoice ${cleanInvoiceId}`);

      // Call the API to update the status to "Pending"
      const response = await fetch(`${API_URL}/api_admin_invoices.php?f=update_invoice_status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          id: cleanInvoiceId,
          status: 'Pending'
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error Response:', errorText);
        throw new Error(`HTTP error ${response.status}: ${errorText}`);
      }

      const result = await response.json();

      if (result.success) {
        // Update local state first for immediate feedback
        if (onUpdateStatus) {
          onUpdateStatus(invoice.id, 'Pending');
        }

        alert('Proforma invoice has been finalized and is now pending payment.');

        // Refresh activity history if we're on that tab
        if (activeTab === 'activity') {
          fetchActivityHistory();
        }
      } else {
        throw new Error(result.error || 'Failed to finalize proforma invoice');
      }

    } catch (error) {
      console.error('Error finalizing proforma invoice:', error);
      alert('Error finalizing invoice: ' + error.message);
    }
  };

  const handleMarkInvoicePaid = async () => {
    try {
      // If already paid, just inform the user
      if (invoice.status === 'Paid') {
        alert('This invoice is already marked as paid.');
        return;
      }

      // Confirm payment with appropriate message based on invoice type
      const confirmMessage = invoice.isProforma
        ? `Are you sure you want to mark proforma invoice #${invoice.id} as paid? This will convert it to a regular invoice.`
        : `Are you sure you want to mark invoice #${invoice.id} as paid?`;

      const confirmPay = window.confirm(confirmMessage);
      if (!confirmPay) return;

      // Get the authentication token
      const token = localStorage.getItem('admin_token');
      if (!token) {
        throw new Error('Authentication token not found. Please log in again.');
      }

      // Ensure invoice ID is properly formatted
      let cleanInvoiceId = '';

      if (typeof invoice.id === 'string') {
        // Remove any non-numeric characters
        cleanInvoiceId = invoice.id.replace(/\D/g, '');

        // Also trim any leading zeros
        cleanInvoiceId = parseInt(cleanInvoiceId, 10).toString();
      } else if (typeof invoice.id === 'number') {
        cleanInvoiceId = invoice.id.toString();
      }

      if (!cleanInvoiceId) {
        throw new Error('Invalid invoice ID');
      }

      // Logging
      console.log('Attempting to mark invoice as paid:', {
        invoiceId: cleanInvoiceId,
        originalId: invoice.id,
        currentStatus: invoice.status,
        isProforma: invoice.isProforma
      });

      // Call the API to update invoice status
      const response = await fetch(`${API_URL}/api_admin_invoices.php?f=update_invoice_status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          id: cleanInvoiceId,
          status: 'Paid'
        })
      });

      // Check if the response is ok
      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error Response:', errorText);

        // Try to parse the error response
        let errorResponse;
        try {
          errorResponse = JSON.parse(errorText);
        } catch {
          throw new Error(`HTTP error ${response.status}: ${errorText}`);
        }

        // If the error is about already being paid, just inform the user
        if (errorResponse.error === 'Invoice is already marked as paid') {
          alert('This invoice is already marked as paid.');

          // Update the status if onUpdateStatus is provided
          if (onUpdateStatus) {
            onUpdateStatus(invoice.id, 'Paid');
          }
          return;
        }

        throw new Error(errorResponse.error || `HTTP error ${response.status}`);
      }

      const result = await response.json();

      console.log('Invoice payment response:', result);

      if (result.success) {
        // Check if this was a proforma conversion
        if (result.type === 'conversion') {
          console.log(`Proforma conversion successful: ${result.old_id} → ${result.new_id}`);

          alert(`Proforma invoice #${result.old_id} has been paid and converted to regular invoice #${result.new_id}`);

          // Force modal update by passing conversion data
          if (onUpdateStatus) {
            // Pass the complete result object with conversion details
            onUpdateStatus(invoice.id, 'Paid', result);
            return;
          }
        } else {
          // Regular status update
          if (onUpdateStatus) {
            // Set current date as payment date for regular status updates
            const paymentDate = new Date().toISOString().split('T')[0];
            onUpdateStatus(invoice.id, 'Paid', null, paymentDate);
          }

          // Show success message
          alert('Invoice marked as paid successfully');
        }

        // Refresh activity history
        if (activeTab === 'activity') {
          fetchActivityHistory();
        }
      } else {
        // Show error from server
        throw new Error(result.error || 'Failed to mark invoice as paid');
      }
    } catch (error) {
      console.error('Detailed error marking invoice as paid:', {
        message: error.message,
        stack: error.stack,
        invoice: invoice
      });

      // More informative error message
      let errorMessage = 'An error occurred while marking the invoice as paid.';

      if (error.message.includes('Failed to fetch')) {
        errorMessage = 'Network error. Please check your internet connection.';
      } else if (error.message.includes('Authentication')) {
        errorMessage = 'Authentication failed. Please log in again.';
      } else if (error.message.includes('already marked as paid')) {
        errorMessage = 'This invoice is already marked as paid.';
      }

      // Show user-friendly error
      alert(errorMessage);
    }
  };

  const fetchActivityHistory = async () => {
    if (!invoice || !invoice.id) return;

    setLoadingActivity(true);

    try {
      // Clean the invoice ID for the API request
      let invoiceId = invoice.id;
      if (typeof invoiceId === 'string') {
        // Remove any non-numeric characters and leading zeros
        invoiceId = invoiceId.replace(/^#0*/, '').replace(/\D/g, '');
      }

      console.log(`Fetching activity history for invoice ID: ${invoiceId}`);

      // Get the authentication token
      const token = localStorage.getItem('admin_token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      // Make the API request to get activity history - IMPORTANT: f parameter must be in URL, not in body
      const response = await fetch(`${API_URL}/api_admin_invoices.php?f=get_activity_logs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          invoice_id: invoiceId
        })
      });

      // Check if response is OK
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
      }

      // Get response as text first for logging
      const responseText = await response.text();
      console.log('Activity history API response text:', responseText);

      // Check if the response is empty or just whitespace
      if (!responseText || responseText.trim() === '') {
        console.log('Empty response from API, using sample data');
        setActivityHistory(getSampleActivityHistory(invoice));
        return;
      }

      try {
        // Try to parse the response as JSON
        const data = JSON.parse(responseText);

        // Handle different possible response formats
        if (Array.isArray(data)) {
          console.log(`Found ${data.length} activity records`);
          setActivityHistory(data);
        } else if (data && data.activities && Array.isArray(data.activities)) {
          console.log(`Found ${data.activities.length} activity records in wrapper`);
          setActivityHistory(data.activities);
        } else if (data && data.success === false) {
          console.error('API returned error:', data.error);
          setActivityHistory(getSampleActivityHistory(invoice));
        } else {
          console.log('Unexpected data format, using sample data');
          setActivityHistory(getSampleActivityHistory(invoice));
        }
      } catch (parseError) {
        console.error('Failed to parse activity history response:', parseError);
        console.log('Response was:', JSON.stringify(responseText));
        setActivityHistory(getSampleActivityHistory(invoice));
      }
    } catch (error) {
      console.error('Error fetching activity history:', error);
      // Fall back to sample data on error
      setActivityHistory(getSampleActivityHistory(invoice));
    } finally {
      setLoadingActivity(false);
    }
  };

  // Generate sample activity history for preview purposes
  const getSampleActivityHistory = (invoice) => {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const twoDaysAgo = new Date(now.getTime() - 48 * 60 * 60 * 1000);
    const userId = invoice.clientId ? invoice.clientId.replace(/\D/g, '') : '1';

    // Safely extract invoice ID, ensuring it's a string before using replace
    const invoiceIdStr = invoice.id ? (typeof invoice.id === 'string' ? invoice.id : invoice.id.toString()) : '0';
    const cleanInvoiceId = invoiceIdStr.replace(/\D/g, '');

    const baseHistory = [
      {
        id: 1,
        invoice_id: cleanInvoiceId,
        user_id: '1', // Admin user ID
        action: 'Invoice Created',
        description: `Invoice ${invoice.id} created for client ${userId}`,
        user_name: 'Admin User',
        timestamp: twoDaysAgo.toISOString(),
        type: 'system'
      }
    ];

    // Add status-specific entries
    if (invoice.status === 'Paid') {
      baseHistory.push({
        id: 2,
        invoice_id: cleanInvoiceId,
        user_id: '1', // Admin user ID
        action: 'Status Updated',
        description: `Invoice ${invoice.id} status changed from Pending to Paid`,
        user_name: 'Admin User',
        timestamp: oneDayAgo.toISOString(),
        type: 'status'
      });

      baseHistory.push({
        id: 3,
        invoice_id: cleanInvoiceId,
        user_id: userId,
        action: 'Payment Received',
        description: `Payment of ${invoice.amount} received via ${invoice.paymentMethod || 'Bank Transfer'}`,
        user_name: 'System',
        timestamp: oneHourAgo.toISOString(),
        type: 'payment'
      });
    } else if (invoice.status === 'Pending') {
      baseHistory.push({
        id: 2,
        invoice_id: cleanInvoiceId,
        user_id: '1', // Admin user ID
        action: 'Invoice Sent',
        description: `Invoice ${invoice.id} sent to client`,
        user_name: 'Admin User',
        timestamp: oneDayAgo.toISOString(),
        type: 'notification'
      });
    } else if (invoice.status === 'Overdue') {
      baseHistory.push({
        id: 2,
        invoice_id: cleanInvoiceId,
        user_id: '1', // Admin user ID
        action: 'Invoice Sent',
        description: `Invoice ${invoice.id} sent to client`,
        user_name: 'Admin User',
        timestamp: twoDaysAgo.toISOString(),
        type: 'notification'
      });

      baseHistory.push({
        id: 3,
        invoice_id: cleanInvoiceId,
        user_id: 'system',
        action: 'Status Updated',
        description: `Invoice ${invoice.id} status changed from Pending to Overdue`,
        user_name: 'System',
        timestamp: oneDayAgo.toISOString(),
        type: 'status'
      });

      baseHistory.push({
        id: 4,
        invoice_id: cleanInvoiceId,
        user_id: '1', // Admin user ID
        action: 'Reminder Sent',
        description: `Payment reminder sent for invoice #${invoice.id}`,
        user_name: 'Admin User',
        timestamp: oneHourAgo.toISOString(),
        type: 'notification'
      });
    }

    return baseHistory;
  };

  // Render status badge with enhanced display for invoice type and number
  const renderStatusBadge = (status, isProforma = false) => {
    // Standard status badges for regular invoices
    const badgeClasses = {
      'Paid': 'bg-green-100 text-green-800',
      'Pending': 'bg-yellow-100 text-yellow-800',
      'Overdue': 'bg-red-100 text-red-800',
      'Disputed': 'bg-purple-100 text-purple-800',
      'Draft': 'bg-blue-100 text-blue-800'
    };

    const icons = {
      'Paid': <CheckCircle className="w-4 h-4 mr-1" />,
      'Pending': <Clock className="w-4 h-4 mr-1" />,
      'Overdue': <AlertCircle className="w-4 h-4 mr-1" />,
      'Disputed': <AlertCircle className="w-4 h-4 mr-1" />,
      'Draft': <File className="w-4 h-4 mr-1" />
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit ${badgeClasses[status] || 'bg-gray-100 text-gray-800'}`}>
        {icons[status] || <AlertCircle className="w-4 h-4 mr-1" />}
        {status}
      </span>
    );
  };

  // Render invoice type badge (Proforma or Invoice)
  const renderTypeBadge = (isProforma) => {
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit ${isProforma ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'}`}>
        <FileText className="w-4 h-4 mr-1" />
        {isProforma ? 'Proforma' : 'Invoice'}
      </span>
    );
  };

  // Render invoice number badge
  const renderNumberBadge = (id) => {
    return (
      <span className="px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit bg-gray-100 text-gray-800">
        <File className="w-4 h-4 mr-1" />
        #{id}
      </span>
    );
  };

  return (
    <div className={`bg-white rounded-lg shadow flex flex-col max-h-[90vh] min-w-[800px] ${className}`}>
      {/* Invoice Header - conditionally rendered and sticky */}
      {!hideHeader && (
        <div className="p-6 border-b bg-white">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
            <div>
              <h2 className="text-xl font-bold text-gray-800 mb-3">
                {invoice.isProforma ? 'Proforma Invoice ' : 'Invoice '}{invoice.id}
              </h2>
              <div className="flex flex-wrap gap-2">
                {/* Display all three badges: type, number, and status */}
                {renderTypeBadge(invoice.isProforma)}
                {renderNumberBadge(invoice.id)}
                {renderStatusBadge(invoice.status)}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tabs Navigation - sticky below header */}
      <div className="px-6 bg-white shadow-sm">
        <div className="flex border-b border-gray-200 overflow-x-auto">
          <button
            className={`py-2 px-4 font-medium text-sm whitespace-nowrap ${
              activeTab === 'details'
                ? 'text-indigo-700 border-b-2 border-indigo-700'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('details')}
          >
            <FileText className="w-4 h-4 inline mr-1" />
            Invoice Details
          </button>
          <button
            className={`py-2 px-4 font-medium text-sm whitespace-nowrap ${
              activeTab === 'activity'
                ? 'text-indigo-700 border-b-2 border-indigo-700'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('activity')}
          >
            <Activity className="w-4 h-4 inline mr-1" />
            Activity History
          </button>
        </div>
      </div>

      {/* Invoice Content - Scrollable area */}
       {/* Invoice Content - Based on Active Tab */}
       <div className="p-6 space-y-6 min-h-[500px]">
         {/* Invoice Details Tab */}
         {activeTab === 'details' && (
           <>
             <div className="grid grid-cols-1 md:grid-cols-2 gap-6 border-b py-6">
               <div className="space-y-4">
                 <div>
                   <div className="text-sm text-gray-500">Client</div>
                   <div className="font-medium">{invoice.client}</div>
                   <div className="text-sm text-gray-500 mt-1">{invoice.clientEmail}</div>
                   <div className="text-sm text-gray-500">Client ID: {invoice.clientId}</div>
                 </div>

                 <div>
                   <div className="text-sm text-gray-500">Payment Method</div>
                   <div className="font-medium">{invoice.paymentMethod || 'Not specified'}</div>
                 </div>
               </div>

               <div className="space-y-4">
                 <div className="grid grid-cols-2 gap-4">
                   <div>
                     <div className="text-sm text-gray-500">Issue Date</div>
                     <div className="font-medium">{formatDate(invoice.date)}</div>
                   </div>
                   <div>
                     <div className="text-sm text-gray-500">Due Date</div>
                     <div className="font-medium">{formatDate(invoice.dueDate)}</div>
                   </div>
                 </div>

                 {invoice.status === 'Paid' && (
                   <div>
                     <div className="text-sm text-gray-500">Payment Date</div>
                     <div className="font-medium">{formatDate(invoice.paymentDate)}</div>
                   </div>
                 )}

                 <div>
                   <div className="text-sm text-gray-500">Total Amount</div>
                   <div className="text-xl font-bold text-indigo-700">{invoice.amount}</div>
                 </div>
               </div>
             </div>

             {/* Invoice Items */}
             <div>
               <h3 className="text-lg font-bold mb-3">Invoice Items</h3>
               <div className="border rounded-md overflow-hidden">
                 <table className="w-full">
                   <thead className="bg-gray-50 text-left text-xs text-gray-500">
                     <tr>
                       <th className="py-3 px-4 font-medium">DESCRIPTION</th>
                       <th className="py-3 px-4 font-medium text-right">QUANTITY</th>
                       <th className="py-3 px-4 font-medium text-right">UNIT PRICE</th>
                       <th className="py-3 px-4 font-medium text-right">TOTAL</th>
                     </tr>
                   </thead>
                   <tbody className="divide-y">
                     {invoice.items && invoice.items.map((item, idx) => (
                       <tr key={idx} className="text-sm">
                         <td className="py-3 px-4 text-gray-800">{item.description}</td>
                         <td className="py-3 px-4 text-gray-800 text-right">{item.quantity}</td>
                         <td className="py-3 px-4 text-gray-800 text-right">{item.unitPrice}</td>
                         <td className="py-3 px-4 font-medium text-right">{item.total}</td>
                       </tr>
                     ))}
                   </tbody>
                   <tfoot className="bg-gray-50">
                     <tr>
                       <td colSpan="3" className="py-3 px-4 text-right font-medium">Subtotal</td>
                       <td className="py-3 px-4 text-right font-medium">{invoice.subtotal || '€0.00'}</td>
                     </tr>
                     <tr>
                       <td colSpan="3" className="py-3 px-4 text-right font-medium">Tax</td>
                       <td className="py-3 px-4 text-right font-medium">{invoice.tax || '€0.00'}</td>
                     </tr>
                     <tr className="font-bold">
                       <td colSpan="3" className="py-3 px-4 text-right">Total</td>
                       <td className="py-3 px-4 text-right text-indigo-700">{invoice.total || invoice.amount || '€0.00'}</td>
                     </tr>
                   </tfoot>
                 </table>
               </div>
             </div>

             {/* Notes */}
             {invoice.notes && (
               <div>
                 <h3 className="text-lg font-bold mb-2">Notes</h3>
                 <div className="bg-gray-50 rounded-md p-4">
                   <p className="text-sm text-gray-700">{invoice.notes}</p>
                 </div>
               </div>
             )}
           </>
         )}

         {/* Activity History Tab */}
         {activeTab === 'activity' && (
           <div className="min-h-[500px] flex flex-col">

             <div className="flex justify-between items-center mb-4 mt-0">
               <h3 className="text-lg font-bold">Activity History</h3>
               <button
                 onClick={fetchActivityHistory}
                 className="p-2 text-gray-500 hover:text-indigo-700 rounded-full hover:bg-gray-50"
                 disabled={loadingActivity}
               >
                 <RefreshCw className={`w-4 h-4 ${loadingActivity ? 'animate-spin' : ''}`} />
               </button>
             </div>

             {loadingActivity ? (
               <div className="text-center py-8">
                 <RefreshCw className="w-8 h-8 animate-spin text-indigo-600 mx-auto mb-4" />
                 <p className="text-gray-600">Loading activity history...</p>
               </div>
             ) : activityHistory.length > 0 ? (
               <div className="space-y-6 flex flex-col justify-end min-h-[400px]">
                 {/* Timeline UI */}
                 <div className="relative">
                   {/* Timeline line */}
                   <div className="absolute left-16 top-0 h-full w-0.5 bg-gray-200"></div>

                   {/* Activity items - reversed to start from bottom */}
                   {[...activityHistory].reverse().map((activity, index) => (
                     <div key={activity.id} className="mb-6 relative">
                       <div className="flex items-start">
                         {/* Timeline dot */}
                         <div className="absolute left-16 mt-2 w-5 h-5 rounded-full bg-indigo-100 border-2 border-indigo-500 z-10 transform -translate-x-1/2"></div>

                         {/* Activity time */}
                         <div className="w-16 text-xs text-gray-500 mt-1 text-right pr-4">
                           {new Date(activity.timestamp).toLocaleDateString()}
                         </div>

                         {/* Activity content */}
                         <div className="flex-1 bg-white rounded-lg border p-4 shadow-sm ml-5">
                           <div className="flex justify-between mb-1">
                             <div className="font-medium text-gray-800">{formatActionText(activity.action)}</div>
                             {activity.type && renderActivityBadge(activity.type)}
                           </div>
                           <p className="text-sm text-gray-600 mb-2">{activity.description}</p>
                           <div className="flex text-xs text-gray-500 items-center">
                             <User className="w-3 h-3 mr-1" />
                             <span className="mr-3">{activity.user_name}</span>
                             <Clock className="w-3 h-3 mr-1" />
                             <span>{formatDateTime(activity.timestamp)}</span>
                           </div>
                         </div>
                       </div>
                     </div>
                   ))}
                 </div>
               </div>
             ) : (
               <div className="bg-gray-50 rounded-lg p-8 text-center">
                 <Activity className="w-10 h-10 text-gray-400 mx-auto mb-3" />
                 <p className="text-gray-600 mb-1">No activity records found for this invoice.</p>
                 <p className="text-sm text-gray-500">Activities will be recorded when actions are performed on this invoice.</p>
               </div>
             )}
           </div>
         )}

         {/* Invoice Actions - conditional based on status */}
         {!hideActions && (
           <div className="flex flex-wrap gap-3 justify-end pt-4 mt-4 border-t">
             {/* Added consistent edit button here too, for all invoice types */}
             {onEdit && (
               <button
                 onClick={() => onEdit(invoice)}
                 className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 flex items-center text-sm hover:bg-gray-50"
               >
                 <Edit className="w-4 h-4 mr-1" />
                 Edit Invoice
               </button>
             )}

             {invoice.status === 'Draft' && onUpdateStatus && (
               <button
                 onClick={() => onUpdateStatus(invoice.id, 'Pending')}
                 className="px-4 py-2 bg-indigo-700 text-white rounded-md flex items-center text-sm hover:bg-indigo-800"
               >
                 <Send className="w-4 h-4 mr-1" />
                 Finalize & Send
               </button>
             )}

             {invoice.status === 'Pending' && (
               <>
                 <button
                   onClick={handleMarkInvoicePaid}
                   className="px-4 py-2 bg-green-600 text-white rounded-md flex items-center text-sm hover:bg-green-700"
                 >
                   <CheckCircle className="w-4 h-4 mr-1" />
                   Mark as Paid
                 </button>
                 {onSend && (
                   <button
                     onClick={() => onSend(invoice, 'reminder')}
                     className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 flex items-center text-sm hover:bg-gray-50"
                   >
                     <Mail className="w-4 h-4 mr-1" />
                     Send Reminder
                   </button>
                 )}
               </>
             )}

             {invoice.status === 'Overdue' && (
               <>
                 {onSend && (
                   <button
                     onClick={() => onSend(invoice, 'reminder')}
                     className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 flex items-center text-sm hover:bg-gray-50"
                   >
                     <Mail className="w-4 h-4 mr-1" />
                     Send Reminder
                   </button>
                 )}
                 <button
                   onClick={handleMarkInvoicePaid}
                   className="px-4 py-2 bg-green-600 text-white rounded-md flex items-center text-sm hover:bg-green-700"
                 >
                   <CheckCircle className="w-4 h-4 mr-1" />
                   Mark as Paid
                 </button>
               </>
             )}

             {invoice.status === 'Disputed' && onUpdateStatus && (
               <button
                 onClick={() => onUpdateStatus(invoice.id, 'Pending')}
                 className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 flex items-center text-sm hover:bg-gray-50"
               >
                 <Edit className="w-4 h-4 mr-1" />
                 Resolve Dispute
               </button>
             )}

             {invoice.status === 'Paid' && (
               <>
                 <button
                   className="px-4 py-2 bg-indigo-700 text-white rounded-md flex items-center text-sm hover:bg-indigo-800"
                 >
                   <ExternalLink className="w-4 h-4 mr-1" />
                   View Receipt
                 </button>
               </>
             )}
           </div>
         )}

     </div>


    </div>
  );
};

export default InvoiceView;