import React, { useState } from 'react';
import { HardDrive, Refresh<PERSON><PERSON>, <PERSON><PERSON><PERSON>riangle, CheckCircle } from 'lucide-react';
import { API_URL } from '../config';
const EnhancedStorageDetectionButton = ({ selectedItem, onUpdateStorage, isEditMode, serverType }) => {
  const [detecting, setDetecting] = useState(false);
  const [detectionResults, setDetectionResults] = useState(null);
  const [error, setError] = useState(null);

  const storageConfigurations = [
    { id: 1, size: 240, label: '240GB SSD' },
    { id: 2, size: 500, label: '500GB SSD' },
    { id: 3, size: 1000, label: '1TB SSD' },
    { id: 4, size: 2000, label: '2TB SSD' },
    { id: 5, size: 4000, label: '4TB SSD' },
    { id: 6, size: 6000, label: '6TB SSD' },
    { id: 7, size: 8000, label: '8TB SSD' },
    { id: 8, size: 10000, label: '10TB HDD' },
    { id: 9, size: 12000, label: '12TB HDD' }
  ];

  const findClosestStorageConfig = (sizeGb) => {
    const sizeNum = Number(sizeGb);
    if (isNaN(sizeNum)) return null;
    return storageConfigurations.reduce((closest, current) => {
      if (closest === null) return current;
      return Math.abs(current.size - sizeNum) < Math.abs(closest.size - sizeNum) ? current : closest;
    }, null);
  };

  // Improved parsing function that correctly handles different iDRAC output formats
  const parseRawIdracOutput = (rawOutput) => {
    console.log("Raw output to parse:", rawOutput);
    const lines = rawOutput.split('\n');
    const disks = [];
    
    let currentDisk = null;
    let currentBayNumber = null;
    let currentSize = null;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Match different disk identifier patterns:
      // 1. Disk.Direct.0-1:AHCI.Embedded.2-1 (format in first example)
      // 2. Disk.Direct.0-2:NonRAID.Embedded.2-1 (format in second example)
      // 3. Disk.Bay.1:... (alternative format)
      const diskMatch = line.match(/Disk\.(Direct\.(\d+)-(\d+)|Bay\.(\d+)):/i);
      
      if (diskMatch) {
        // Save previous disk if we found one
        if (currentDisk && currentSize) {
          disks.push({
            id: currentDisk,
            size_gb: currentSize,
            bay_number: currentBayNumber
          });
        }
        
        currentDisk = line;
        currentSize = null;
        
        // Extract bay number - prioritize the number after the hyphen in "Direct.X-Y" format
        // Then add +1 to convert from 0-indexed to 1-indexed bay numbering
        if (diskMatch[3]) {
          // This is the Y in Direct.X-Y format - the correct bay number
          currentBayNumber = parseInt(diskMatch[3], 10) + 1;
        } else if (diskMatch[4]) {
          // This is the Bay.X format
          currentBayNumber = parseInt(diskMatch[4], 10) + 1;
        } else if (diskMatch[2]) {
          // Fallback to X in Direct.X-Y if somehow Y isn't captured
          currentBayNumber = parseInt(diskMatch[2], 10) + 1;
        } else {
          // Default bay number if nothing else matches
          currentBayNumber = 1; // Changed from 0 to 1
        }
      }
      
      // Extract disk size
      const sizeMatch = line.match(/Size\s*=\s*([\d\.]+)\s*(GB|TB)/i);
      if (sizeMatch) {
        const size = parseFloat(sizeMatch[1]);
        const unit = sizeMatch[2].toUpperCase();
        // Convert to GB if size is in TB
        currentSize = unit === 'TB' ? size * 1000 : size;
      }
    }
    
    // Add the last disk
    if (currentDisk && currentSize) {
      disks.push({
        id: currentDisk,
        size_gb: currentSize,
        bay_number: currentBayNumber
      });
    }
    
    // Remove duplicate bay numbers by incrementing duplicates
    const usedBayNumbers = new Set();
    const uniqueDisks = disks.map(disk => {
      let bayNumber = disk.bay_number;
      
      // If bay number is already used, find next available number
      if (usedBayNumbers.has(bayNumber)) {
        let newBayNumber = bayNumber;
        while (usedBayNumbers.has(newBayNumber)) {
          newBayNumber++;
        }
        bayNumber = newBayNumber;
        disk.bay_fixed = true; // Mark that we adjusted the bay number
      }
      
      usedBayNumbers.add(bayNumber);
      return { ...disk, bay_number: bayNumber };
    });
    
    return uniqueDisks;
  };

  const detectStorage = async () => {
    if (!selectedItem || !selectedItem.ipmi) {
      setError("No valid iDRAC IP address available");
      return;
    }
    try {
      setDetecting(true);
      setError(null);
      setDetectionResults(null);
      const token = localStorage.getItem('admin_token');
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=detect_idrac_storage`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          token: token,
          server_id: selectedItem.id,
          ipmi_address: selectedItem.ipmi,
          ipmi_username: 'root',
          ipmi_password: selectedItem.ipmi_root_pass || '',
          idrac_version: selectedItem.idrac_version || 8,
          server_type: serverType || 'blade' // Send server type to backend
        })
      });
      if (!response.ok) throw new Error(`HTTP error ${response.status}`);
      const data = await response.json();
      if (data.success) {
        // Use our improved parser instead of relying on the server's parsing
        const processedDisks = parseRawIdracOutput(data.raw_output);
        
        const detectedDisks = processedDisks.map(disk => {
          const matchedConfig = findClosestStorageConfig(disk.size_gb);
          return { 
            ...disk, 
            matched_config: matchedConfig,
            bay_source: 'direct_pattern' // Indicate how we detected the bay
          };
        });
        
        setDetectionResults({ 
          disks: detectedDisks, 
          raw_data: data.raw_output,
          pendingUpdates: false // Add flag to track if changes are pending
        });
        
        // Create bay updates for the component but don't apply yet
        const updatedBays = {};
        detectedDisks.forEach(disk => {
          if (disk.bay_number !== null && disk.bay_number !== undefined && disk.matched_config) {
            // We use bay_number directly since it's already adjusted to 1-based numbering
            updatedBays[`bay${disk.bay_number}`] = disk.matched_config.id;
          }
        });
        
        // Store the updates but don't apply them until confirmed
        if (Object.keys(updatedBays).length > 0) {
          setDetectionResults(prev => ({
            ...prev,
            updatedBays,
            pendingUpdates: true
          }));
        }
      } else {
        setError(data.message || "Storage detection failed");
      }
    } catch (err) {
      console.error("Error detecting storage:", err);
      setError(err.message || "Failed to detect storage");
    } finally {
      setDetecting(false);
    }
  };

  // Function to apply the detected changes
  const applyStorageChanges = () => {
    if (!detectionResults || !detectionResults.updatedBays) {
      setError("No storage changes to apply");
      return;
    }
    
    // Confirm before applying changes
    if (window.confirm(`Are you sure you want to update the storage configuration for ${selectedItem.label || 'this server'}?`)) {
      console.log("Applying storage changes:", detectionResults.updatedBays);
      
      // Make sure to include the server ID in the updates
      const updatedBaysWithId = {
        ...detectionResults.updatedBays,
        id: selectedItem.id, // Ensure server ID is included
        serverType: serverType || 'blade' // Include server type with proper default
      };
      
      // Call the update function from props
      onUpdateStorage(updatedBaysWithId);
      
      // Mark as applied
      setDetectionResults(prev => ({
        ...prev,
        pendingUpdates: false,
        changesApplied: true
      }));
    }
  };

  if (isEditMode) return null;

  return (
    <div className="mb-4">
      <button
        onClick={detectStorage}
        disabled={detecting}
        className={`flex items-center px-3 py-2 rounded text-sm font-medium
          ${detecting ? 'bg-gray-200 text-gray-600' : 'bg-blue-100 hover:bg-blue-200 text-blue-800'}`}
      >
        {detecting ? <RefreshCw className="w-4 h-4 mr-2 animate-spin" /> : <HardDrive className="w-4 h-4 mr-2" />}
        {detecting ? 'Detecting Storage...' : 'Auto-Detect Storage'}
      </button>
      
      {error && (
        <div className="mt-2 p-2 bg-red-50 text-red-700 rounded-md text-sm flex items-start">
          <AlertTriangle className="w-4 h-4 mr-2 mt-0.5 flex-shrink-0" />
          <span>{error}</span>
        </div>
      )}

      {detectionResults && !error && (
        <div className="mt-2 p-2 bg-green-50 text-green-800 rounded-md text-sm">
          <div className="flex items-center mb-2">
            <CheckCircle className="w-4 h-4 mr-2" />
            <span className="font-medium">Storage detected successfully! (Using 1-based bay numbering)</span>
          </div>

          <div className="mt-2">
            <h4 className="font-medium text-xs">Detected Disks:</h4>
            {detectionResults.disks.length > 0 ? (
              <ul className="mt-1 space-y-1">
                {detectionResults.disks.map((disk, index) => (
                  <li key={index} className="flex items-center">
                    <span className="text-xs">
                      {`Bay ${disk.bay_number}: ${disk.size_gb.toFixed(2)}GB → ${disk.matched_config?.label || 'Unknown'}`}
                      {disk.bay_fixed && <span className="text-blue-700 ml-1">(auto-assigned bay)</span>}
                      {disk.bay_source && !disk.bay_fixed && (
                        <span className="text-green-700 ml-1">
                          {disk.bay_source === 'direct_pattern' && '(detected)'}
                        </span>
                      )}
                    </span>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-red-700 text-xs">No disks detected!</p>
            )}
          </div>

          {detectionResults.pendingUpdates && (
            
              <button
                onClick={applyStorageChanges}
                className="mt-2 px-3 py-1 bg-blue-600 text-white rounded text-xs font-medium hover:bg-blue-700"
              >
                Apply These Changes
              </button>
    
          )}

          {detectionResults.changesApplied && (
            <div className="mt-3 p-2 bg-green-100 rounded border border-green-200">
              <p className="text-green-800 text-xs font-medium flex items-center">
                <CheckCircle className="w-3 h-3 mr-1" />
                Storage configuration changes applied successfully!
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default EnhancedStorageDetectionButton;