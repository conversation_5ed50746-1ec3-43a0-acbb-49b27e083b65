import React, { useState, useEffect } from 'react';
import { XCircle, AlertCircle, Network, Search, RefreshCw, CheckCircle, Filter, Divide, ArrowRight } from 'lucide-react';
import axios from 'axios';
import { API_URL } from '../config';
const SelectSubnetModal = ({ 
  onClose, 
  onSelectSubnet, 
  isMainSubnet = true,
  currentValue = '',
  serverType,
  serverId
}) => {
  const [subnets, setSubnets] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [dividingSubnet, setDividingSubnet] = useState(null);
  const [newSubnetSize, setNewSubnetSize] = useState('');
  const [divideLoading, setDivideLoading] = useState(false);
  const [divideError, setDivideError] = useState('');
  const [divideSuccess, setDivideSuccess] = useState('');
  
  // Fetch available subnets
  useEffect(() => {
    const fetchSubnets = async () => {
      try {
        setLoading(true);
        setError('');
        
        const token = localStorage.getItem('admin_token');
        
        const response = await axios.post(`${API_URL}/api_admin_subnets.php?f=get_subnets', {
          token,
          status: 'Available', // Only request available subnets from API
          search: searchQuery
        });
        
        if (Array.isArray(response.data)) {
          // Additional filtering to ensure subnets are truly available
          const availableSubnets = response.data.filter(subnet => {
            // Check that subnet isn't assigned
            if (subnet.assigned_server_id) return false;
            
            // Check status property (should be 'Available')
            if (subnet.status !== 'Available') return false;
            
            // Check for descendants property if available
            if (subnet.descendants && subnet.descendants.some(
              child => child.assigned_server_id || child.status === 'Unavailable'
            )) {
              return false;
            }
            
            return true;
          });
          
          setSubnets(availableSubnets);
        } else {
          setSubnets([]);
        }
      } catch (err) {
        console.error('Error fetching subnets:', err);
        setError('Failed to load available subnets');
      } finally {
        setLoading(false);
      }
    };
    
    fetchSubnets();
  }, [searchQuery, divideSuccess]);
  
  // Handle search input
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };
  
  // Select a subnet
  const handleSelectSubnet = (subnet) => {
    onSelectSubnet(subnet);
  };
  
  // Start dividing a subnet
  const handleStartDivide = (subnet) => {
    setDividingSubnet(subnet);
    // Calculate appropriate default subnet size based on current size
    const currentSize = parseInt(subnet.cidr.split('/')[1]);
    setNewSubnetSize(currentSize + 1);
  };
  
  // Cancel dividing
  const handleCancelDivide = () => {
    setDividingSubnet(null);
    setNewSubnetSize('');
    setDivideError('');
  };
  
  // Handle dividing subnet
  const handleDivideSubnet = async () => {
    try {
      setDivideLoading(true);
      setDivideError('');
      
      const token = localStorage.getItem('admin_token');
      
      // Extract subnet ID from SUB-xxxx format if needed
      const subnetId = typeof dividingSubnet.id === 'string' && dividingSubnet.id.startsWith('SUB-')
        ? parseInt(dividingSubnet.id.replace('SUB-', ''))
        : dividingSubnet.id;
      
      const response = await axios.post(`${API_URL}/api_admin_subnets.php?f=divide_subnet', {
        token,
        subnet_id: subnetId,
        new_subnet_size: parseInt(newSubnetSize),
        generate_ips: true
      });
      
      if (response.data.success) {
        setDivideSuccess(`Subnet successfully divided into ${response.data.new_subnet_ids?.length || 'multiple'} subnets`);
        // Reset form
        setTimeout(() => {
          setDividingSubnet(null);
          setNewSubnetSize('');
          setDivideSuccess('');
        }, 2000);
      } else {
        throw new Error(response.data.error || 'Failed to divide subnet');
      }
    } catch (err) {
      console.error('Error dividing subnet:', err);
      setDivideError(err.response?.data?.error || err.message || 'Failed to divide subnet');
    } finally {
      setDivideLoading(false);
    }
  };
  
  // Format CIDR nicely
  const formatCidr = (cidr) => {
    if (!cidr) return '';
    const [subnet, mask] = cidr.split('/');
    return (
      <span className="font-mono">
        {subnet}<span className="text-indigo-700">/{mask}</span>
      </span>
    );
  };
  
  // Render utilization badge
  const renderUtilizationBadge = (percent) => {
    let badgeClass = '';
    if (percent >= 90) {
      badgeClass = 'bg-red-100 text-red-800';
    } else if (percent >= 75) {
      badgeClass = 'bg-yellow-100 text-yellow-800';
    } else if (percent >= 50) {
      badgeClass = 'bg-blue-100 text-blue-800';
    } else {
      badgeClass = 'bg-green-100 text-green-800';
    }
    
    return (
      <span className={`px-2 py-0.5 rounded-full text-xs font-medium w-fit ${badgeClass}`}>
        {percent}%
      </span>
    );
  };
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-[85vh]">
        <div className="p-4 border-b flex justify-between items-center">
          <h2 className="text-lg font-bold text-gray-800">
            {dividingSubnet 
              ? 'Divide Subnet' 
              : isMainSubnet 
                ? 'Select Main Subnet' 
                : 'Add Additional Subnet'}
          </h2>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <XCircle className="w-5 h-5" />
          </button>
        </div>
        
        <div className="p-4 overflow-y-auto max-h-[calc(85vh-120px)]">
          {error && (
            <div className="bg-red-50 text-red-700 p-3 rounded-md mb-4 flex items-center">
              <AlertCircle className="w-5 h-5 mr-2" />
              {error}
            </div>
          )}
          
          {dividingSubnet ? (
            <div className="space-y-4">
              <div className="p-3 bg-indigo-50 rounded-md">
                <h3 className="font-medium mb-1">Dividing Subnet</h3>
                <div className="flex items-center space-x-4">
                  <div className="text-lg font-mono">{dividingSubnet.cidr}</div>
                  <ArrowRight className="w-5 h-5 text-indigo-500" />
                  <div className="text-lg font-mono">?/{newSubnetSize}</div>
                </div>
                
                <p className="text-sm text-gray-600 mt-2">
                  The subnet will be divided into {Math.pow(2, newSubnetSize - parseInt(dividingSubnet.cidr.split('/')[1]))} smaller subnets of size /{newSubnetSize}
                </p>
              </div>
              
              {divideError && (
                <div className="bg-red-50 text-red-700 p-3 rounded-md flex items-center">
                  <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0" />
                  <span>{divideError}</span>
                </div>
              )}
              
              {divideSuccess && (
                <div className="bg-green-50 text-green-700 p-3 rounded-md flex items-center">
                  <CheckCircle className="w-5 h-5 mr-2 flex-shrink-0" />
                  <span>{divideSuccess}</span>
                </div>
              )}
              
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    New Subnet Size
                  </label>
                  <select
                    value={newSubnetSize}
                    onChange={(e) => setNewSubnetSize(e.target.value)}
                    className="w-full p-2 border rounded-md"
                  >
                    {Array.from(
                      { length: 31 - parseInt(dividingSubnet.cidr.split('/')[1]) },
                      (_, i) => parseInt(dividingSubnet.cidr.split('/')[1]) + i + 1
                    )
                      .filter(size => size <= 31) // Typically, don't go beyond /31
                      .map(size => (
                        <option key={size} value={size}>
                          /{size} - {Math.pow(2, 32 - size) - 2} IPs
                        </option>
                      ))}
                  </select>
                  <p className="mt-1 text-sm text-gray-500">
                    The current subnet size is /{dividingSubnet.cidr.split('/')[1]}
                  </p>
                </div>
                
                <div className="flex justify-end space-x-3 mt-4">
                  <button
                    type="button"
                    onClick={handleCancelDivide}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md text-sm hover:bg-gray-50"
                    disabled={divideLoading}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={handleDivideSubnet}
                    className="px-4 py-2 bg-indigo-600 text-white rounded-md text-sm hover:bg-indigo-700 flex items-center"
                    disabled={divideLoading}
                  >
                    {divideLoading ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        Dividing...
                      </>
                    ) : (
                      <>
                        <Divide className="w-4 h-4 mr-2" />
                        Divide Subnet
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <>
              {/* Current value */}
              {currentValue && (
                <div className="bg-gray-50 p-3 rounded-md mb-4">
                  <div className="text-sm text-gray-500 mb-1">Currently assigned:</div>
                  <div className="font-mono text-indigo-700">{currentValue}</div>
                </div>
              )}
              
              {/* Search field */}
              <div className="relative mb-4">
                <input
                  type="text"
                  placeholder="Search by subnet, name, or location..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                  className="w-full px-3 py-2 pl-9 border rounded-md"
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              </div>
              
              {/* Subnets list */}
              <div className="overflow-y-auto">
                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <RefreshCw className="w-6 h-6 animate-spin text-indigo-600 mr-2" />
                    <span>Loading subnets...</span>
                  </div>
                ) : subnets.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    
                    <ul className="list-disc text-left pl-8 pt-2">
                      <li>No available subnets found.</li>
                      <li>Subnets must not be assigned to any server</li>
                      <li>Subnets must not have child subnets assigned to servers</li>
                    </ul>
                  
                  </div>
                ) : (
                  <div className="grid grid-cols-1 gap-3">
                    {subnets.map(subnet => (
                      <div 
                        key={subnet.id}
                        className="border rounded-md p-3 hover:bg-indigo-50 cursor-pointer"
                        onClick={() => handleSelectSubnet(subnet)}
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <div className="font-semibold text-gray-800">{subnet.name}</div>
                            <div className="text-gray-600">{formatCidr(subnet.cidr)}</div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm text-gray-600">{subnet.location}</div>
                            <div className="text-xs text-gray-500">{subnet.category}</div>
                          </div>
                        </div>
                        
                        <div className="mt-2 flex items-center justify-between">
                          <div className="flex items-center">
                            {renderUtilizationBadge(subnet.utilizationPercent)}
                            <span className="ml-2 text-xs text-gray-600">
                              {subnet.usedIPs}/{subnet.totalIPs} IPs used
                            </span>
                          </div>
                          <div className="flex space-x-2">
                            <button 
                              className="px-2 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md text-xs flex items-center"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleStartDivide(subnet);
                              }}
                              title="Divide into smaller subnets"
                            >
                              <Divide className="w-3 h-3 mr-1" />
                              Divide
                            </button>
                            <button 
                              className="px-2 py-1 bg-indigo-100 hover:bg-indigo-200 text-indigo-700 rounded-md text-xs flex items-center"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleSelectSubnet(subnet);
                              }}
                            >
                              <CheckCircle className="w-3 h-3 mr-1" />
                              Select
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </>
          )}
        </div>
        
        <div className="p-4 border-t flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 text-sm"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
};

export default SelectSubnetModal;