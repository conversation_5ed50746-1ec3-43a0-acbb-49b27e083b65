import React, { useState, useEffect } from 'react';
import { Network, Wifi, Link, Plus, RefreshCw, CheckCircle, AlertTriangle, XCircle, Lock, Power, ArrowUpCircle, ArrowDownCircle } from 'lucide-react';
import { API_URL } from '../config';
const NetworkConnections = ({ server, serverType, onRefresh, editable = false, isEditMode = false }) => {
  // Use local server state to enable immediate UI updates
  const [localServer, setLocalServer] = useState(server);
  const [switchData, setSwitchData] = useState(null);
  const [portDetails, setPortDetails] = useState({});
  const [availablePorts, setAvailablePorts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [assignModalOpen, setAssignModalOpen] = useState(false);
  const [selectedPort, setSelectedPort] = useState(null);
  const [switches, setSwitches] = useState([]);
  const [selectedSwitch, setSelectedSwitch] = useState(server?.switch_id || null);
  const [toggleConfirmOpen, setToggleConfirmOpen] = useState(false);
  const [portToToggle, setPortToToggle] = useState(null);
  const [toggleAction, setToggleAction] = useState(null);
  const [toggleInProgress, setToggleInProgress] = useState(false);
  const [operationalStatus, setOperationalStatus] = useState({});
  const [refreshingStatus, setRefreshingStatus] = useState(false);
  // Add missing state variable for ports
  const [ports, setPorts] = useState([]);
  const prevPortConfigRef = React.useRef('');
  // Update local server when parent server prop changes
  useEffect(() => {
    setLocalServer(server);
  }, [server]);

  // Function to fetch all switches
  const fetchSwitches = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin_token');
      
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_inventory_switches', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      
      const data = await response.json();
      setSwitches(data);
      
      setLoading(false);
    } catch (err) {
      console.error("Error fetching switches:", err);
      setError("Failed to load switches. Please try again.");
      setLoading(false);
    }
  };

  // Function to fetch switch data if needed
  useEffect(() => {
    if (localServer?.switch_id) {
      fetchSwitchData(localServer.switch_id);
      setSelectedSwitch(localServer.switch_id);
    }
  }, [localServer?.switch_id]);



  useEffect(() => {
    if (localServer?.switch_id) {
      // Check if any port has changed by creating a unique string of all port values
      const portConfigKey = `${localServer.port1 || ''}|${localServer.port2 || ''}|${localServer.port3 || ''}|${localServer.port4 || ''}`;
      
      if (prevPortConfigRef.current !== portConfigKey) {
        console.log("Port configuration changed, refreshing data");
        prevPortConfigRef.current = portConfigKey;
        refreshPortData();
      }
    }
  }, [localServer?.switch_id, localServer?.port1, localServer?.port2, localServer?.port3, localServer?.port4]);
  

  // Fetch switch data
  const fetchSwitchData = async (switchId) => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin_token');
      
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_inventory_switches', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      
      const switches = await response.json();
      const targetSwitch = switches.find(s => s.id === parseInt(switchId));
      
      if (targetSwitch) {
        setSwitchData(targetSwitch);
        fetchPortDetails(switchId);
        fetchAvailablePorts(switchId);
      }
      
      setLoading(false);
    } catch (error) {
      console.error("Error fetching switch data:", error);
      setError("Failed to load switch information");
      setLoading(false);
    }
  };

  // Fetch detailed information about all ports for the current server
  const fetchPortDetails = async (switchId) => {
    if (!switchId || !localServer) return;
    
    try {
      const token = localStorage.getItem('admin_token');
      
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_switch_ports', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          token,
          switch_id: switchId
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      
      const allPorts = await response.json();
      
      // Create a map of port number to port details
      const portMap = {};
      
      // Check if the server has any ports connected
      for (let i = 1; i <= 4; i++) {
        const portNumField = `port${i}`;
        const portNum = localServer[portNumField];
        
        if (portNum) {
          // Find the port details
          const portDetail = allPorts.find(p => p.port_number === portNum);
          if (portDetail) {
            portMap[portNumField] = portDetail;
            
            // Also fetch operational status for this port
            fetchPortOperationalStatus(switchId, portDetail.id, portNumField);
          }
        }
      }
      
      setPortDetails(portMap);
      setPorts(allPorts); // Also set the ports state
    } catch (error) {
      console.error("Error fetching port details:", error);
    }
  };

// Replace fetchPortOperationalStatus in NetworkConnections.js with this improved version

// New function to fetch operational status for each port
const fetchPortOperationalStatus = async (switchId, portId, portField) => {
  if (!switchId || !portId) return;
  
  try {
    setOperationalStatus(prev => ({
      ...prev,
      [portField]: {
        status: "Refreshing...",
        lastChecked: new Date().toISOString()
      }
    }));
    
    const token = localStorage.getItem('admin_token');
    
    // Use the new dedicated refresh_port_status endpoint
    const response = await fetch(`${API_URL}/api_admin_inventory.php?f=refresh_port_status', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        token,
        switch_id: switchId,
        port_id: portId
      })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }
    
    const result = await response.json();
    
    if (result.success) {
      // We now receive the proper operational status directly from the API
      const portInfo = result.ports[0];
      let opStatus = "Unknown";
      
      if (portInfo) {
        // Determine status based on admin_status and operational_status
        if (portInfo.status === "Disabled") {
          opStatus = "Administratively Down";
        } else if (portInfo.operational_status === "Down") {
          opStatus = "Link Down";
        } else if (portInfo.operational_status === "Up") {
          opStatus = "Link Up";
        }
        
        setOperationalStatus(prev => ({
          ...prev,
          [portField]: {
            status: opStatus,
            lastChecked: new Date().toISOString(),
            adminStatus: portInfo.admin_status,
            operStatus: portInfo.operational_status
          }
        }));
        
        // Also update the port in the port details list
        setPortDetails(prev => {
          const updated = {...prev};
          updated[portField] = {...updated[portField], ...portInfo};
          return updated;
        });
      }
    } else {
      throw new Error(result.error || "Unknown error refreshing port status");
    }
  } catch (error) {
    console.error(`Error fetching operational status for port ${portId}:`, error);
    setOperationalStatus(prev => ({
      ...prev,
      [portField]: {
        status: "Error",
        lastChecked: new Date().toISOString(),
        error: error.message
      }
    }));
  }
};

// Update refreshAllPortStatus to use our improved approach
const refreshAllPortStatus = async () => {
  if (!localServer?.switch_id) return;
  
  setRefreshingStatus(true);
  
  try {
    const token = localStorage.getItem('admin_token');
    
    // Call refresh_port_status without specific port_id to refresh all ports
    const response = await fetch(`${API_URL}/api_admin_inventory.php?f=refresh_port_status', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        token,
        switch_id: localServer.switch_id
      })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }
    
    const result = await response.json();
    
    if (result.success) {
      // Update port details and operational status for all ports
      const newPortDetails = {...portDetails};
      const newOperationalStatus = {...operationalStatus};
      
      // Check if the server has any ports connected
      for (let i = 1; i <= 4; i++) {
        const portNumField = `port${i}`;
        const portNum = localServer[portNumField];
        
        if (portNum) {
          // Find the port details in the result
          const portInfo = result.ports.find(p => p.port_number === portNum);
          
          if (portInfo) {
            // Update port details
            newPortDetails[portNumField] = portInfo;
            
            // Determine operational status
            let opStatus = "Unknown";
            if (portInfo.status === "Disabled") {
              opStatus = "Administratively Down";
            } else if (portInfo.operational_status === "Down") {
              opStatus = "Link Down";
            } else if (portInfo.operational_status === "Up") {
              opStatus = "Link Up";
            }
            
            // Update operational status
            newOperationalStatus[portNumField] = {
              status: opStatus,
              lastChecked: new Date().toISOString(),
              adminStatus: portInfo.admin_status,
              operStatus: portInfo.operational_status
            };
          }
        }
      }
      
      // Update state
      setPortDetails(newPortDetails);
      setOperationalStatus(newOperationalStatus);
    } else {
      throw new Error(result.error || "Unknown error refreshing port status");
    }
  } catch (error) {
    console.error("Error refreshing all port statuses:", error);
  } finally {
    setRefreshingStatus(false);
  }
};

  // Fetch available ports for a switch
  const fetchAvailablePorts = async (switchId) => {
    if (!switchId) return;
    
    try {
      const token = localStorage.getItem('admin_token');
      
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_switch_ports', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          token,
          switch_id: switchId
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      
      const ports = await response.json();
      // Ensure ports is an array before filtering
      const availablePorts = Array.isArray(ports) ? ports.filter(port => port.status === 'Available') : [];
      setAvailablePorts(availablePorts);
    } catch (error) {
      console.error("Error fetching switch ports:", error);
      setAvailablePorts([]);
    }
  };

  // Toggle port status (enable/disable)
  const togglePortStatus = async (portNumber, action) => {
    // Find the port details for this port number
    let portDetail = null;
    let portField = null;
    
    for (let i = 1; i <= 4; i++) {
      const field = `port${i}`;
      if (localServer[field] === portNumber) {
        portField = field;
        portDetail = portDetails[field];
        break;
      }
    }
    
    if (!portDetail) {
      console.error(`Port details not found for port ${portNumber}`);
      return;
    }
    
    // Set up confirmation dialog
    setPortToToggle(portDetail);
    setToggleAction(action);
    setToggleConfirmOpen(true);
  };
  
// Update the performTogglePort function in NetworkConnections.js to include delayed refreshing

const performTogglePort = async () => {
  if (!portToToggle || !toggleAction) return;
  
  try {
    setToggleInProgress(true);
    const token = localStorage.getItem('admin_token');
    
    const response = await fetch(`${API_URL}/api_admin_inventory.php?f=toggle_port_status', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        token,
        port_id: portToToggle.id,
        action: toggleAction
      })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }
    
    const result = await response.json();
    
    if (result.success) {
      // Update port status in local state
      setPorts(prevPorts => 
        prevPorts.map(p => 
          p.id === portToToggle.id 
            ? {...p, status: result.new_status} 
            : p
        )
      );
      
      // Also update the operational status to indicate it's changing
      const transitionStatus = toggleAction === 'enable' ? 'Link Activating...' : 'Shutting Down...';
      setOperationalStatus(prev => ({
        ...prev,
        [portToToggle.id]: {
          status: transitionStatus,
          lastChecked: new Date().toISOString()
        }
      }));
      
      // Close confirmation dialog if open
      setToggleConfirmOpen(false);
      
      // Trigger data change event to update other components
      const event = new CustomEvent('inventory-data-change', {
        detail: {
          deviceType: 'port',
          switchId: portToToggle.switch_id,
          portId: portToToggle.id,
          timestamp: Date.now(),
          action: 'port-status-changed',
          newStatus: result.new_status
        }
      });
      window.dispatchEvent(event);
      
      // For enable actions, set up a delayed refresh sequence
      if (toggleAction === 'enable') {
        // Schedule multiple refreshes with increasing delays
        const scheduleRefresh = async (delay, attempt = 1, maxAttempts = 3) => {
          if (attempt > maxAttempts) return;
          
          // Set a visual indicator that we're checking
          if (attempt === 1) {
            // Show waiting message on first attempt
            setTimeout(() => {
              setOperationalStatus(prev => ({
                ...prev,
                [portToToggle.id]: {
                  ...prev[portToToggle.id],
                  status: `Checking link (${attempt}/${maxAttempts})...`,
                  lastChecked: new Date().toISOString()
                }
              }));
            }, delay - 500); // Show message just before refresh
          }
          
          // Execute the actual refresh after the delay
          setTimeout(async () => {
            try {
              await fetchPortOperationalStatus(portToToggle.switch_id, portToToggle.id, findPortFieldForId(portToToggle.id));
              
              // Check if port is up after refresh
              const portField = findPortFieldForId(portToToggle.id);
              const currentStatus = operationalStatus[portField]?.status;
              if (currentStatus === "Link Up") {
                // Link is up, no need for further refreshes
                return;
              }
              
              // Schedule next attempt with increased delay if not the last attempt
              if (attempt < maxAttempts) {
                scheduleRefresh(delay * 1.5, attempt + 1, maxAttempts);
              }
            } catch (refreshError) {
              console.error(`Error in delayed refresh attempt ${attempt}:`, refreshError);
              // Still try next attempt if not the last one
              if (attempt < maxAttempts) {
                scheduleRefresh(delay * 1.5, attempt + 1, maxAttempts);
              }
            }
          }, delay);
        };
        
        // Start the sequence with an initial 3-second delay
        scheduleRefresh(3000);
      } else {
        // For disable actions, a single delayed refresh is enough
        setTimeout(() => {
          const portField = findPortFieldForId(portToToggle.id);
          fetchPortOperationalStatus(portToToggle.switch_id, portToToggle.id, portField);
        }, 1500);
      }
    } else {
      throw new Error(result.error || "Unknown error occurred");
    }
  } catch (error) {
    console.error("Error toggling port status:", error);
    alert(`Failed to ${toggleAction} port: ${error.message}`);
  } finally {
    setToggleInProgress(false);
    // Reset toggle state
    setPortToToggle(null);
    setToggleAction(null);
  }
};

  // Helper function to find the port field (port1, port2, etc.) for a given port ID
  const findPortFieldForId = (portId) => {
    for (const [field, details] of Object.entries(portDetails)) {
      if (details && details.id === portId) {
        return field;
      }
    }
    return null;
  };



  // Delete port connection
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [connectionToDelete, setConnectionToDelete] = useState(null);

  const refreshPortData = async () => {
    if (!localServer?.switch_id) return;
    
    try {
      console.log("Explicitly refreshing port data after operation");
      setRefreshingStatus(true);
      
      // 1. Refresh switch data to get latest network configuration
      await fetchSwitchData(localServer.switch_id);
      
      // 2. Fetch updated port details
      await fetchPortDetails(localServer.switch_id);
      
      // 3. Refresh available ports if needed
      await fetchAvailablePorts(localServer.switch_id);
      
      // 4. Refresh operational status for all connected ports
      for (let i = 1; i <= 4; i++) {
        const portField = `port${i}`;
        const portNum = localServer[portField];
        
        if (portNum) {
          const portDetail = portDetails[portField];
          if (portDetail) {
            await fetchPortOperationalStatus(localServer.switch_id, portDetail.id, portField);
          }
        }
      }
      
      // 5. Notify parent component of the refresh
      if (onRefresh) {
        onRefresh();
      }
      
      console.log("Port data refresh completed");
      setRefreshingStatus(false);
    } catch (error) {
      console.error("Error refreshing port data:", error);
      setRefreshingStatus(false);
    }
  };
  


  const deleteConnection = async () => {
    if (!connectionToDelete || !localServer?.id) {
      return;
    }
    
    try {
      setLoading(true);
      const token = localStorage.getItem('admin_token');
      
      // Identify which port number is being deleted to update local state
      const portNumber = connectionToDelete;
      let portField = null;
      let portSpeedField = null;
      
      // Determine which port field is being removed
      if (localServer.port1 === portNumber) {
        portField = 'port1';
        portSpeedField = 'port1_speed';
      } else if (localServer.port2 === portNumber) {
        portField = 'port2';
        portSpeedField = 'port2_speed';
      } else if (localServer.port3 === portNumber) {
        portField = 'port3';
        portSpeedField = 'port3_speed';
      } else if (localServer.port4 === portNumber) {
        portField = 'port4';
        portSpeedField = 'port4_speed';
      }
      
      // Store switch ID before potentially clearing it
      const currentSwitchId = localServer.switch_id;
      
      // Make the API call
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=unassign_port_from_server', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          token,
          device_id: localServer.id,
          device_type: serverType,
          port_number: connectionToDelete
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result.success) {
        // Update local server state to reflect changes immediately
        const updatedServer = { ...localServer };
        
        if (portField) {
          updatedServer[portField] = null;
          updatedServer[portSpeedField] = null;
          
          // If this was the only port, also clear the switch_id
          const hasOtherPorts = (
            (updatedServer.port1 && portField !== 'port1') || 
            (updatedServer.port2 && portField !== 'port2') || 
            (updatedServer.port3 && portField !== 'port3') || 
            (updatedServer.port4 && portField !== 'port4')
          );
          
          if (!hasOtherPorts) {
            updatedServer.switch_id = null;
            setSwitchData(null);
          }
          
          // Update port details
          const updatedPortDetails = { ...portDetails };
          delete updatedPortDetails[portField];
          setPortDetails(updatedPortDetails);
          
          // Clear operational status for this port
          setOperationalStatus(prev => {
            const updated = {...prev};
            delete updated[portField];
            return updated;
          });
          
          // Update local state
          setLocalServer(updatedServer);
        }
        
        // Close confirm dialog and reset
        setDeleteConfirmOpen(false);
        setConnectionToDelete(null);
        
        // Explicitly refresh port data after a short delay to ensure backend processing is complete
        setTimeout(() => {
          refreshPortData();
        }, 300);
        
        // Notify other components with additional data
        const event = new CustomEvent('inventory-data-change', {
          detail: {
            timestamp: new Date().toISOString(),
            deviceType: serverType,
            deviceId: localServer.id,
            switchId: currentSwitchId,
            portNumber: connectionToDelete,
            action: 'port-unassignment',
            isLastPort: updatedServer.switch_id === null
          }
        });
        window.dispatchEvent(event);
      } else {
        setError(result.error || "Failed to remove connection");
      }
      
      setLoading(false);
    } catch (error) {
      console.error("Error removing connection:", error);
      setError("Failed to remove connection: " + error.message);
      setLoading(false);
    }
  };

  const assignPort = async () => {
    if (!selectedPort || !localServer?.id || !selectedSwitch) {
      if (!selectedSwitch) {
        setError("Please select a switch first");
      } else if (!selectedPort) {
        setError("Please select a port");
      }
      return;
    }
    
    try {
      setLoading(true);
      const token = localStorage.getItem('admin_token');
      
      // Find the selected port details from availablePorts
      const selectedPortDetails = availablePorts.find(p => p.id === parseInt(selectedPort));
      
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=assign_port_to_server_modal', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          token,
          port_id: selectedPort,
          device_id: localServer.id,
          device_type: serverType,
          switch_id: selectedSwitch
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result.success) {
        // Now update the local server state to reflect changes immediately
        const portField = result.port_field; // This comes from the API
        const portSpeedField = `${portField}_speed`;
        
        // Create a copy of the server with updated values
        const updatedServer = {
          ...localServer,
          switch_id: selectedSwitch,
          [portField]: selectedPortDetails.port_number,
          [portSpeedField]: selectedPortDetails.max_speed || 0
        };
        
        // Update local state to reflect changes
        setLocalServer(updatedServer);
        
        // Close modal and reset
        setAssignModalOpen(false);
        setSelectedPort(null);
        
        // Explicitly refresh port data after a short delay to ensure backend processing is complete
        setTimeout(() => {
          refreshPortData();
        }, 300);
        
        // Notify other components
        const event = new CustomEvent('inventory-data-change', {
          detail: {
            timestamp: new Date().toISOString(),
            deviceType: serverType,
            deviceId: localServer.id,
            switchId: selectedSwitch,
            portId: selectedPort,
            action: 'port-assignment'
          }
        });
        window.dispatchEvent(event);
      } else {
        setError(result.error || "Failed to assign port");
      }
      
      setLoading(false);
    } catch (error) {
      console.error("Error assigning port:", error);
      setError("Failed to assign port: " + error.message);
      setLoading(false);
    }
  };

  // Helper function to render operational status icon and text
  const renderOperationalStatus = (portField) => {
    const status = operationalStatus[portField]?.status || "Unknown";
    const lastChecked = operationalStatus[portField]?.lastChecked;
    
    let statusIcon;
    let statusClass;
    
    switch(status) {
      case "Link Up":
        statusIcon = <ArrowUpCircle className="w-3 h-3 mr-1 text-green-600" />;
        statusClass = "text-green-700 bg-green-50";
        break;
      case "Link Down":
        statusIcon = <ArrowDownCircle className="w-3 h-3 mr-1 text-red-600" />;
        statusClass = "text-red-700 bg-red-50";
        break;
      case "Administratively Down":
        statusIcon = <Power className="w-3 h-3 mr-1 text-gray-600" />;
        statusClass = "text-gray-700 bg-gray-100";
        break;
      case "Unknown":
        statusIcon = <AlertTriangle className="w-3 h-3 mr-1 text-yellow-600" />;
        statusClass = "text-yellow-700 bg-yellow-50";
        break;
      case "Error":
        statusIcon = <XCircle className="w-3 h-3 mr-1 text-red-600" />;
        statusClass = "text-red-700 bg-red-50";
        break;
      default:
        statusIcon = <AlertTriangle className="w-3 h-3 mr-1 text-yellow-600" />;
        statusClass = "text-yellow-700 bg-yellow-50";
    }
    
    return (
      <div className={`text-xs px-2 py-1 rounded-full inline-flex items-center my-1 ${statusClass}`}>
        {statusIcon}
        <span>{status}</span>
      </div>
    );
  };

  // Render delete confirmation dialog
  const renderDeleteConfirmation = () => {
    if (!deleteConfirmOpen) return null;
    
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-lg w-full max-w-sm p-3">
          <h3 className="text-sm font-bold mb-2">Remove Network Connection</h3>
          
          <div className="mb-3 text-xs">
            Are you sure you want to remove the connection to port {connectionToDelete}?
          </div>
          
          {error && (
            <div className="mb-2 p-1 bg-red-50 text-red-600 rounded text-xs flex items-center">
              <AlertTriangle className="w-3 h-3 mr-1" />
              {error}
            </div>
          )}
          
          <div className="flex justify-end space-x-2">
            <button
              onClick={() => {
                setDeleteConfirmOpen(false);
                setConnectionToDelete(null);
                setError(null);
              }}
              className="px-2 py-1 text-xs bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
            >
              Cancel
            </button>
            <button
              onClick={deleteConnection}
              disabled={loading}
              className={`px-2 py-1 text-xs rounded flex items-center ${
                loading
                  ? 'bg-red-300 cursor-not-allowed'
                  : 'bg-red-600 hover:bg-red-700 text-white'
              }`}
            >
              {loading ? (
                <>
                  <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                  Removing...
                </>
              ) : (
                <>
                  <XCircle className="w-3 h-3 mr-1" />
                  Remove
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Port toggle confirmation dialog
  const renderToggleConfirmation = () => {
    if (!toggleConfirmOpen || !portToToggle || !toggleAction) return null;
    
    const action = toggleAction === 'enable' ? 'enable' : 'disable';
    
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-lg w-full max-w-sm p-3">
          <h3 className="text-sm font-bold mb-2">{action.charAt(0).toUpperCase() + action.slice(1)} Port</h3>
          
          <div className="mb-3 text-xs">
            Are you sure you want to {action} port {portToToggle.port_number}?
            {action === 'disable' && ' This will interrupt network connectivity.'}
          </div>
          
          {error && (
            <div className="mb-2 p-1 bg-red-50 text-red-600 rounded text-xs flex items-center">
              <AlertTriangle className="w-3 h-3 mr-1" />
              {error}
            </div>
          )}
          
          <div className="flex justify-end space-x-2">
            <button
              onClick={() => {
                setToggleConfirmOpen(false);
                setPortToToggle(null);
                setToggleAction(null);
                setError(null);
              }}
              className="px-2 py-1 text-xs bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
            >
              Cancel
            </button>
            <button
              onClick={performTogglePort}
              disabled={toggleInProgress}
              className={`px-2 py-1 text-xs rounded flex items-center ${
                toggleInProgress
                  ? 'bg-indigo-300 cursor-not-allowed'
                  : (toggleAction === 'enable' ? 'bg-green-600' : 'bg-gray-600') + ' hover:bg-indigo-700 text-white'
              }`}
            >
              {toggleInProgress ? (
                <>
                  <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Power className="w-3 h-3 mr-1" />
                  {action.charAt(0).toUpperCase() + action.slice(1)}
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Render port connection - ENHANCED VERSION WITH OPERATIONAL STATUS
  const renderPortConnection = (portNumber, portSpeed, index) => {
    if (!portNumber) return null;
    
    // Get port details if available
    const portField = `port${index}`;
    const portDetail = portDetails[portField] || null;
    const isDisabled = portDetail && portDetail.status === 'Disabled';
    
    const speedDisplay = portSpeed ? `${portSpeed} Mbps` : 'Unknown';
    
    return (
      <div key={`port-${index}`} className={`bg-white border border-gray-200 rounded-md shadow-sm mb-1.5 overflow-hidden ${isDisabled ? 'opacity-60' : ''}`}>
        <div className={`${isDisabled ? 'bg-gray-50' : 'bg-indigo-50'} px-2 py-0.5 flex justify-between items-center border-b ${isDisabled ? 'border-gray-200' : 'border-indigo-100'}`}>
          <div className="flex items-center text-xs">
            <Network className={`w-3 h-3 mr-1 ${isDisabled ? 'text-gray-500' : 'text-indigo-600'}`} />
            <span className={`font-medium ${isDisabled ? 'text-gray-600' : 'text-indigo-700'}`}>
              Connection {index} {isDisabled && '(Disabled)'}
            </span>
          </div>
          <div className="flex items-center">
            <div className={`text-xs px-1 py-0.5 rounded mr-1 ${isDisabled ? 'bg-gray-100 text-gray-700' : 'bg-indigo-100 text-indigo-800'}`}>
              {speedDisplay}
            </div>
            
            {/* Add enable/disable button */}
            {editable && portDetail && (
              <button 
                onClick={() => {
                  togglePortStatus(portNumber, isDisabled ? 'enable' : 'disable');
                }}
                className={`text-xs p-0.5 rounded-full mr-1 ${
                  isDisabled ? 
                  'text-green-500 hover:text-green-700 hover:bg-green-50' : 
                  'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
                title={isDisabled ? "Enable port" : "Disable port"}
              >
                <Power className="w-3 h-3" />
              </button>
            )}
            
            {/* Delete button shown in edit mode or when editable is true */}
            {editable && (
              <button 
                onClick={() => {
                  setConnectionToDelete(portNumber);
                  setDeleteConfirmOpen(true);
                  setError(null);
                }}
                className="text-xs text-red-500 hover:text-red-700 p-0.5 rounded-full hover:bg-red-50"
                title="Remove connection"
              >
                <XCircle className="w-3 h-3" />
              </button>
            )}
          </div>
        </div>
        
        <div className="flex flex-col text-xs px-2 py-1">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <Wifi className="w-3 h-3 mr-1 text-gray-500" />
              <span className="text-gray-500">NIC {index}</span>
            </div>
            
            {switchData ? (
    <div className="flex items-center">
      <Network className={`w-3 h-3 mr-1 ${isDisabled ? 'text-gray-400' : 'text-gray-600'}`} />
      <span className={isDisabled ? 'text-gray-500' : 'text-gray-700'}>
  <a href={`/admin/inventory/switch/${switchData.id}`} className="text-blue-600 hover:underline">
    {switchData.label}
  </a> 
  {' '}Port {portNumber && portNumber.toString().includes('Ethernet') ? portNumber : `Ethernet${portNumber}`}
</span>
    </div>
  ) : (
    <span className="text-gray-500 text-xs italic">Loading switch info...</span>
  )}
          </div>
          
          {/* Operational Status Display */}
          <div className="flex justify-between items-center mt-1">
            <div>
              {renderOperationalStatus(portField)}
            </div>
            
            <button 
              onClick={() => fetchPortOperationalStatus(switchData?.id, portDetail?.id, portField)}
              disabled={refreshingStatus}
              className="text-xs p-0.5 rounded-full text-gray-500 hover:text-indigo-700 hover:bg-indigo-50"
              title="Refresh port status"
            >
              <RefreshCw className={`w-3 h-3 ${refreshingStatus ? 'animate-spin' : ''}`} />
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Render the modal for port assignment with switch selection
const renderAssignPortModal = () => {
  if (!assignModalOpen) return null;
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-sm p-3">
        <h3 className="text-sm font-bold mb-2">Add Network Connection</h3>
        
        {/* Switch Selection - only show if server doesn't have a switch assigned */}
        {!localServer?.switch_id && (
          <div className="mb-2">
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Select Switch
            </label>
            <select
              value={selectedSwitch || ''}
              onChange={(e) => {
                setSelectedSwitch(e.target.value);
                if (e.target.value) {
                  fetchAvailablePorts(e.target.value);
                } else {
                  setAvailablePorts([]);
                }
              }}
              className="w-full text-xs border border-gray-300 rounded p-1.5"
            >
              <option value="">Select a switch...</option>
              {switches.map((sw) => (
                <option key={sw.id} value={sw.id}>
                  {sw.label}
                </option>
              ))}
            </select>
          </div>
        )}
        
        {/* Port Selection */}
        <div className="mb-3">
          <label className="block text-xs font-medium text-gray-700 mb-1">
            Select Port
          </label>
          <select
            value={selectedPort || ''}
            onChange={(e) => setSelectedPort(e.target.value)}
            className="w-full text-xs border border-gray-300 rounded p-1.5"
            disabled={!selectedSwitch || availablePorts.length === 0}
          >
            <option value="">Select a port...</option>
            {availablePorts.map((port) => (
              <option key={port.id} value={port.id}>
                Port {port.port_number} ({port.max_speed} Mbps)
              </option>
            ))}
          </select>
          
          {selectedSwitch && availablePorts.length === 0 && (
            <p className="text-xs text-red-600 mt-1">
              No available ports found on this switch.
            </p>
          )}
        </div>
        
        {error && (
          <div className="mb-2 p-1 bg-red-50 text-red-600 rounded text-xs flex items-center">
            <AlertTriangle className="w-3 h-3 mr-1" />
            {error}
          </div>
        )}
        
        <div className="flex justify-end space-x-2">
          <button
            onClick={() => {
              setAssignModalOpen(false);
              setSelectedPort(null);
              setError(null);
            }}
            className="px-2 py-1 text-xs bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
          >
            Cancel
          </button>
          <button
            onClick={assignPort}
            disabled={loading || !selectedPort}
            className={`px-2 py-1 text-xs rounded flex items-center ${
              loading || !selectedPort
                ? 'bg-indigo-300 cursor-not-allowed'
                : 'bg-indigo-600 hover:bg-indigo-700 text-white'
            }`}
          >
            {loading ? (
              <>
                <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                Assigning...
              </>
            ) : (
              <>
                <CheckCircle className="w-3 h-3 mr-1" />
                Assign Port
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

  // Listen for inventory data changes from other components
  useEffect(() => {
    const handleInventoryDataChange = (event) => {
      const { deviceType, action, portId } = event.detail;
      
      // If this is a port status change event
      if (deviceType === 'port' && action === 'port-status-changed') {
        // Refresh port details if this affects our server
        if (localServer?.switch_id) {
          fetchPortDetails(localServer.switch_id);
        }
      }
    };
    
    window.addEventListener('inventory-data-change', handleInventoryDataChange);
    
    return () => {
      window.removeEventListener('inventory-data-change', handleInventoryDataChange);
    };
  }, [localServer?.switch_id]);

  // Main render
  return (
    <div className="mb-2">
      <div className="flex justify-between items-center mb-1.5">
        <div className="flex items-center">
          <Network className="w-3 h-3 mr-1 text-indigo-700" />
          <h4 className="text-xs font-medium text-gray-700">Network Connections</h4>
        </div>
        <div className="flex items-center">
          {/* Refresh port status button */}
          <button
            onClick={refreshAllPortStatus}
            disabled={refreshingStatus}
            className={`mr-2 flex items-center text-xs px-1.5 py-0.5 rounded 
              ${refreshingStatus ? 'bg-gray-100 text-gray-500' : 'bg-blue-50 hover:bg-blue-100 text-blue-600'}`}
            title="Refresh all port statuses"
          >
            <RefreshCw className={`w-2.5 h-2.5 mr-0.5 ${refreshingStatus ? 'animate-spin' : ''}`} />
            {refreshingStatus ? 'Refreshing...' : 'Refresh Status'}
          </button>
          
          {editable && (
            <button
              onClick={() => {
                setError(null);
                setAssignModalOpen(true);
                if (!localServer?.switch_id) {
                  // If no switch assigned, fetch available switches
                  fetchSwitches();
                } else {
                  // Use existing switch
                  setSelectedSwitch(localServer.switch_id);
                  fetchAvailablePorts(localServer.switch_id);
                }
              }}
              className="flex items-center text-xs px-1.5 py-0.5 rounded bg-indigo-50 hover:bg-indigo-100 text-indigo-600"
            >
              <Plus className="w-2.5 h-2.5 mr-0.5" />
              Add
            </button>
          )}
        </div>
      </div>
      
      {loading && !assignModalOpen && !deleteConfirmOpen && !toggleConfirmOpen && (
        <div className="flex justify-center p-1.5 bg-gray-50 rounded-md border border-gray-200 text-xs">
          <RefreshCw className="w-3 h-3 animate-spin text-indigo-600 mr-1" />
          <span className="text-gray-600">Loading...</span>
        </div>
      )}
      
      {!loading && (
        <>
          {/* Display port connections */}
          {localServer && (localServer.port1 || localServer.port2 || localServer.port3 || localServer.port4) ? (
            <div>
              {localServer.port1 && renderPortConnection(localServer.port1, localServer.port1_speed, 1)}
              {localServer.port2 && renderPortConnection(localServer.port2, localServer.port2_speed, 2)}
              {localServer.port3 && renderPortConnection(localServer.port3, localServer.port3_speed, 3)}
              {localServer.port4 && renderPortConnection(localServer.port4, localServer.port4_speed, 4)}
            </div>
          ) : (
            <div className="p-1.5 bg-gray-50 border border-gray-200 rounded-md text-xs text-gray-500 flex items-center justify-center">
              <XCircle className="w-3 h-3 mr-1 text-gray-400" />
              No connections
            </div>
          )}
          

        </>
      )}
      
      {/* Port assignment modal */}
      {renderAssignPortModal()}
      
      {/* Delete confirmation modal */}
      {renderDeleteConfirmation()}
      
      {/* Toggle confirmation modal */}
      {renderToggleConfirmation()}
    </div>
  );
};

export default NetworkConnections;