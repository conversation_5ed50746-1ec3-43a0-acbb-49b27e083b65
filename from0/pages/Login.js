import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { AtSign, Lock, AlertCircle, Server } from 'lucide-react';
import { useAuth } from '../AuthContext'; // Adjust the import path based on your file structure

const Login = ({ navigateTo }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [errorMessage, setErrorMessage] = useState('Enter your credentials to access admin panel');
  const [isErrorState, setIsErrorState] = useState(false);
  const [loginAttempt, setLoginAttempt] = useState(0);
  
  // Use the auth context
  const { login, isLoading, error: authError, isAuthenticated } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const from = location.state?.from?.pathname || '/admin/dashboard';

  // Check if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      // Redirect to intended destination if already authenticated
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, from]);

  // Update error message if auth context reports an error
  useEffect(() => {
    if (authError && loginAttempt > 0) {
      setErrorMessage(authError);
      setIsErrorState(true);
    }
  }, [authError, loginAttempt]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoginAttempt(prev => prev + 1);

    if (!username || !password) {
      setErrorMessage('Please enter both email and password');
      setIsErrorState(true);
      return;
    }

    setIsErrorState(false);
    setErrorMessage('Signing in...');
    
    try {
      console.log('Attempting login with:', username);
      const result = await login(username, password);
      
      if (result.success) {
        console.log('Login successful, redirecting to:', from);
        // Small delay to ensure token is saved before navigation
        setTimeout(() => {
          navigate(from, { replace: true });
        }, 100);
      } else {
        setErrorMessage(result.message || 'Invalid admin credentials');
        setIsErrorState(true);
      }
    } catch (error) {
      console.error('Login error:', error);
      setErrorMessage(`Server error: ${error.message || 'Unable to connect'}`);
      setIsErrorState(true);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 to-indigo-900 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-full opacity-10">
          {/* Animated lines */}
          <svg width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="none">
            <line x1="0" y1="0" x2="100" y2="100" stroke="white" strokeWidth="0.2" className="animate-pulse">
              <animate attributeName="y1" values="0;100;0" dur="20s" repeatCount="indefinite" />
            </line>
            <line x1="20" y1="0" x2="100" y2="80" stroke="white" strokeWidth="0.2" className="animate-pulse">
              <animate attributeName="y1" values="20;100;20" dur="18s" repeatCount="indefinite" />
            </line>
            <line x1="40" y1="0" x2="100" y2="60" stroke="white" strokeWidth="0.2" className="animate-pulse">
              <animate attributeName="y1" values="40;100;40" dur="15s" repeatCount="indefinite" />
            </line>
            <line x1="60" y1="0" x2="100" y2="40" stroke="white" strokeWidth="0.2" className="animate-pulse">
              <animate attributeName="y1" values="60;100;60" dur="12s" repeatCount="indefinite" />
            </line>
            <line x1="80" y1="0" x2="100" y2="20" stroke="white" strokeWidth="0.2" className="animate-pulse">
              <animate attributeName="y1" values="80;100;80" dur="10s" repeatCount="indefinite" />
            </line>
          </svg>
        </div>
      </div>

      <div className="w-full max-w-4xl relative z-10">
        <div className="flex rounded-lg shadow-2xl overflow-hidden bg-opacity-90 backdrop-blur-sm">
          <div className="w-full md:w-1/2 bg-white p-8 relative">
            <div className="mb-8">
              <h1 className="text-2xl font-bold text-gray-800 mb-2">Admin Login</h1>
              
              {isErrorState ? (
                <div className="flex items-center text-red-600 text-sm mb-4">
                  <AlertCircle className="h-4 w-4 mr-2" />
                  <p>{errorMessage}</p>
                </div>
              ) : (
                <p className="text-sm text-gray-500">{errorMessage}</p>
              )}
            </div>
            
            <form onSubmit={handleSubmit}>
              <div className="mb-6">
                <label className="block text-gray-700 text-sm font-medium mb-2" htmlFor="email">
                  Admin Email
                </label>
                <div className="relative overflow-hidden group">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none z-10">
                    <AtSign className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="email"
                    type="email"
                    className={`w-full pl-10 pr-3 py-2 border ${isErrorState ? 'border-red-300' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all duration-300 relative z-0`}
                    placeholder="<EMAIL>"
                    value={username}
                    onChange={(e) => {
                      setUsername(e.target.value);
                      setIsErrorState(false);
                    }}
                    autoComplete="email"
                  />
                  <div className="absolute bottom-0 left-0 h-0.5 bg-indigo-600 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
                </div>
              </div>
              
              <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-gray-700 text-sm font-medium" htmlFor="password">
                    Password
                  </label>
                </div>
                <div className="relative overflow-hidden group">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none z-10">
                    <Lock className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="password"
                    type="password"
                    className={`w-full pl-10 pr-3 py-2 border ${isErrorState ? 'border-red-300' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all duration-300 relative z-0`}
                    placeholder="••••••••"
                    value={password}
                    onChange={(e) => {
                      setPassword(e.target.value);
                      setIsErrorState(false);
                    }}
                    autoComplete="current-password"
                  />
                  <div className="absolute bottom-0 left-0 h-0.5 bg-indigo-600 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
                </div>
              </div>
              
              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-indigo-700 hover:bg-indigo-800 text-white font-medium py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-lg"
              >
                {isLoading ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Signing in...
                  </span>
                ) : 'Admin Sign In'}
              </button>
            </form>
          </div>
          
          <div className="hidden md:block md:w-1/2 bg-indigo-700 p-8 relative">
            <div className="flex flex-col items-center justify-center h-full relative z-10">
              <div className="mb-6 text-white flex items-center">
                <Server className="h-10 w-10 mr-3" />
                <span className="text-3xl font-bold">ZetServers</span>
              </div>
              <div className="text-white text-center">
                <h2 className="text-xl font-semibold mb-2">Admin Dashboard</h2>
                <p className="text-indigo-200">
                  Secure management portal for administrators only.
                </p>
              </div>
            </div>
            
            {/* Animated background elements for the right panel */}
            <div className="absolute inset-0 overflow-hidden">
              <div className="grid grid-cols-8 grid-rows-8 gap-2 w-full h-full opacity-20">
                {[...Array(40)].map((_, index) => (
                  <div 
                    key={index} 
                    className="bg-white rounded-full h-1 w-1 animate-pulse"
                    style={{
                      animationDelay: `${index * 0.1}s`,
                      position: 'absolute',
                      top: `${Math.random() * 100}%`,
                      left: `${Math.random() * 100}%`
                    }}
                  ></div>
                ))}
              </div>
              <svg className="absolute bottom-0 left-0 w-full" viewBox="0 0 100 20" preserveAspectRatio="none">
                <path 
                  d="M0,10 Q25,0 50,10 T100,10 V20 H0 Z" 
                  fill="rgba(255,255,255,0.1)"
                >
                  <animate attributeName="d" 
                    values="M0,10 Q25,0 50,10 T100,10 V20 H0 Z;
                            M0,15 Q25,5 50,15 T100,15 V20 H0 Z;
                            M0,10 Q25,0 50,10 T100,10 V20 H0 Z" 
                    dur="15s" 
                    repeatCount="indefinite" />
                </path>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;