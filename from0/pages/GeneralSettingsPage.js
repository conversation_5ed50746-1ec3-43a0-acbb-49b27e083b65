import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import axios from 'axios';
import { Save, Loader } from 'lucide-react';
import Sidebar from '../components/Sidebar';
import TopMenu from '../components/TopMenu';
import { API_URL } from '../config';

const GeneralSettingsPage = ({ sidebarCollapsed, toggleSidebar }) => {
  const [settings, setSettings] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Fetch settings on component mount
  useEffect(() => {
    fetchSettings();
  }, []);

  // Fetch settings from the server
  const fetchSettings = async () => {
    setIsLoading(true);
    try {
      const token = localStorage.getItem('admin_token');

      const response = await axios.post(`${API_URL}/api_admin_general_settings.php?f=get_general_settings`, {
        token: token
      });

      if (response.data.success) {
        setSettings(response.data.settings);
      } else {
        toast.error(response.data.error || 'Failed to load settings');
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
      toast.error('Failed to load settings');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle setting input changes
  const handleSettingChange = (id, value) => {
    setSettings(prevSettings => 
      prevSettings.map(setting => 
        setting.id === id ? { ...setting, setting_value: value } : setting
      )
    );
  };

  // Save settings
  const saveSettings = async () => {
    setIsSaving(true);
    try {
      const token = localStorage.getItem('admin_token');

      const response = await axios.post(`${API_URL}/api_admin_general_settings.php?f=save_general_settings`, {
        token: token,
        settings: settings
      });

      if (response.data.success) {
        toast.success('Settings saved successfully');
        fetchSettings(); // Refresh settings
      } else {
        toast.error(response.data.error || 'Failed to save settings');
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Failed to save settings');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <Sidebar
        collapsed={sidebarCollapsed}
        activeMenu="Settings"
        toggleSidebar={toggleSidebar}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Menu */}
        <TopMenu collapsed={sidebarCollapsed} toggleSidebar={toggleSidebar} />

        {/* Main Content Area */}
        <main className="flex-1 overflow-y-auto bg-gray-100 p-4">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white shadow-md rounded-lg overflow-hidden">
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h1 className="text-xl font-semibold text-gray-800">General Settings</h1>
                  <button
                    onClick={saveSettings}
                    disabled={isSaving}
                    className="px-4 py-2 bg-indigo-600 text-white rounded-md flex items-center hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                  >
                    {isSaving ? (
                      <>
                        <Loader className="w-4 h-4 mr-2 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4 mr-2" />
                        Save Settings
                      </>
                    )}
                  </button>
                </div>

                {isLoading ? (
                  <div className="flex justify-center items-center h-64">
                    <Loader className="w-8 h-8 text-indigo-600 animate-spin" />
                  </div>
                ) : (
                  <div className="space-y-6">
                    {settings.map(setting => (
                      <div key={setting.id} className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">
                          {setting.description || setting.setting_key}
                        </label>
                        <input
                          type="text"
                          value={setting.setting_value}
                          onChange={(e) => handleSettingChange(setting.id, e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                        />
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default GeneralSettingsPage;
