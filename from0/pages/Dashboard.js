import React, { useState, useEffect } from 'react';
import {
  Plus,
  Search,
  Filter,
  XCircle,
  RefreshCw,
  ArrowUp,
  ArrowDown,
  FileText,
  CheckCircle,
  Clock,
  AlertCircle,
  DollarSign,
  CreditCard,
  Eye,
  MoreVertical,
  MessageSquare,
  Ban,
  Edit,
  Trash2,
  User,
  Calendar
} from 'lucide-react';
import Sidebar from '../components/Sidebar';
import TopMenu from '../components/TopMenu';
import MetricCards from '../components/MetricCards';

const Dashboard = ({ navigateTo, sidebarCollapsed, toggleSidebar }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('All');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedTask, setSelectedTask] = useState(null);
  const [addTaskModalOpen, setAddTaskModalOpen] = useState(false);
  const [sortField, setSortField] = useState('id');
  const [sortDirection, setSortDirection] = useState('desc');
  const [staffMembers, setStaffMembers] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [newTask, setNewTask] = useState({
    description: '',
    department_id: '',
    category: 'Other',
    status: 'Pending',
    admin_id: '',
    content: ''
  });

  // Task editing state
  const [editMode, setEditMode] = useState(false);
  const [editedTask, setEditedTask] = useState(null);

  // Task messages state
  const [taskMessages, setTaskMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loadingMessages, setLoadingMessages] = useState(false);

  // Loading states for better UX
  const [updating, setUpdating] = useState(false);

  // State for data from backend
  const [tasks, setTasks] = useState([]);
  const [recentActivity, setRecentActivity] = useState([]);
  const [recentOrders, setRecentOrders] = useState([]);
  const [lastUpdated, setLastUpdated] = useState('');
  const [taskStats, setTaskStats] = useState({
    total: 0,
    inProgress: 0,
    pending: 0,
    completed: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [timeFilter, setTimeFilter] = useState('today');

  // Get unique categories and statuses from tasks
  const uniqueCategories = ['All', ...new Set(tasks.map(task => task.category).filter(Boolean))];
  const uniqueStatuses = ['All', ...new Set(tasks.map(task => task.status).filter(Boolean))];

  useEffect(() => {
    // Initialize activity log table on component mount
    fetch(`/api_admin_dashboard.php?f=init_activity_log`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: localStorage.getItem('admin_token')
      })
    })
    .catch(err => console.error("Error initializing activity log:", err));

    // Load all initial data
    Promise.all([
      fetchData(),
      fetchStaffMembers(),
      fetchDepartments()
    ]).catch(err => {
      console.error("Error loading initial data:", err);
    });
  }, []);

  // Reset the new task form with default values
  const resetNewTaskForm = () => {
    setNewTask({
      description: '',
      department_id: departments.length > 0 ? departments[0].id : '',
      category: 'Network Configuration',
      status: 'Pending',
      admin_id: '',
      content: ''
    });
  };

  // Fetch all dashboard data
  const fetchData = async () => {
    setLoading(true);
    setError(null);

    try {
      await Promise.all([
        fetchTasks(),
        fetchRecentOrders(),
        fetchRecentActivity(),
        fetchTaskStats()
      ]);

      setLastUpdated(new Date().toLocaleTimeString());
      setLoading(false);
    } catch (err) {
      console.error("Error fetching dashboard data:", err);
      setError("Failed to load dashboard data. Please try again.");
      setLoading(false);
    }
  };

  // Fetch tasks with filtering and sorting
  const fetchTasks = async () => {
    let url = `/api_admin_dashboard.php?f=get_dashboard_tasks&sortField=${sortField}&sortDirection=${sortDirection}`;

    if (selectedStatus !== 'All') {
      url += `&status=${selectedStatus}`;
    }

    if (selectedCategory !== 'All') {
      url += `&category=${selectedCategory}`;
    }

    if (searchQuery) {
      url += `&search=${searchQuery}`;
    }

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();
      setTasks(data);
    } catch (err) {
      console.error("Error fetching tasks:", err);
      throw err;
    }
  };

  // Fetch staff members
  const fetchStaffMembers = async () => {
    try {
      const response = await fetch(`/api_admin_dashboard.php?f=get_staff_members`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token: localStorage.getItem('admin_token') })
      });

      if (!response.ok) throw new Error(`HTTP error ${response.status}`);

      const data = await response.json();
      setStaffMembers(data);
      return data;
    } catch (err) {
      console.error("Error fetching staff members:", err);
      return [];
    }
  };

  // Fetch departments
  const fetchDepartments = async () => {
    try {
      const response = await fetch(`/api_admin_dashboard.php?f=get_departments`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token: localStorage.getItem('admin_token') })
      });

      if (!response.ok) throw new Error(`HTTP error ${response.status}`);

      const data = await response.json();
      setDepartments(data);
      return data;
    } catch (err) {
      console.error("Error fetching departments:", err);
      return [];
    }
  };

  // Fetch recent orders
  const fetchRecentOrders = async () => {
    try {
      const response = await fetch(`/api_admin_dashboard.php?f=get_recent_orders`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();
      setRecentOrders(data);
    } catch (err) {
      console.error("Error fetching recent orders:", err);
      throw err;
    }
  };

  // Fetch recent activity
  const fetchRecentActivity = async () => {
    try {
      const response = await fetch(`/api_admin_dashboard.php?f=get_recent_activity&timeFilter=${timeFilter}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();
      setRecentActivity(data);
    } catch (err) {
      console.error("Error fetching recent activity:", err);
      throw err;
    }
  };

  // Fetch task statistics
  const fetchTaskStats = async () => {
    try {
      const response = await fetch(`/api_admin_dashboard.php?f=get_task_stats`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();
      setTaskStats(data);
    } catch (err) {
      console.error("Error fetching task stats:", err);
      throw err;
    }
  };

  // OPTIMIZATION: Fast refresh function for when you do need to refresh
  const handleQuickRefresh = async () => {
    try {
      // Only fetch tasks and stats, skip orders and activity
      await Promise.all([
        fetchTasks(),
        fetchTaskStats()
      ]);
      setLastUpdated(new Date().toLocaleTimeString());
    } catch (err) {
      console.error("Error refreshing:", err);
    }
  };

  // OPTIMIZATION: Helper function to update task in the list without full refresh
  const updateTaskInList = (updatedTask) => {
    setTasks(prevTasks => 
      prevTasks.map(task => 
        task.id === updatedTask.id ? {
          ...task,
          description: updatedTask.description,
          department_id: updatedTask.department_id,
          department: getDepartmentName(updatedTask.department_id),
          category: updatedTask.category,
          status: updatedTask.status,
          admin_id: updatedTask.admin_id,
          assignedTo: getStaffName(updatedTask.admin_id),
          content: updatedTask.content
        } : task
      )
    );
  };

  // OPTIMIZATION: Helper function to update selected task display
  const updateSelectedTaskDisplay = (updatedTask) => {
    const department = departments.find(d => d.id === updatedTask.department_id);
    const staff = staffMembers.find(s => s.id === updatedTask.admin_id);

    setSelectedTask({
      ...selectedTask,
      description: updatedTask.description,
      department_id: updatedTask.department_id,
      department: department ? department.name : 'General',
      category: updatedTask.category,
      status: updatedTask.status,
      admin_id: updatedTask.admin_id,
      assignedTo: staff ? staff.name : 'Unassigned',
      content: updatedTask.content
    });
  };

  // Fetch task messages
  const fetchTaskMessages = async (taskId) => {
    if (!taskId) return;

    setLoadingMessages(true);
    try {
      const response = await fetch(`/api_admin_dashboard.php?f=get_task_messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          task_id: taskId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setTaskMessages(data.messages || []);
      } else {
        console.error("Failed to fetch task messages:", data.error);
      }
    } catch (err) {
      console.error("Error fetching task messages:", err);
    } finally {
      setLoadingMessages(false);
    }
  };

  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleStatusFilter = (status) => {
    setSelectedStatus(status);
  };

  const handleCategoryFilter = (category) => {
    setSelectedCategory(category);
  };

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // OPTIMIZED: Use quick refresh instead of full refresh
  const handleRefreshData = () => {
    handleQuickRefresh();
  };

  const getSortIcon = (field) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ?
      <ArrowUp className="w-4 h-4 inline ml-1" /> :
      <ArrowDown className="w-4 h-4 inline ml-1" />;
  };

  const handleTaskClick = (task) => {
    setSelectedTask(task);
    fetchTaskMessages(task.id);
  };

  const closeTaskDetails = () => {
    setSelectedTask(null);
    setEditMode(false);
    setEditedTask(null);
    setTaskMessages([]);
    setNewMessage('');
  };

  const openAddTaskModal = () => {
    // Set initial department_id if departments are loaded
    if (departments.length > 0 && !newTask.department_id) {
      setNewTask(prev => ({
        ...prev,
        department_id: departments[0].id
      }));
    }
    setAddTaskModalOpen(true);
  };

  const closeAddTaskModal = () => {
    setAddTaskModalOpen(false);
    resetNewTaskForm();
  };

  // OPTIMIZED: Use handleQuickRefresh instead of fetchData
  const handleAddTask = async () => {
    // Validate task
    if (!newTask.description.trim()) {
      alert('Please enter a task description');
      return;
    }

    try {
      const response = await fetch(`/api_admin_dashboard.php?f=add_task`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          ...newTask
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        // CHANGED: Use quick refresh instead of fetchData()
        handleQuickRefresh();
        closeAddTaskModal();
      } else {
        alert(result.error || 'Failed to create task');
      }
    } catch (err) {
      console.error("Error adding task:", err);
      alert('Failed to create task: ' + err.message);
    }
  };

  // Edit task functions
  const handleEditTask = () => {
    setEditedTask({
      id: selectedTask.id,
      description: selectedTask.description,
      department_id: selectedTask.department_id,
      category: selectedTask.category,
      status: selectedTask.status,
      admin_id: selectedTask.admin_id || '',
      content: selectedTask.content || ''
    });
    setEditMode(true);
  };

  const handleEditChange = (e) => {
    const { name, value } = e.target;
    setEditedTask({
      ...editedTask,
      [name]: value
    });
  };

  // OPTIMIZED: Use local updates + fetchTaskStats only
  const handleSaveChanges = async () => {
    if (updating) return; // Prevent double-clicks

    setUpdating(true);
    try {
      const token = localStorage.getItem('admin_token');
      if (!token) {
        alert('Please log in again.');
        return;
      }

      const response = await fetch(`/api_admin_dashboard.php?f=update_task`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token.trim(),
          ...editedTask
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        // CHANGED: Update locally instead of full refresh
        updateTaskInList(editedTask);
        updateSelectedTaskDisplay(editedTask);
        
        // Only fetch task stats (much faster than full refresh)
        fetchTaskStats();

        // Exit edit mode
        setEditMode(false);
        setEditedTask(null);
      } else {
        alert(result.error || 'Failed to update task');
      }
    } catch (err) {
      console.error("Error updating task:", err);
      alert('Failed to update task: ' + err.message);
    } finally {
      setUpdating(false);
    }
  };

  const handleCancelEdit = () => {
    setEditMode(false);
    setEditedTask(null);
  };

  // Message functions
  const handleMessageChange = (e) => {
    setNewMessage(e.target.value);
  };

  const handleAddMessage = async () => {
    if (!selectedTask || !newMessage.trim()) return;

    try {
      const response = await fetch(`/api_admin_dashboard.php?f=add_task_message`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          task_id: selectedTask.id,
          message: newMessage
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        // Add the new message to the list
        setTaskMessages([
          ...taskMessages,
          {
            id: result.message_id,
            message: newMessage,
            admin_name: result.admin_name,
            time: result.time,
            date: result.date
          }
        ]);

        // Clear the input
        setNewMessage('');

        // Refresh activity data
        fetchRecentActivity();
      } else {
        alert(result.error || 'Failed to add message');
      }
    } catch (err) {
      console.error("Error adding message:", err);
      alert('Failed to add message: ' + err.message);
    }
  };

  const handleNewTaskChange = (e) => {
    const { name, value } = e.target;
    setNewTask({
      ...newTask,
      [name]: value
    });
  };

  // OPTIMIZED: Use handleQuickRefresh instead of fetchData
  const handleDeleteTask = async () => {
    if (!selectedTask || !window.confirm('Are you sure you want to delete this task?')) {
      return;
    }

    try {
      const response = await fetch(`/api_admin_dashboard.php?f=delete_task`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          id: selectedTask.id
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        // CHANGED: Use quick refresh instead of fetchData()
        handleQuickRefresh();
        closeTaskDetails();
      } else {
        alert(result.error || 'Failed to delete task');
      }
    } catch (err) {
      console.error("Error deleting task:", err);
      alert('Failed to delete task: ' + err.message);
    }
  };

  // OPTIMIZED: Update locally instead of full refresh
  const handleUpdateTaskStatus = async (newStatus) => {
    if (!selectedTask) return;

    try {
      const response = await fetch(`/api_admin_dashboard.php?f=update_task`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          id: selectedTask.id,
          status: newStatus
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        // CHANGED: Update locally instead of full refresh
        updateTaskInList({ ...selectedTask, status: newStatus });
        setSelectedTask({ ...selectedTask, status: newStatus });
        
        // Only update stats (fast)
        fetchTaskStats();
      } else {
        alert(result.error || 'Failed to update task');
      }
    } catch (err) {
      console.error("Error updating task:", err);
      alert('Failed to update task: ' + err.message);
    }
  };

  const handleTimeFilterChange = (e) => {
    setTimeFilter(e.target.value);
    // Fetch updated activity data when filter changes
    fetch(`/api_admin_dashboard.php?f=get_recent_activity&timeFilter=${e.target.value}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: localStorage.getItem('admin_token')
      })
    })
    .then(res => res.json())
    .then(data => setRecentActivity(data))
    .catch(err => console.error("Error fetching activity with new filter:", err));
  };

  // Helper function to get department name by id
  const getDepartmentName = (departmentId) => {
    const department = departments.find(d => d.id === departmentId);
    return department ? department.name : 'General';
  };

  // Helper function to get staff name by id
  const getStaffName = (staffId) => {
    const staff = staffMembers.find(s => s.id === staffId);
    return staff ? staff.name : 'Unassigned';
  };

  const renderStatusBadge = (status) => {
    const badgeClasses = {
      'In Progress': 'bg-blue-100 text-blue-800',
      'Pending': 'bg-yellow-100 text-yellow-800',
      'Completed': 'bg-green-100 text-green-800',
      'Cancelled': 'bg-red-100 text-red-800',
      'Open': 'bg-blue-100 text-blue-800',
      'Closed': 'bg-gray-100 text-gray-800',
      'Resolved': 'bg-green-100 text-green-800'
    };

    const icons = {
      'In Progress': <Clock className="w-4 h-4 mr-1" />,
      'Pending': <AlertCircle className="w-4 h-4 mr-1" />,
      'Completed': <CheckCircle className="w-4 h-4 mr-1" />,
      'Cancelled': <XCircle className="w-4 h-4 mr-1" />,
      'Open': <Clock className="w-4 h-4 mr-1" />,
      'Closed': <CheckCircle className="w-4 h-4 mr-1" />,
      'Resolved': <CheckCircle className="w-4 h-4 mr-1" />
    };

    // Default styling if status not found in mapping
    const badgeClass = badgeClasses[status] || 'bg-gray-100 text-gray-800';
    const icon = icons[status] || <AlertCircle className="w-4 h-4 mr-1" />;

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit ${badgeClass}`}>
        {icon}
        {status}
      </span>
    );
  };

  const renderOrderStatusBadge = (status) => {
    const statusMap = {
      'Active': 'Completed',
      'Installing': 'Processing',
      'PendingPayment': 'Pending',
      'Cancelled': 'Cancelled'
    };

    // Map API status to UI status
    const uiStatus = statusMap[status] || status;

    const badgeClasses = {
      'Completed': 'bg-green-100 text-green-800',
      'Processing': 'bg-blue-100 text-blue-800',
      'Pending': 'bg-yellow-100 text-yellow-800',
      'Cancelled': 'bg-red-100 text-red-800'
    };

    // Default styling if status not found in mapping
    const badgeClass = badgeClasses[uiStatus] || 'bg-gray-100 text-gray-800';

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium w-fit ${badgeClass}`}>
        {uiStatus}
      </span>
    );
  };

  // Filter tasks based on search query and filters (client-side backup filtering)
  const filteredTasks = tasks.filter(task => {
    const matchesSearch =
      (task.id && task.id.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (task.description && task.description.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (task.department && task.department.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (task.category && task.category.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (task.assignedTo && task.assignedTo.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesStatus = selectedStatus === 'All' || task.status === selectedStatus;
    const matchesCategory = selectedCategory === 'All' || task.category === selectedCategory;

    return matchesSearch && matchesStatus && matchesCategory;
  });

  // Apply local sort if needed (though sorting should primarily be done by the API)
  const sortedTasks = [...filteredTasks].sort((a, b) => {
    if (sortDirection === 'desc') {
      return [a, b] = [b, a];
    }

    if (sortField === 'id') {
      const aNum = parseInt((a.id || '').replace(/\D/g, ''));
      const bNum = parseInt((b.id || '').replace(/\D/g, ''));
      return aNum - bNum;
    } else if (sortField === 'description') {
      return (a.description || '').localeCompare(b.description || '');
    } else if (sortField === 'department') {
      return (a.department || '').localeCompare(b.department || '');
    } else if (sortField === 'category') {
      return (a.category || '').localeCompare(b.category || '');
    } else if (sortField === 'status') {
      return (a.status || '').localeCompare(b.status || '');
    } else if (sortField === 'assignedTo') {
      return (a.assignedTo || '').localeCompare(b.assignedTo || '');
    }
    return 0;
  });

  // Use the task stats from API or calculate them as backup
  const getTaskStats = () => {
    return taskStats;
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <Sidebar
        sidebarCollapsed={sidebarCollapsed}
        activeMenu="Dashboard"
        navigateTo={navigateTo}
        toggleSidebar={toggleSidebar}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col bg-gray-100">
        {/* Top Menu */}
        <TopMenu toggleSidebar={toggleSidebar} />

        {/* Dashboard Content */}
        <div className="p-6 space-y-6 overflow-auto">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-800">Dashboard</h1>
            <div className="flex items-center space-x-2">
              <div className="text-sm text-gray-500">Last updated: {lastUpdated || 'Never'}</div>
              <button
                onClick={handleRefreshData}
                className="p-2 text-gray-500 hover:text-indigo-700 rounded-full hover:bg-gray-100"
                disabled={loading}
              >
                <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              </button>
              <button
                onClick={openAddTaskModal}
                className="bg-indigo-700 hover:bg-indigo-800 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center"
              >
                <Plus className="w-4 h-4 mr-1" />
                Create Task
              </button>
            </div>
          </div>

          {/* Dashboard Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <MetricCards taskStats={getTaskStats()} />
          </div>

          {/* Tasks Table */}
          <div className="bg-white border border-gray-200 shadow-sm rounded-md">
            <div className="p-4 border-b flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div className="flex flex-wrap gap-2">
                <div className="relative">
                  <select
                    className="pl-3 pr-8 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
                    value={selectedStatus}
                    onChange={(e) => handleStatusFilter(e.target.value)}
                  >
                    {uniqueStatuses.map(status => (
                      <option key={status} value={status}>{status === 'All' ? 'All Status' : status}</option>
                    ))}
                  </select>
                  <Filter className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                </div>

                <div className="relative">
                  <select
                    className="pl-3 pr-8 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
                    value={selectedCategory}
                    onChange={(e) => handleCategoryFilter(e.target.value)}
                  >
                    {uniqueCategories.map(category => (
                      <option key={category} value={category}>{category === 'All' ? 'All Categories' : category}</option>
                    ))}
                  </select>
                  <Filter className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                </div>
              </div>
              <div className="relative w-full sm:w-64">
                <input
                  type="text"
                  placeholder="Search tasks..."
                  className="pl-8 pr-3 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 search-input"
                  value={searchQuery}
                  onChange={handleSearch}
                />
                <Search className="absolute left-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
              </div>
            </div>

            <div className="overflow-x-auto">
                <table className="w-full tasks-table">
                  <thead>
                    <tr className="text-gray-500 text-xs border-b">
                      <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('id')}>
                        ID {getSortIcon('id')}
                      </th>
                      <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('description')}>
                        DESCRIPTION {getSortIcon('description')}
                      </th>
                      {/* This column will be hidden on small screens */}
                      <th className="p-4 text-left font-medium cursor-pointer hide-sm" onClick={() => handleSort('category')}>
                        CATEGORY {getSortIcon('category')}
                      </th>
                      <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('status')}>
                        STATUS {getSortIcon('status')}
                      </th>
                      {/* This column will be hidden on small screens */}
                      <th className="p-4 text-left font-medium cursor-pointer hide-sm" onClick={() => handleSort('assignedTo')}>
                        ASSIGNED TO {getSortIcon('assignedTo')}
                      </th>

                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td colSpan="6" className="p-4 text-center">
                          <div className="flex justify-center items-center">
                            <RefreshCw className="w-5 h-5 animate-spin mr-2 text-indigo-700" />
                            Loading tasks...
                          </div>
                        </td>
                      </tr>
                    ) : error ? (
                      <tr>
                        <td colSpan="6" className="p-4 text-center text-red-600">
                          {error}
                        </td>
                      </tr>
                    ) : sortedTasks.length > 0 ? (
                      sortedTasks.map((task, index) => (
                        <tr
                          key={task.id}
                          className={`border-b ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} task-row`}
                          onClick={() => handleTaskClick(task)}
                        >
                          <td className="p-4 font-medium text-indigo-700">{task.id}</td>
                          <td className="p-4 text-gray-700">{task.description}</td>
                          <td className="p-4 text-gray-700 hide-sm">{task.category || 'N/A'}</td>
                          <td className="p-4">{renderStatusBadge(task.status)}</td>
                          <td className="p-4 text-gray-700 hide-sm">{task.assignedTo || 'Unassigned'}</td>

                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan="6" className="p-4 text-center text-gray-500">
                          No tasks found matching your criteria
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
            </div>

            {/* Pagination */}
            <div className="p-4 border-t flex justify-between items-center">
              <div className="text-sm text-gray-500">
                Showing {sortedTasks.length} of {tasks.length} tasks
              </div>
              <div className="flex space-x-1">
                <button className="px-3 py-1 border rounded text-sm bg-white text-gray-500">Previous</button>
                <button className="px-3 py-1 border rounded text-sm bg-indigo-700 text-white">1</button>
                <button className="px-3 py-1 border rounded text-sm bg-white text-gray-700">2</button>
                <button className="px-3 py-1 border rounded text-sm bg-white text-gray-500">Next</button>
              </div>
            </div>
          </div>

          {/* Additional Info Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Activity */}
            <div className="bg-white shadow-sm rounded-md border">
              <div className="p-4 border-b flex justify-between items-center">
                <h3 className="text-lg font-semibold">Recent Activity</h3>
                <select
                  className="pl-3 pr-8 py-2 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
                  value={timeFilter}
                  onChange={handleTimeFilterChange}
                >
                  <option value="today">Today</option>
                  <option value="yesterday">Yesterday</option>
                  <option value="thisWeek">This Week</option>
                  <option value="thisMonth">This Month</option>
                </select>
              </div>

              <div className="divide-y">
                {loading ? (
                  <div className="p-4 text-center">
                    <RefreshCw className="inline-block w-5 h-5 animate-spin mr-2 text-indigo-700" />
                    Loading activity...
                  </div>
                ) : recentActivity.length > 0 ? (
                  recentActivity.map((activity, index) => (
<div key={index} className="px-4 py-4 hover:bg-gray-50">
  <div className="flex items-center justify-between">
    <div className="flex items-center">
      <div style={{ marginRight: "8px" }}>
        <div className="h-8 w-8 rounded-full bg-blue-50 flex items-center justify-center">
          <User className="w-4 h-4 text-blue-500" />
        </div>
      </div>
      <div>
        <div className="font-medium">{activity.action}</div>
        <div className="text-gray-700 mt-1">{activity.description}</div>
        <div className="text-gray-500 text-sm mt-1">by {activity.user}</div>
      </div>
    </div>
    <div className="text-gray-500 text-sm">{activity.timestamp}</div>
  </div>
</div>
                  ))
                ) : (
                  <div className="p-4 text-center text-gray-500">
                    No recent activity
                  </div>
                )}
              </div>
              <div className="p-4 border-t">
                <a href="#" className="text-indigo-600 text-sm font-medium">
                  View All Activity
                </a>
              </div>
            </div>

            {/* Recent Orders */}
            <div className="bg-white shadow-sm rounded-md border">
              <div className="p-4 border-b flex justify-between items-center">
                <h3 className="text-lg font-semibold">Recent Orders</h3>
                <button
                  className="text-indigo-700 text-sm font-medium flex items-center"
                  onClick={() => navigateTo('/admin/orders')}
                >
                  <Eye className="w-4 h-4 mr-1" />
                  View All
                </button>
              </div>

              <div className="divide-y">
                {loading ? (
                  <div className="p-4 text-center">
                    <RefreshCw className="inline-block w-5 h-5 animate-spin mr-2 text-indigo-700" />
                    Loading orders...
                  </div>
                ) : recentOrders.length > 0 ? (
                  recentOrders.map((order) => (
                    <div key={order.id} className="p-4 hover:bg-gray-50">
                      <div className="flex justify-between">
                        <div className="font-medium text-indigo-700">{order.id}</div>
                        <div>{renderOrderStatusBadge(order.status)}</div>
                      </div>
                      <div className="flex justify-between mt-2">
                        <div className="text-sm">{order.customerName}</div>
                        <div className="font-medium">{order.amount}</div>
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {new Date(order.date).toLocaleDateString('en-GB', { day: '2-digit', month: '2-digit', year: 'numeric' })}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="p-4 text-center text-gray-500">
                    No recent orders
                  </div>
                )}
              </div>
              <div className="p-4 border-t">
                <button
                  className="w-full py-2 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
                  onClick={() => navigateTo('/admin/orders')}
                >
                  See All Orders
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Add Task Modal */}
      {addTaskModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-md shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-3 border-b flex justify-between items-center">
              <h2 className="text-lg font-bold text-gray-800">Add New Task</h2>
              <button
                onClick={closeAddTaskModal}
                className="text-gray-500 hover:text-gray-700"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>
            <div className="p-4 space-y-3">
              {/* Task Form */}
              <div className="space-y-3">
                <div>
                  <label className="block text-xs text-gray-700">Description*</label>
                  <input
                    type="text"
                    name="description"
                    value={newTask.description}
                    onChange={handleNewTaskChange}
                    className="w-full p-1.5 border rounded-md focus-ring text-sm"
                    placeholder="Enter task description"
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-xs text-gray-700">Department</label>
                    <select
                      name="department_id"
                      value={newTask.department_id}
                      onChange={handleNewTaskChange}
                      className="w-full p-1.5 border rounded-md focus-ring text-sm"
                    >
                      {departments.length === 0 ? (
                        <option value="">Loading departments...</option>
                      ) : (
                        departments.map(dept => (
                          <option key={dept.id} value={dept.id}>{dept.name}</option>
                        ))
                      )}
                    </select>
                  </div>

                  <div>
                    <label className="block text-xs text-gray-700">Category</label>
                    <select
                      name="category"
                      value={newTask.category}
                      onChange={handleNewTaskChange}
                      className="w-full p-1.5 border rounded-md focus-ring text-sm"
                    >
                      <option value="Other">Other</option>
                      <option value="Remote Hands">Remote Hands</option>
                      <option value="Hardware">Hardware</option>
                      <option value="Patching">Patching</option>
                      <option value="Maintenance">Maintenance</option>
                    </select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-xs text-gray-700">Status</label>
                    <select
                      name="status"
                      value={newTask.status}
                      onChange={handleNewTaskChange}
                      className="w-full p-1.5 border rounded-md focus-ring text-sm"
                    >
                      <option value="Pending">Pending</option>
                      <option value="In Progress">In Progress</option>
                      <option value="To Order">To Order</option>
                      <option value="Completed">Completed</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-xs text-gray-700">Assigned To</label>
                    <select
                      name="admin_id"
                      value={newTask.admin_id}
                      onChange={handleNewTaskChange}
                      className="w-full p-1.5 border rounded-md focus-ring text-sm"
                    >
                      <option value="">Unassigned</option>
                      {staffMembers.length === 0 ? (
                        <option value="" disabled>Loading staff...</option>
                      ) : (
                        staffMembers.map(staff => (
                          <option key={staff.id} value={staff.id}>{staff.name}</option>
                        ))
                      )}
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-xs text-gray-700">Content</label>
                  <textarea
                    name="content"
                    value={newTask.content || ''}
                    onChange={handleNewTaskChange}
                    className="w-full p-1.5 border rounded-md focus-ring text-sm"
                    rows="3"
                    placeholder="Enter task details (optional)"
                  ></textarea>
                </div>
              </div>

              {/* Form Actions */}
              <div className="flex justify-end gap-2 pt-2 border-t mt-2">
                <button
                  onClick={closeAddTaskModal}
                  className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 flex items-center text-xs hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleAddTask}
                  className="px-3 py-1.5 bg-indigo-700 text-white rounded-md flex items-center text-xs hover:bg-indigo-800"
                >
                  <Plus className="w-3 h-3 mr-1" />
                  Add Task
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Task Details Modal with Edit Mode and Messages */}
      {selectedTask && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-md shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-3 border-b flex justify-between items-center">
              <h2 className="text-lg font-bold text-gray-800">
                {editMode ? 'Edit Task' : 'Task Details'}
              </h2>
              <button
                onClick={closeTaskDetails}
                className="text-gray-500 hover:text-gray-700"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>
            <div className="p-4 space-y-3">
              {/* Task Header */}
              <div className="flex justify-between items-start">
                <div>
          
                  {editMode ? (
                    <input
                      type="text"
                      name="description"
                      value={editedTask.description}
                      onChange={handleEditChange}
                      className="w-full p-1.5 border rounded-md mt-1 text-sm"
                    />
                  ) : (
                    <div className="text-sm text-gray-500">{selectedTask.description}</div>
                  )}
                </div>
                {!editMode && renderStatusBadge(selectedTask.status)}
              </div>

              {/* Task Details */}
              <div className="grid grid-cols-2 gap-3 py-2 border-t border-b">
                <div>
                  <div className="text-xs text-gray-500">Category</div>
                  {editMode ? (
                    <select
                      name="category"
                      value={editedTask.category}
                      onChange={handleEditChange}
                      className="w-full p-1.5 border rounded-md text-sm"
                    >
                      <option value="Other">Other</option>
                      <option value="Remote Hands">Remote Hands</option>
                      <option value="Hardware">Hardware</option>
                      <option value="Patching">Patching</option>
                      <option value="Maintenance">Maintenance</option>
                    </select>
                  ) : (
                    <div className="font-medium text-sm">{selectedTask.category || 'N/A'}</div>
                  )}
                </div>
                <div>
                  <div className="text-xs text-gray-500">Department</div>
                  {editMode ? (
                    <select
                      name="department_id"
                      value={editedTask.department_id}
                      onChange={handleEditChange}
                      className="w-full p-1.5 border rounded-md text-sm"
                    >
                      {departments.map(dept => (
                        <option key={dept.id} value={dept.id}>{dept.name}</option>
                      ))}
                    </select>
                  ) : (
                    <div className="font-medium text-sm">{selectedTask.department}</div>
                  )}
                </div>
                <div>
                  <div className="text-xs text-gray-500">Assigned To</div>
                  {editMode ? (
                    <select
                      name="admin_id"
                      value={editedTask.admin_id}
                      onChange={handleEditChange}
                      className="w-full p-1.5 border rounded-md text-sm"
                    >
                      <option value="">Unassigned</option>
                      {staffMembers.map(staff => (
                        <option key={staff.id} value={staff.id}>{staff.name}</option>
                      ))}
                    </select>
                  ) : (
                    <div className="font-medium text-sm">{selectedTask.assignedTo || 'Unassigned'}</div>
                  )}
                </div>
                <div>
                  <div className="text-xs text-gray-500">Status</div>
                  {editMode ? (
                    <select
                      name="status"
                      value={editedTask.status}
                      onChange={handleEditChange}
                      className="w-full p-1.5 border rounded-md text-sm"
                    >
                      <option value="Pending">Pending</option>
                      <option value="In Progress">In Progress</option>
                      <option value="To Order">To Order</option>
                      <option value="Completed">Completed</option>
                    </select>
                  ) : (
                    renderStatusBadge(selectedTask.status)
                  )}
                </div>
                {!editMode && selectedTask.createdDate && (
                  <div className="col-span-2">
                    <div className="text-xs text-gray-500">Created Date</div>
                    <div className="font-medium text-sm">
                      {new Date(selectedTask.createdDate).toLocaleDateString('en-GB', {
                          day: '2-digit',
                          month: '2-digit',
                          year: 'numeric'
                        })}
                    </div>
                  </div>
                )}
              </div>

              {/* Task Description */}
              <div>
                <h3 className="text-sm font-bold mb-1">Details</h3>
                {editMode ? (
                  <textarea
                    name="content"
                    value={editedTask.content}
                    onChange={handleEditChange}
                    className="w-full p-1.5 border rounded-md text-sm"
                    rows="3"
                  ></textarea>
                ) : (
                  <div className="bg-gray-50 rounded-md p-2">
                    <p className="text-xs text-gray-700">
                      {selectedTask.content || `No description provided.`}
                    </p>
                  </div>
                )}
              </div>

              {/* Messages Section */}
              <div>
                <div className="flex justify-between items-center mb-1">
                  <h3 className="text-sm font-bold">Messages</h3>
                </div>

                {/* Messages List */}
                <div className="bg-gray-50 rounded-md p-2 mb-2 max-h-40 overflow-y-auto">
                  {loadingMessages ? (
                    <div className="flex justify-center items-center p-2">
                      <RefreshCw className="w-4 h-4 animate-spin mr-1 text-indigo-700" />
                      <span className="text-xs">Loading messages...</span>
                    </div>
                  ) : taskMessages.length > 0 ? (
                    <div className="space-y-3">
                      {taskMessages.map((msg) => (
                        <div key={msg.id} className="flex items-start bg-white rounded p-2 border-l-2 border-indigo-200">
                          <div className="h-6 w-6 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-700 mr-2 flex-shrink-0">
                            <User className="w-3 h-3" />
                          </div>
                          <div className="flex-grow w-full">
                            <div className="flex justify-between items-center w-full mb-1">
                              <span className="font-medium text-xs text-gray-800">{msg.admin_name}</span>
                              <span className="text-xs text-gray-500">{msg.time}</span>
                            </div>
                            <div 
                              className="text-xs text-gray-700 whitespace-pre-wrap leading-relaxed"
                              style={{ 
                                wordBreak: 'break-word',
                                whiteSpace: 'pre-wrap',
                                lineHeight: '1.4'
                              }}
                            >
                              {msg.message}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center text-gray-500 text-xs p-2">
                      No messages yet.
                    </div>
                  )}
                </div>

                {/* New Message Form */}
                {!editMode && (
                  <div>
                    <div className="flex space-x-2">
                      <textarea
                        value={newMessage}
                        onChange={handleMessageChange}
                        placeholder="Add a message... (Press Shift+Enter for new line)"
                        className="flex-grow p-2 border rounded-md text-xs focus:ring-indigo-200 focus:border-indigo-300 resize-none"
                        rows="3"
                        style={{ minHeight: '60px' }}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            handleAddMessage();
                          }
                        }}
                      ></textarea>
                      <button
                        onClick={handleAddMessage}
                        disabled={!newMessage.trim()}
                        className={`px-3 py-2 rounded-md text-xs flex items-center self-end ${
                          newMessage.trim() ? 'bg-indigo-700 text-white hover:bg-indigo-800' : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                        }`}
                      >
                        <MessageSquare className="w-3 h-3 mr-1" />
                        Send
                      </button>
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      Press Enter to send, Shift+Enter for new line
                    </div>
                  </div>
                )}
              </div>

              {/* Task Actions */}
              <div className="flex justify-end gap-2 pt-2 border-t mt-1">
                {editMode ? (
                  <>
                    <button
                      onClick={handleCancelEdit}
                      className="px-3 py-1 border border-gray-300 rounded-md text-gray-700 flex items-center text-xs hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleSaveChanges}
                      disabled={updating}
                      className={`px-3 py-1 rounded-md flex items-center text-xs ${
                        updating 
                          ? 'bg-gray-400 text-white cursor-not-allowed' 
                          : 'bg-indigo-700 text-white hover:bg-indigo-800'
                      }`}
                    >
                      {updating ? (
                        <>
                          <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Save
                        </>
                      )}
                    </button>
                  </>
                ) : (
                  <>
                    <button
                      onClick={() => handleDeleteTask()}
                      className="px-3 py-1 border border-red-300 rounded-md text-red-700 flex items-center text-xs hover:bg-red-50"
                    >
                      <Trash2 className="w-3 h-3 mr-1" />
                      Delete
                    </button>
                    <button
                      onClick={handleEditTask}
                      className="px-3 py-1 border border-gray-300 rounded-md text-gray-700 flex items-center text-xs hover:bg-gray-50"
                    >
                      <Edit className="w-3 h-3 mr-1" />
                      Edit
                    </button>
                    <button
                      onClick={() => handleUpdateTaskStatus(selectedTask.status === 'Completed' ? 'In Progress' : 'Completed')}
                      className="px-3 py-1 bg-indigo-700 text-white rounded-md flex items-center text-xs hover:bg-indigo-800"
                    >
                      <CheckCircle className="w-3 h-3 mr-1" />
                      {selectedTask.status === 'Completed' ? 'Reopen' : 'Complete'}
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;