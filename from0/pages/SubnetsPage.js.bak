import React, { useState, useEffect } from 'react';
import {
  Search,
  Plus,
  Filter,
  ArrowUp,
  ArrowDown,
  MoreVertical,
  Eye,
  Download,
  Edit,
  XCircle,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Clock,
  Network,
  Globe,
  Server,
  Hash,
  Wifi,
  Activity,
  ChevronDown,
  Shield,
  Map,
  Zap,
  FileText,
  Trash2,
  Layers,
  Unlink,
  Link,
  Divide
} from 'lucide-react';
import Sidebar from '../components/Sidebar';
import TopMenu from '../components/TopMenu';
import AddSubnetModal from '../components/AddSubnetModal';
import DivideSubnetModal from '../components/DivideSubnetModal';
import AssignSubnetModal from '../components/AssignSubnetModal';
import axios from 'axios';
import { toast } from 'react-toastify';
import CompactSubnetTree from '../components/CompactSubnetTree';
import { API_URL } from '../config';
const SubnetsPage = ({ navigateTo, sidebarCollapsed, toggleSidebar }) => {
  // State for subnets data and loading
  const [subnets, setSubnets] = useState([]);
  const [mainSubnets, setMainSubnets] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState('Never');
  const mainSubnetsArray = Array.isArray(mainSubnets) ? mainSubnets : [];
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);

  const itemsPerPage = 10;
  const totalPages = Math.ceil(mainSubnetsArray.length / itemsPerPage);
  // Filter and sort state
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('All');
  const [selectedCategory, setSelectedCategory] = useState('Root');
  const [selectedLocation, setSelectedLocation] = useState('All');
  const [sortField, setSortField] = useState('lastUpdated');
  const [sortDirection, setSortDirection] = useState('desc');
  const [allSubnets, setAllSubnets] = useState([]);
  // Modal state
  const [selectedSubnet, setSelectedSubnet] = useState(null);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isDivideModalOpen, setIsDivideModalOpen] = useState(false);
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);

  // State for filter dropdowns
  const [uniqueCategories, setUniqueCategories] = useState(['All']);
  const [uniqueLocations, setUniqueLocations] = useState(['All']);
  const [uniqueStatuses, setUniqueStatuses] = useState(['All']);

  // Statistics state
  const [stats, setStats] = useState({
    totalIps: 0,
    usedIps: 0,
    utilizationPercent: 0,
    locationCount: 0,
    networkPerformance: 99.98
  });

  // Fetch subnets from API
  const fetchSubnets = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // First fetch all subnets for the tree (unfiltered)
      await fetchAllSubnetsForTree();

      // Then fetch filtered subnets for the main view
      const response = await axios.post(`${API_URL}/api_admin_subnets.php?f=get_subnets`, {
        token: localStorage.getItem('admin_token'),
        status: selectedStatus !== 'All' ? selectedStatus : '',
        category: selectedCategory !== 'All' ? selectedCategory : '',
        location: selectedLocation !== 'All' ? selectedLocation : '',
        search: searchQuery || '',
        sortField: sortField,
        sortDirection: sortDirection
      });

      if (response.data && Array.isArray(response.data)) {
        setSubnets(response.data);

        // Ensure all categories are always available
        const categories = [
          'All',
          'Root',
          'Customer',
          'Internal',
          'Management'
        ];
        setUniqueCategories(categories);

        // Filter to only main subnets (those without a parentId or with parentId = null/0)
        const mainSubnetsOnly = response.data.filter(subnet =>
          !subnet.parentId || subnet.parentId === 0 || subnet.parentId === null
        );
        setMainSubnets(mainSubnetsOnly);

        // Update last updated timestamp
        setLastUpdated(new Date().toLocaleTimeString());
      }
    } catch (err) {
      console.error('Error fetching subnets:', err);
      setError('Failed to fetch subnets');
      toast.error('Failed to fetch subnets');
    } finally {
      setIsLoading(false);
    }
  };


  // Initial data fetch
  useEffect(() => {
    fetchSubnets();
  }, []);

  // Refetch when filters or sort change
  useEffect(() => {
    fetchSubnets();
  }, [selectedStatus, selectedCategory, selectedLocation, sortField, sortDirection]);

  useEffect(() => {
    if (currentPage > totalPages) {
      setCurrentPage(Math.max(1, totalPages));
    }
  }, [mainSubnetsArray, totalPages]);

  // Manual refresh
  const handleRefreshData = () => {
    fetchSubnets();
  };

  // Search handling with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      fetchSubnets();
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Handle search input
  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
  };

  // Filter handlers
  const handleStatusFilter = (status) => {
    setSelectedStatus(status);
  };

  const handleCategoryFilter = (category) => {
    setSelectedCategory(category);
  };

  const handleLocationFilter = (location) => {
    setSelectedLocation(location);
  };

  // Sort handlers
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const fetchAllSubnetsForTree = async () => {
    try {
      const response = await axios.post(`${API_URL}/api_admin_subnets.php?f=get_subnets`, {
        token: localStorage.getItem('admin_token'),
        // No filters to ensure we get ALL subnets
      });

      if (response.data && Array.isArray(response.data)) {
        setAllSubnets(response.data);
      }
    } catch (err) {
      console.error('Error fetching all subnets for tree:', err);
      // Don't show an error toast here to avoid duplicate notifications
    }
  };

  // Get sort icon based on current sort field/direction
  const getSortIcon = (field) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ?
      <ArrowUp className="w-4 h-4 inline ml-1" /> :
      <ArrowDown className="w-4 h-4 inline ml-1" />;
  };

  // Pagination handlers
  const nextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const goToPage = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  // Modal handlers
  const handleSubnetClick = (subnet) => {
    setSelectedSubnet(subnet);
  };

  const closeSubnetDetails = () => {
    setSelectedSubnet(null);
  };

  const openAddSubnetModal = () => {
    setIsAddModalOpen(true);
  };

  const closeAddSubnetModal = () => {
    setIsAddModalOpen(false);
  };

  const openDivideSubnetModal = (subnet) => {
    setSelectedSubnet(subnet);
    setIsDivideModalOpen(true);
  };

  const closeDivideSubnetModal = () => {
    setIsDivideModalOpen(false);
  };

  const openAssignSubnetModal = (subnet) => {
    setSelectedSubnet(subnet);
    setIsAssignModalOpen(true);
  };

  const closeAssignSubnetModal = () => {
    setIsAssignModalOpen(false);
  };

  // CRUD operations
  const handleAddSubnet = async (subnetData) => {
    try {
      const response = await axios.post(`${API_URL}/api_admin_subnets.php?f=add_subnet`, {
        ...subnetData,
        token: localStorage.getItem('admin_token')
      }, {
        validateStatus: function (status) {
          return status >= 200 && status < 300;
        }
      });

      console.log('Add Subnet Full Response:', response);

      if (response.data.success) {
        toast.success('Subnet added successfully');
        fetchSubnets();
        closeAddSubnetModal();
      } else {
        throw new Error(response.data.error || 'Failed to add subnet');
      }
    } catch (err) {
      console.error('Add Subnet Detailed Error:', {
        message: err.message,
        response: err.response?.data,
        status: err.response?.status,
        headers: err.response?.headers
      });

      const errorMessage = err.response?.data?.error ||
                           err.response?.data?.message ||
                           'Error adding subnet';

      toast.error(errorMessage);
    }
  };

  const handleDeleteSubnet = async (subnetId, force = false) => {
    if (!window.confirm('Are you sure you want to delete this subnet?')) {
      return;
    }

    try {
      const response = await axios.post(`${API_URL}/api_admin_subnets.php?f=delete_subnet`, {
        subnet_id: subnetId,
        force: force,
        token: localStorage.getItem('admin_token')
      });

      if (response.data.success) {
        toast.success('Subnet deleted successfully');
        fetchSubnets();
        if (selectedSubnet && selectedSubnet.id === subnetId) {
          closeSubnetDetails();
        }
      }
    } catch (err) {
      console.error('Error deleting subnet:', err);

      // Check if it's because the subnet is assigned
      if (err.response?.data?.error?.includes('assigned to a server')) {
        if (window.confirm('This subnet is assigned to a server. Force delete?')) {
          handleDeleteSubnet(subnetId, true);
        }
      } else if (err.response?.data?.error?.includes('has child subnets')) {
        if (window.confirm('This subnet has child subnets. Force delete all?')) {
          handleDeleteSubnet(subnetId, true);
        }
      } else {
        toast.error('Error deleting subnet: ' + (err.response?.data?.error || err.message));
      }
    }
  };

  const handleAssignSubnet = async (data) => {
    try {
      console.log('Assign Subnet Request Data:', data);

      const response = await axios.post(
        '${API_URL}/api_admin_subnets.php?f=assign_subnet_to_server',
        {
          ...data,
          token: localStorage.getItem('admin_token')
        },
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Full Assign Subnet Response:', response);

      if (response.data.success) {
        toast.success('Subnet assigned successfully');
        fetchSubnets();
        closeAssignSubnetModal();
      } else {
        console.error('Assign Subnet Error:', response.data);
        toast.error(response.data.error || 'Failed to assign subnet');
      }
    } catch (err) {
      console.error('Detailed Error Assigning Subnet:', {
        message: err.message,
        response: err.response?.data,
        status: err.response?.status,
        headers: err.response?.headers
      });

      // More specific error handling
      if (err.response && err.response.data && err.response.data.error) {
        toast.error(err.response.data.error);
      } else {
        toast.error('Error assigning subnet: ' + (err.response?.data?.error || err.message));
      }
    }
  };

  const handleUnassignSubnet = async (subnetId) => {
    if (!window.confirm('Are you sure you want to unassign this subnet?')) {
      return;
    }

    try {
      const response = await axios.post(`${API_URL}/api_admin_subnets.php?f=unassign_subnet`, {
        subnet_id: subnetId,
        token: localStorage.getItem('admin_token') // Include the token
      });

      if (response.data.success) {
        toast.success('Subnet unassigned successfully');
        fetchSubnets();
      }
    } catch (err) {
      console.error('Error unassigning subnet:', err);
      toast.error('Error unassigning subnet: ' + (err.response?.data?.error || err.message));
    }
  };

  const handleDivideSubnet = async (formData) => {
    // Create a properly formatted request payload
    const requestData = {
      subnet_id: formData.subnet_id,
      new_subnet_size: formData.new_subnet_size,
      generate_ips: formData.generate_ips,
      token: localStorage.getItem('admin_token')
    };

    console.log('Divide Subnet Request Data:', requestData);

    try {
      // Show a pending toast to indicate the request is being processed
      const pendingToast = toast.info('Processing subnet division...', { autoClose: false });

      // Make the API request with explicit error handling
      const response = await axios.post(
        '${API_URL}/api_admin_subnets.php?f=divide_subnet',
        requestData,
        {
          // Add timeout and headers for better debugging
          timeout: 30000, // 30 seconds timeout
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      // Log the full response for debugging
      console.log('Full Divide Subnet Response:', response);

      // Close the pending toast
      toast.dismiss(pendingToast);

      // Check if the response indicates success
      if (response.data && response.data.success) {
        // Success! Show confirmation and update UI
        toast.success(`Subnet divided successfully into ${response.data.new_subnet_ids.length} smaller subnets`);

        // Only call fetchSubnets once and await it to ensure data is fully refreshed
        await fetchSubnets();

        closeDivideSubnetModal(); // Close the modal
      } else {
        // API returned but without success flag
        const errorMsg = response.data?.error || 'Unknown error occurred';
        console.error('API returned without success:', response.data);
        toast.error(`Error dividing subnet: ${errorMsg}`);
      }
    } catch (err) {
      // Detailed error logging for all possible error scenarios
      console.error('Exception in Divide Subnet:', {
        message: err.message,
        response: err.response?.data,
        status: err.response?.status,
        stack: err.stack
      });

      // Provide specific error message based on error type
      if (err.response) {
        // Server responded with an error status
        const serverError = err.response.data?.error || err.response.statusText || 'Server error';
        toast.error(`Server error: ${serverError}`);
      } else if (err.request) {
        // Request was made but no response received
        toast.error('No response from server. Please check your network connection.');
      } else {
        // Other errors
        toast.error(`Error: ${err.message}`);
      }
    }
  };

  // Helper functions
  const renderStatusBadge = (status, assignedTo) => {
    const badgeClasses = {
      'Available': 'bg-green-100 text-green-800',
      'Assigned': 'bg-blue-100 text-blue-800',
      'Full': 'bg-red-100 text-red-800',
      'Unavailable': 'bg-orange-100 text-orange-800', // New status
      'Partially Available': 'bg-green-100 text-green-800',
      'Reserved': 'bg-yellow-100 text-yellow-800',
      'Deprecated': 'bg-red-100 text-red-800',
      'Active': 'bg-green-100 text-green-800'
    };

    const icons = {
      'Available': <CheckCircle className="w-4 h-4 mr-1" />,
      'Assigned': <Link className="w-4 h-4 mr-1" />,
      'Full': <XCircle className="w-4 h-4 mr-1" />,
      'Unavailable': <AlertCircle className="w-4 h-4 mr-1" />, // New status
      'Partially Available': <AlertCircle className="w-4 h-4 mr-1" />,
      'Reserved': <AlertCircle className="w-4 h-4 mr-1" />,
      'Deprecated': <XCircle className="w-4 h-4 mr-1" />,
      'Active': <CheckCircle className="w-4 h-4 mr-1" />
    };

    // If status is Assigned and we have assigned server info, display it
    let displayText = status;
    if (status === 'Assigned' && assignedTo && assignedTo !== 'Unassigned') {
      displayText = assignedTo;
    } else if (status === 'Unavailable') {
      displayText = 'Partially Available';
    }

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit ${badgeClasses[status] || 'bg-gray-100 text-gray-800'}`}>
        {icons[status] || <AlertCircle className="w-3 h-3 mr-1" />}
        {displayText}
      </span>
    );
  };

  const renderUtilizationBadge = (percent) => {
    let badgeClass = '';
    if (percent >= 90) {
      badgeClass = 'bg-red-100 text-red-800';
    } else if (percent >= 75) {
      badgeClass = 'bg-yellow-100 text-yellow-800';
    } else if (percent >= 50) {
      badgeClass = 'bg-blue-100 text-blue-800';
    } else {
      badgeClass = 'bg-green-100 text-green-800';
    }

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium w-fit ${badgeClass}`}>
        {percent}%
      </span>
    );
  };

  // Format IP address CIDR notation
  const formatCidr = (cidr) => {
    const [subnet, mask] = cidr.split('/');
    return (
      <div className="font-mono">
        {subnet}<span className="text-indigo-700">/{mask}</span>
      </div>
    );
  };

  // Only paginate the main subnets
  const paginatedSubnets = mainSubnetsArray.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <Sidebar
        sidebarCollapsed={sidebarCollapsed}
        activeMenu="Subnets"
        navigateTo={navigateTo}
        toggleSidebar={toggleSidebar}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col bg-gray-100">
        {/* Top Menu */}
        <TopMenu toggleSidebar={toggleSidebar} />

        {/* Subnets Content */}
        <div className="p-6 space-y-6 overflow-auto">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-800">Subnets Management</h1>
            <div className="flex items-center space-x-2">
              <div className="text-sm text-gray-500">Last updated: {lastUpdated}</div>
              <button
                onClick={handleRefreshData}
                className="p-2 text-gray-500 hover:text-indigo-700 rounded-full hover:bg-gray-100"
                disabled={isLoading}
              >
                <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              </button>
              <button
                className="bg-indigo-700 hover:bg-indigo-800 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center"
                onClick={openAddSubnetModal}
                disabled={isLoading}
              >
                <Plus className="w-4 h-4 mr-1" />
                Add New Subnet
              </button>
            </div>
          </div>

          {/* Quick Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* IP Address Utilization */}
            <div className="bg-white shadow-sm rounded-md border p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm text-gray-500">IP Address Utilization</div>
                  <div className="text-xl font-bold mt-1">{stats.usedIps.toLocaleString()} / {stats.totalIps.toLocaleString()}</div>
                  <div className="mt-1 text-xs text-gray-500">
                    {stats.utilizationPercent}% usage across all subnets
                  </div>
                </div>
                <div className="bg-indigo-100 p-3 rounded-full">
                  <Hash className="w-6 h-6 text-indigo-700" />
                </div>
              </div>
              <div className="mt-2 w-full bg-gray-200 rounded-full h-2.5">
                <div className="bg-indigo-600 h-2.5 rounded-full" style={{ width: `${stats.utilizationPercent}%` }}></div>
              </div>
            </div>

            {/* Locations */}
            <div className="bg-white shadow-sm rounded-md border p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm text-gray-500">Subnet Locations</div>
                  <div className="text-xl font-bold mt-1">{stats.locationCount}</div>
                  <div className="mt-1 text-xs text-gray-500">
                    {uniqueLocations.slice(1, 5).join(', ')}
                    {uniqueLocations.length > 5 ? '...' : ''}
                  </div>
                </div>
                <div className="bg-green-100 p-3 rounded-full">
                  <Map className="w-6 h-6 text-green-700" />
                </div>
              </div>
            </div>

            {/* Network Performance */}
            <div className="bg-white shadow-sm rounded-md border p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm text-gray-500">Network Performance</div>
                  <div className="text-xl font-bold mt-1">{stats.networkPerformance}%</div>
                  <div className="mt-1 flex items-center text-xs text-green-500">
                    <ArrowUp className="w-3 h-3 mr-1" />
                    +0.05% from last month
                  </div>
                </div>
                <div className="bg-blue-100 p-3 rounded-full">
                  <Wifi className="w-6 h-6 text-blue-700" />
                </div>
              </div>
            </div>
          </div>

          {/* Subnets Filter and Search */}
          <div className="bg-white border border-gray-200 shadow-sm rounded-md">
            <div className="p-4 border-b flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div className="flex flex-wrap gap-2">
              <div className="relative">
  <select
    className="pl-3 pr-8 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
    value={selectedStatus}
    onChange={(e) => handleStatusFilter(e.target.value)}
    disabled={isLoading}
  >
    <option value="All">All Status</option>
    <option value="Available">Available</option>
    <option value="Assigned">Assigned</option>
    <option value="Unavailable">Unavailable</option>
    <option value="Full">Full</option>
    {/* Include any other statuses as needed */}
  </select>
  <Filter className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
</div>
                <div className="relative">
                  <select
                    className="pl-3 pr-8 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
                    value={selectedCategory}
                    onChange={(e) => handleCategoryFilter(e.target.value)}
                    disabled={isLoading}
                  >
                    {uniqueCategories.map(category => (
                      <option key={category} value={category}>{category === 'All' ? 'All Categories' : category}</option>
                    ))}
                  </select>
                  <Filter className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                </div>
                <div className="relative">
                  <select
                    className="pl-3 pr-8 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
                    value={selectedLocation}
                    onChange={(e) => handleLocationFilter(e.target.value)}
                    disabled={isLoading}
                  >
                    {uniqueLocations.map(location => (
                      <option key={location} value={location}>{location === 'All' ? 'All Locations' : location}</option>
                    ))}
                  </select>
                  <Filter className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                </div>
              </div>
              <div className="relative w-full sm:w-64">
                <input
                  type="text"
                  placeholder="Search subnets..."
                  className="pl-8 pr-3 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 search-input"
                  value={searchQuery}
                  onChange={handleSearch}
                  disabled={isLoading}
                />
                <Search className="absolute left-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
              </div>
            </div>

            {/* Error display */}
            {error && (
              <div className="p-4 bg-red-50 text-red-700 border-b border-red-100">
                <div className="flex items-center">
                  <AlertCircle className="w-5 h-5 mr-2" />
                  <span>{error}</span>
                </div>
              </div>
            )}

            {/* Loading indicator */}
            {isLoading && (
              <div className="p-4 text-center text-gray-500">
                <div className="inline-block animate-spin mr-2">
                  <RefreshCw className="w-5 h-5" />
                </div>
                Loading subnet data...
              </div>
            )}

            {/* Subnets Table - Now only showing main subnets */}
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="text-gray-500 text-xs border-b">
                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('id')}>
                      ID {getSortIcon('id')}
                    </th>
                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('cidr')}>
                      SUBNET {getSortIcon('cidr')}
                    </th>
                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('name')}>
                      NAME {getSortIcon('name')}
                    </th>
                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('utilizationPercent')}>
                      UTILIZATION {getSortIcon('utilizationPercent')}
                    </th>
                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('status')}>
                      STATUS {getSortIcon('status')}
                    </th>
                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('location')}>
                      LOCATION {getSortIcon('location')}
                    </th>
                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('category')}>
                      CATEGORY {getSortIcon('category')}
                    </th>
                    <th className="p-4 text-left font-medium">ACTIONS</th>
                  </tr>
                </thead>
                <tbody>
                {!isLoading && mainSubnets.length > 0 ? (
                    paginatedSubnets.map((subnet, index) => (
                      <tr
                        key={subnet.id}
                        className={`border-b ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-indigo-50 cursor-pointer`}
                        onClick={() => handleSubnetClick(subnet)}
                      >
                        <td className="p-4 font-medium text-indigo-700">{subnet.id}</td>
                        <td className="p-4 text-gray-700">{formatCidr(subnet.cidr)}</td>
                        <td className="p-4 text-gray-700">{subnet.name}</td>

                        <td className="p-4">
                          <div className="flex items-center">
                            {renderUtilizationBadge(subnet.utilizationPercent)}
                            <div className="ml-2 w-16 bg-gray-200 rounded-full h-1.5">
                              <div
                                className="bg-indigo-600 h-1.5 rounded-full"
                                style={{ width: `${subnet.utilizationPercent}%` }}
                              ></div>
                            </div>
                          </div>
                        </td>
                        <td className="p-4">{renderStatusBadge(subnet.status, subnet.assignedTo)}</td>
                        <td className="p-4 text-gray-700">{subnet.location}</td>
                        <td className="p-4 text-gray-700">{subnet.category}</td>
                        <td className="p-4" onClick={(e) => e.stopPropagation()}>
  <div className="flex space-x-2">
    <button
      className="p-1 text-gray-500 hover:text-indigo-700 transition-colors"
      title="View Details"
      onClick={(e) => {
        e.stopPropagation();
        handleSubnetClick(subnet);
      }}
    >
      <Eye className="w-4 h-4" />
    </button>

    {/* Only show Divide button for Available subnets */}
    {subnet.status === 'Available' && (
      <button
        className="p-1 text-gray-500 hover:text-indigo-700 transition-colors"
        title="Divide Subnet"
        onClick={(e) => {
          e.stopPropagation();
          openDivideSubnetModal(subnet);
        }}
      >
        <Divide className="w-4 h-4" />
      </button>
    )}

    {subnet.status === 'Available' ? (
      <button
        className="p-1 text-gray-500 hover:text-indigo-700 transition-colors"
        title="Assign to Server"
        onClick={(e) => {
          e.stopPropagation();
          openAssignSubnetModal(subnet);
        }}
      >
        <Link className="w-4 h-4" />
      </button>
    ) : subnet.status === 'Assigned' ? (
      <button
        className="p-1 text-gray-500 hover:text-indigo-700 transition-colors"
        title="Unassign from Server"
        onClick={(e) => {
          e.stopPropagation();
          handleUnassignSubnet(subnet.id);
        }}
      >
        <Unlink className="w-4 h-4" />
      </button>
    ) : (
      <button
        className="p-1 text-gray-400 cursor-not-allowed transition-colors"
        title={subnet.status === 'Unavailable' ?
          'Cannot assign: Has assigned descendants' :
          `Cannot assign. Status: ${subnet.status}`}
        disabled
      >
        <Link className="w-4 h-4" />
      </button>
    )}

    <button
      className="p-1 text-gray-500 hover:text-red-700 transition-colors"
      title="Delete Subnet"
      onClick={(e) => {
        e.stopPropagation();
        handleDeleteSubnet(subnet.id);
      }}
    >
      <Trash2 className="w-4 h-4" />
    </button>
  </div>
</td>
                      </tr>
                    ))
                  ) : (
                    !isLoading && (
                      <tr>
                        <td colSpan="9" className="p-4 text-center text-gray-500">
                          No subnets found matching your criteria
                        </td>
                      </tr>
                    )
                  )}
                </tbody>
              </table>
            </div>

            {/* Pagination - Updated to use mainSubnets.length for calculations */}
            {!isLoading && mainSubnets.length > 0 && (
              <div className="p-4 border-t flex justify-between items-center">
                <div className="text-sm text-gray-500">
                  Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, mainSubnets.length)} of {mainSubnets.length} subnets
                </div>
                <div className="flex space-x-1">
                  <button
                    className={`px-3 py-1 border rounded text-sm ${currentPage === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-500 hover:bg-gray-50'}`}
                    onClick={prevPage}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </button>

                  {/* Page numbers */}
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    // Show pages around current page
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        className={`px-3 py-1 border rounded text-sm ${currentPage === pageNum ? 'bg-indigo-700 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'}`}
                        onClick={() => goToPage(pageNum)}
                      >
                        {pageNum}
                      </button>
                    );
                  })}

                  <button
                    className={`px-3 py-1 border rounded text-sm ${currentPage === totalPages ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-500 hover:bg-gray-50'}`}
                    onClick={nextPage}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Subnet Details Modal - Modified to be more compact */}
      {selectedSubnet && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-[85vh] overflow-y-auto">
            <div className="p-3 border-b flex justify-between items-center">
              <h2 className="text-lg font-bold text-gray-800">Subnet Details</h2>
              <button
                onClick={closeSubnetDetails}
                className="text-gray-500 hover:text-gray-700"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>
            <div className="p-4 space-y-4">
              {/* Subnet Header - More compact */}
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center border-b pb-3">
            <div>
              <div className="text-xl font-bold text-gray-800">{selectedSubnet.name}</div>
              <div className="text-sm font-mono text-indigo-700">{selectedSubnet.cidr}</div>
            </div>
            {renderStatusBadge(selectedSubnet.status, selectedSubnet.assignedTo)}
          </div>

              {/* Subnet Information - More compact */}
              <div>
                <h3 className="text-md font-bold mb-1">Subnet Information</h3>
                <div className="bg-gray-50 rounded-md p-3">
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3 text-sm">

                    <div>
                      <div className="text-xs text-gray-500">Location</div>
                      <div className="font-medium">{selectedSubnet.location}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Gateway</div>
                      <div className="font-medium font-mono">{selectedSubnet.gateway}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Category</div>
                      <div className="font-medium">{selectedSubnet.category}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Assigned To</div>
                      <div className="font-medium">{selectedSubnet.assignedTo}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Last Updated</div>
                      <div className="font-medium">{selectedSubnet.lastUpdated}</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Utilization Details - More compact */}
              <div>
                <h3 className="text-md font-bold mb-1">Utilization</h3>
                <div className="bg-gray-50 rounded-md p-3">
                  <div className="flex items-center justify-between mb-1 text-sm">
                    <div className="font-medium">
                      {selectedSubnet.usedIPs} of {selectedSubnet.totalIPs} IP addresses used
                    </div>
                    <div className="font-medium">
                      {selectedSubnet.utilizationPercent}% utilized
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div
                      className={`h-2 rounded-full ${
                        selectedSubnet.utilizationPercent >= 90 ? 'bg-red-600' :
                        selectedSubnet.utilizationPercent >= 75 ? 'bg-yellow-500' :
                        selectedSubnet.utilizationPercent >= 50 ? 'bg-blue-600' :
                        'bg-green-500'
                      }`}
                      style={{ width: `${selectedSubnet.utilizationPercent}%` }}
                    ></div>
                  </div>

                  <div className="text-xs text-gray-600">
                    <strong>Available:</strong> {selectedSubnet.totalIPs - selectedSubnet.usedIPs} addresses
                  </div>

                  <div className="flex flex-wrap gap-2 mt-2">
                    {selectedSubnet.utilizationPercent > 80 && (
                      <div className="bg-yellow-50 text-yellow-800 text-xs px-2 py-0.5 rounded-md flex items-center">
                        <AlertCircle className="w-3 h-3 mr-1" />
                        High utilization
                      </div>
                    )}
                    {selectedSubnet.utilizationPercent > 95 && (
                      <div className="bg-red-50 text-red-800 text-xs px-2 py-0.5 rounded-md flex items-center">
                        <AlertCircle className="w-3 h-3 mr-1" />
                        Critical - consider expanding
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Description - More compact */}
              <div>
                <h3 className="text-md font-bold mb-1">Description</h3>
                <div className="bg-gray-50 rounded-md p-3">
                  <p className="text-xs text-gray-700">{selectedSubnet.description}</p>
                </div>
              </div>

              {/* Subnet Hierarchy - More compact */}
              <div>
  <h3 className="text-xs font-medium text-gray-700 mb-1 flex items-center">
    <Network className="w-4 h-4 mr-1 text-indigo-700" />
    Subnet Hierarchy
  </h3>
  <CompactSubnetTree
    subnets={allSubnets} // Pass allSubnets instead of filtered subnets
    currentSubnetId={selectedSubnet.id}
    onSubnetSelect={(subnet) => {
      closeSubnetDetails();
      setSelectedSubnet(subnet);
    }}
  />
</div>



{/* Updated Subnet Actions in the detail modal */}
<div className="flex flex-wrap gap-2 justify-end pt-3 border-t">
  {/* Only show Divide button for Available subnets */}
  {selectedSubnet.status === 'Available' && (
    <button
      className="px-3 py-1 border border-gray-300 rounded-md text-xs text-gray-700 flex items-center hover:bg-gray-50"
      onClick={() => {
        closeSubnetDetails();
        openDivideSubnetModal(selectedSubnet);
      }}
    >
      <Divide className="w-3 h-3 mr-1" />
      Divide Subnet
    </button>
  )}

  {selectedSubnet.status === 'Available' ? (
    <button
      className="px-3 py-1 border border-gray-300 rounded-md text-xs text-gray-700 flex items-center hover:bg-gray-50"
      onClick={() => {
        closeSubnetDetails();
        openAssignSubnetModal(selectedSubnet);
      }}
    >
      <Link className="w-3 h-3 mr-1" />
      Assign to Server
    </button>
  ) : selectedSubnet.status === 'Assigned' ? (
    <button
      className="px-3 py-1 border border-gray-300 rounded-md text-xs text-gray-700 flex items-center hover:bg-gray-50"
      onClick={() => {
        closeSubnetDetails();
        handleUnassignSubnet(selectedSubnet.id);
      }}
    >
      <Unlink className="w-3 h-3 mr-1" />
      Unassign from Server
    </button>
  ) : selectedSubnet.status === 'Unavailable' ? (
    <div className="px-3 py-1 bg-orange-50 border border-orange-200 rounded-md text-xs text-orange-800 flex items-center">
      <AlertCircle className="w-3 h-3 mr-1" />
      Cannot assign: This subnet has assigned descendants
    </div>
  ) : (
    <button
      className="px-3 py-1 border border-gray-300 rounded-md text-xs text-gray-400 cursor-not-allowed flex items-center"
      disabled
    >
      <AlertCircle className="w-3 h-3 mr-1" />
      Cannot Modify
    </button>
  )}

  <button
    className="px-3 py-1 bg-indigo-700 text-white rounded-md text-xs flex items-center hover:bg-indigo-800"
    onClick={() => {
      closeSubnetDetails();
      openAssignSubnetModal(selectedSubnet);
    }}
  >
    <Server className="w-3 h-3 mr-1" />
    Manage IP Allocations
  </button>
</div>
            </div>
          </div>
        </div>
      )}

      {/* Add Subnet Modal */}
      {isAddModalOpen && (
        <AddSubnetModal
          onClose={closeAddSubnetModal}
          onAddSubnet={handleAddSubnet}
        />
      )}

      {/* Divide Subnet Modal */}
      {isDivideModalOpen && selectedSubnet && (
        <DivideSubnetModal
          subnet={selectedSubnet}
          onClose={closeDivideSubnetModal}
          onDivideSubnet={handleDivideSubnet}
        />
      )}

      {/* Assign Subnet Modal */}
      {isAssignModalOpen && selectedSubnet && (
        <AssignSubnetModal
          subnet={selectedSubnet}
          onClose={closeAssignSubnetModal}
          onAssignSubnet={handleAssignSubnet}
        />
      )}

    </div>

  );
};

export default SubnetsPage;