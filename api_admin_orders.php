<?php
require_once("auth_functions.php");
require_once("invoice_email_functions.php");


// Function to safely encode to JSON
function safeJsonEncode($data) {
  $json = json_encode($data);

  // If JSON encoding fails, return an empty array
  if ($json === false) {
    return '[]';
  }

  return $json;
}

function getServiceDetails($pdo, $serviceId) {
  try {
    if (empty($serviceId)) {
      return [
        'success' => false,
        'error' => 'Service ID is required'
      ];
    }

    // First, get basic service data from orders_items table
    $serviceStmt = $pdo->prepare("
      SELECT oi.*,
        o.status as order_status, o.order_type, o.label as order_label,
        o.order_date, o.expiration_date
      FROM orders_items oi
      LEFT JOIN orders o ON oi.order_id = o.id
      WHERE oi.id = :service_id
      LIMIT 1
    ");

    $serviceStmt->bindValue(':service_id', $serviceId);
    $serviceStmt->execute();

    $service = $serviceStmt->fetch(PDO::FETCH_ASSOC);

    if (!$service) {
      return [
        'success' => false,
        'error' => "Service with ID $serviceId not found"
      ];
    }

    // Get CPU details if available
    if (!empty($service['cpu_id'])) {
      $cpuStmt = $pdo->prepare("
        SELECT * FROM dedicated_cpu
        WHERE id = :cpu_id
        LIMIT 1
      ");
      $cpuStmt->bindValue(':cpu_id', $service['cpu_id']);
      $cpuStmt->execute();
      $cpu = $cpuStmt->fetch(PDO::FETCH_ASSOC);

      if ($cpu) {
        $service['cpu'] = isset($cpu['cpu']) ? $cpu['cpu'] :
                         (isset($cpu['name']) ? $cpu['name'] : 'CPU #' . $service['cpu_id']);
        $service['cpu_details'] = $cpu;
      }
    }

    // Get Storage details if available
    if (!empty($service['storage_id'])) {
      $storageStmt = $pdo->prepare("
        SELECT * FROM dedicated_storages
        WHERE id = :storage_id
        LIMIT 1
      ");
      $storageStmt->bindValue(':storage_id', $service['storage_id']);
      $storageStmt->execute();
      $storage = $storageStmt->fetch(PDO::FETCH_ASSOC);

      if ($storage) {
        $service['storage'] = isset($storage['name']) ? $storage['name'] :
                             (isset($storage['description']) ? $storage['description'] : 'Storage #' . $service['storage_id']);
        $service['storage_details'] = $storage;
      }
    }

    // Get Bandwidth details if available
    if (!empty($service['bandwidth_id'])) {
      $bandwidthStmt = $pdo->prepare("
        SELECT * FROM dedicated_bandwidth
        WHERE id = :bandwidth_id
        LIMIT 1
      ");
      $bandwidthStmt->bindValue(':bandwidth_id', $service['bandwidth_id']);
      $bandwidthStmt->execute();
      $bandwidth = $bandwidthStmt->fetch(PDO::FETCH_ASSOC);

      if ($bandwidth) {
        $service['bandwidth'] = isset($bandwidth['name']) ? $bandwidth['name'] :
                               (isset($bandwidth['description']) ? $bandwidth['description'] : 'Bandwidth #' . $service['bandwidth_id']);
        $service['bandwidth_details'] = $bandwidth;
      }
    }

    // Get Location details if available
    if (!empty($service['location_id'])) {
      $locationStmt = $pdo->prepare("
        SELECT c.*, co.city as country_name
        FROM cities c
        LEFT JOIN cities co ON c.city = co.id
        WHERE c.id = :location_id
        LIMIT 1
      ");
      $locationStmt->bindValue(':location_id', $service['location_id']);
      $locationStmt->execute();
      $location = $locationStmt->fetch(PDO::FETCH_ASSOC);

      if ($location) {
        $locationName = '';
        if (!empty($location['city'])) {
          $locationName = $location['city'];
          if (!empty($location['country_name'])) {
            $locationName .= ', ' . $location['country_name'];
          }
        } else if (!empty($location['name'])) {
          $locationName = $location['name'];
        } else {
          $locationName = 'Location #' . $service['location_id'];
        }

        $service['location'] = $locationName;
        $service['location_details'] = $location;
      }
    }

    // Get Subnet details if available
    if (!empty($service['subnet_id'])) {
      $subnetStmt = $pdo->prepare("
        SELECT * FROM dedicated_subnets
        WHERE id = :subnet_id
        LIMIT 1
      ");
      $subnetStmt->bindValue(':subnet_id', $service['subnet_id']);
      $subnetStmt->execute();
      $subnet = $subnetStmt->fetch(PDO::FETCH_ASSOC);

      if ($subnet) {
        $service['subnet'] = isset($subnet['name']) ? $subnet['name'] : 'Subnet #' . $service['subnet_id'];
        $service['subnet_details'] = $subnet;
      }
    }

    // If server_id exists, try to get server details
    if (!empty($service['server_id'])) {
      try {
        $serverStmt = $pdo->prepare("
          SELECT * FROM inventory_dedicated_servers
          WHERE id = :server_id
          LIMIT 1
        ");
        $serverStmt->bindValue(':server_id', $service['server_id']);
        $serverStmt->execute();
        $server = $serverStmt->fetch(PDO::FETCH_ASSOC);

        if ($server) {
          $service['server_details'] = $server;
        }
      } catch (Exception $e) {
        error_log("Error getting server details: " . $e->getMessage());
        // Continue execution even if server details can't be fetched
      }
    }

    // Set status to order status if not already set
    if (empty($service['status']) && !empty($service['order_status'])) {
      $service['status'] = $service['order_status'];
    } else if (empty($service['status'])) {
      $service['status'] = 'Pending'; // Default status is now Pending instead of Active
    }

    // Format prices for display
    if (isset($service['order_price'])) {
      $service['price'] = '€' . number_format($service['order_price'], 2, '.', ',');
    }

    if (isset($service['requirement_price'])) {
      $service['recurring_price'] = '€' . number_format($service['requirement_price'], 2, '.', ',');
    }

    // Set default hostname if not specified
    if (empty($service['hostname'])) {
      $service['hostname'] = 'srv-' . $service['id'] . '.example.com';
    }

    // Add service type if not specified
    if (empty($service['type'])) {
      if (!empty($service['order_type'])) {
        $service['type'] = $service['order_type'];
      } else {
        $service['type'] = 'Dedicated Server'; // Default type
      }
    }

    // Add description if not specified
    if (empty($service['description'])) {
      $cpuDesc = $service['cpu'] ?? 'CPU';
      $storageDesc = $service['storage'] ?? 'Storage';
      $bandwidthDesc = $service['bandwidth'] ?? 'Bandwidth';

      $service['description'] = "Server with $cpuDesc, $storageDesc, $bandwidthDesc";
    }

    return [
      'success' => true,
      'service' => $service
    ];
  } catch (Exception $e) {
    error_log("Error in getServiceDetails: " . $e->getMessage());
    return [
      'success' => false,
      'error' => 'Failed to get service details: ' . $e->getMessage()
    ];
  }
}


// Helper functions for debugging
function getOrderCount($pdo, $userId) {
  $stmt = $pdo->prepare("SELECT COUNT(*) FROM orders WHERE owner_id = :user_id");
  $stmt->bindValue(':user_id', $userId);
  $stmt->execute();
  return $stmt->fetchColumn();
}

function getItemCount($pdo, $userId) {
  $stmt = $pdo->prepare("SELECT COUNT(*) FROM orders_items WHERE user_id = :user_id");
  $stmt->bindValue(':user_id', $userId);
  $stmt->execute();
  return $stmt->fetchColumn();
}

function checkTableExists($pdo, $tableName) {
  $stmt = $pdo->query("SHOW TABLES LIKE '$tableName'");
  return $stmt->rowCount() > 0;
}

function getSampleOrders($pdo, $userId) {
  $stmt = $pdo->prepare("SELECT id, owner_id, status, order_date FROM orders WHERE owner_id = :user_id LIMIT 5");
  $stmt->bindValue(':user_id', $userId);
  $stmt->execute();
  return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getSampleItems($pdo, $userId) {
  $stmt = $pdo->prepare("SELECT id, user_id, order_id, cpu_id, created_at FROM orders_items WHERE user_id = :user_id LIMIT 5");
  $stmt->bindValue(':user_id', $userId);
  $stmt->execute();
  return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getTableStructure($pdo, $tableName) {
  try {
    $stmt = $pdo->query("DESCRIBE $tableName");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
  } catch (Exception $e) {
    return ['error' => $e->getMessage()];
  }
}

function getDirectQuery($pdo, $userId) {
  try {
    // Try a direct query with minimal joins
    $sql = "SELECT oi.id, oi.user_id, oi.order_id, o.status
            FROM orders_items oi
            LEFT JOIN orders o ON oi.order_id = o.id
            WHERE oi.user_id = :user_id";
    $stmt = $pdo->prepare($sql);
    $stmt->bindValue(':user_id', $userId);
    $stmt->execute();
    $result = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Also check orders table directly
    $ordersSql = "SELECT id, owner_id, status FROM orders WHERE owner_id = :user_id";
    $ordersStmt = $pdo->prepare($ordersSql);
    $ordersStmt->bindValue(':user_id', $userId);
    $ordersStmt->execute();
    $orders = $ordersStmt->fetchAll(PDO::FETCH_ASSOC);

    return [
      'direct_items' => $result,
      'direct_orders' => $orders
    ];
  } catch (Exception $e) {
    return ['error' => $e->getMessage()];
  }
}

/**
 * Get upgrade orders from the orders_items table
 */
function getUpgradeOrders($pdo, $filters = []) {
  try {
    // Build query to get upgrade orders from orders_items table
    $sql = "SELECT oi.id, oi.order_id, oi.user_id, oi.type, oi.server_id, oi.cpu_id,
            oi.bandwidth_id, oi.location_id, oi.storage_id, oi.subnet_id, oi.additional_ips_id,
            oi.order_price, oi.requirement_price, oi.payment_period, oi.created_at,
            oi.hostname, oi.date_ordered, oi.due_date, oi.status,
            u.first_name, u.last_name, u.company_name, u.email, u.city, u.country,
            o.label as order_label, o.order_type, o.order_date, o.expiration_date,
            o.initial_price as order_initial_price, o.recurring_price as order_recurring_price,
            o.status as order_status
            FROM orders_items oi
            LEFT JOIN users u ON oi.user_id = u.id
            LEFT JOIN orders o ON oi.order_id = o.id
            WHERE oi.type = 'upgrade'";

    // Add search filter
    if (!empty($filters['search'])) {
      $searchTerm = '%' . $filters['search'] . '%';
      $sql .= " AND (oi.hostname LIKE :search
                OR u.first_name LIKE :search
                OR u.last_name LIKE :search
                OR u.company_name LIKE :search
                OR u.email LIKE :search
                OR o.label LIKE :search)";
    }

    // Add status filter
    if (!empty($filters['status']) && $filters['status'] !== 'All') {
      $sql .= " AND oi.status = :status";
    }

    // Add sorting
    $sortField = isset($filters['sortField']) ? $filters['sortField'] : 'created_at';
    $sortDirection = isset($filters['sortDirection']) ? $filters['sortDirection'] : 'desc';

    // Map sort fields to actual column names
    $sortMapping = [
      'date' => 'oi.created_at',
      'customerName' => 'u.company_name',
      'amount' => 'oi.order_price',
      'status' => 'oi.status',
      'hostname' => 'oi.hostname'
    ];

    $actualSortField = isset($sortMapping[$sortField]) ? $sortMapping[$sortField] : 'oi.created_at';
    $sql .= " ORDER BY $actualSortField $sortDirection";

    $stmt = $pdo->prepare($sql);

    // Bind parameters
    if (!empty($filters['search'])) {
      $stmt->bindValue(':search', $searchTerm);
    }
    if (!empty($filters['status']) && $filters['status'] !== 'All') {
      $stmt->bindValue(':status', $filters['status']);
    }

    $stmt->execute();
    $upgradeOrders = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Format the data for frontend consumption
    $formattedOrders = [];
    foreach ($upgradeOrders as $order) {
      $customerName = trim(($order['first_name'] ?? '') . ' ' . ($order['last_name'] ?? ''));
      if (empty($customerName) && !empty($order['company_name'])) {
        $customerName = $order['company_name'];
      }
      if (empty($customerName)) {
        $customerName = 'Unknown Customer';
      }

      $formattedOrders[] = [
        'id' => 'UPG-' . $order['id'],
        'order_id' => $order['order_id'],
        'customerName' => $customerName,
        'customerEmail' => $order['email'] ?? '',
        'date' => $order['created_at'] ? date('Y-m-d', strtotime($order['created_at'])) : date('Y-m-d'),
        'amount' => '€' . number_format((float)($order['order_price'] ?? 0), 2),
        'status' => $order['status'] ?? 'Pending',
        'paymentStatus' => 'Pending', // Default for upgrades
        'location' => $order['city'] ?? 'Unknown',
        'items' => 1,
        'hostname' => $order['hostname'] ?? '',
        'type' => 'Upgrade',
        'server_id' => $order['server_id'],
        'cpu_id' => $order['cpu_id'],
        'bandwidth_id' => $order['bandwidth_id'],
        'storage_id' => $order['storage_id'],
        'subnet_id' => $order['subnet_id'],
        'additional_ips_id' => $order['additional_ips_id'],
        'payment_period' => $order['payment_period'] ?? 'monthly'
      ];
    }

    return $formattedOrders;

  } catch (Exception $e) {
    error_log("Error fetching upgrade orders: " . $e->getMessage());
    return [];
  }
}

/**
 * Create sample upgrade orders for testing
 */
function createSampleUpgradeOrders($pdo) {
  try {
    // First, ensure we have some users and orders to work with
    $userStmt = $pdo->prepare("SELECT id FROM users LIMIT 3");
    $userStmt->execute();
    $users = $userStmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($users)) {
      return [
        'success' => false,
        'error' => 'No users found in database'
      ];
    }

    $orderStmt = $pdo->prepare("SELECT id FROM orders LIMIT 3");
    $orderStmt->execute();
    $orders = $orderStmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($orders)) {
      return [
        'success' => false,
        'error' => 'No orders found in database'
      ];
    }

    // Create sample upgrade orders
    $sampleUpgrades = [
      [
        'order_id' => $orders[0]['id'],
        'user_id' => $users[0]['id'],
        'type' => 'upgrade',
        'hostname' => 'srv-001.example.com',
        'order_price' => 25.00,
        'requirement_price' => 25.00,
        'payment_period' => 'monthly',
        'status' => 'Pending'
      ],
      [
        'order_id' => isset($orders[1]) ? $orders[1]['id'] : $orders[0]['id'],
        'user_id' => isset($users[1]) ? $users[1]['id'] : $users[0]['id'],
        'type' => 'upgrade',
        'hostname' => 'srv-002.example.com',
        'order_price' => 50.00,
        'requirement_price' => 50.00,
        'payment_period' => 'monthly',
        'status' => 'Processing'
      ],
      [
        'order_id' => isset($orders[2]) ? $orders[2]['id'] : $orders[0]['id'],
        'user_id' => isset($users[2]) ? $users[2]['id'] : $users[0]['id'],
        'type' => 'upgrade',
        'hostname' => 'srv-003.example.com',
        'order_price' => 75.00,
        'requirement_price' => 75.00,
        'payment_period' => 'monthly',
        'status' => 'Completed'
      ]
    ];

    $insertStmt = $pdo->prepare("
      INSERT INTO orders_items (
        order_id, user_id, type, hostname, order_price, requirement_price,
        payment_period, status, created_at, date_ordered, due_date
      ) VALUES (
        :order_id, :user_id, :type, :hostname, :order_price, :requirement_price,
        :payment_period, :status, NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 1 MONTH)
      )
    ");

    $created = 0;
    foreach ($sampleUpgrades as $upgrade) {
      try {
        $insertStmt->execute($upgrade);
        $created++;
      } catch (Exception $e) {
        error_log("Error creating sample upgrade: " . $e->getMessage());
      }
    }

    return [
      'success' => true,
      'message' => "Created $created sample upgrade orders"
    ];

  } catch (Exception $e) {
    error_log("Error creating sample upgrade orders: " . $e->getMessage());
    return [
      'success' => false,
      'error' => $e->getMessage()
    ];
  }
}

/**
 * Get orders from the database with optimized query
 */
function getAllOrders($pdo, $filters = []) {
  try {
    // Build optimized query with joins that includes invoice data to avoid separate queries
    $sql = "SELECT o.id, o.label, o.owner_id, o.order_type, o.order_date, o.expiration_date,
            o.initial_price, o.recurring_price, o.status,
            u.first_name, u.last_name, u.company_name, u.email, u.city, u.country,
            i.id as invoice_id, i.status as invoice_status, i.payment_method,
            (SELECT COUNT(*) FROM invoice_items WHERE invoice_id = i.id) as items_count
            FROM orders o
            LEFT JOIN users u ON o.owner_id = u.id
            LEFT JOIN invoices i ON o.id = i.order_id AND i.id = (SELECT MAX(id) FROM invoices WHERE order_id = o.id)
            WHERE 1=1";

    $params = [];

    // Apply status filter
    if (!empty($filters['status']) && $filters['status'] != 'All') {
      $sql .= " AND o.status = :status";
      $params[':status'] = $filters['status'];
    }

    // Apply search filter
    if (!empty($filters['search'])) {
      $sql .= " AND (o.id LIKE :search OR o.label LIKE :search OR u.company_name LIKE :search
                OR u.first_name LIKE :search OR u.last_name LIKE :search)";
      $params[':search'] = '%' . $filters['search'] . '%';
    }

    // Apply sort
    $sortField = isset($filters['sortField']) ? $filters['sortField'] : 'date';
    $sortDir = isset($filters['sortDirection']) ? $filters['sortDirection'] : 'desc';

    // Map frontend sort fields to database columns
    $sortFieldMap = [
      'id' => 'o.id',
      'customerName' => 'COALESCE(u.company_name, CONCAT(u.first_name, " ", u.last_name))',
      'date' => 'o.order_date',
      'amount' => 'o.initial_price',
      'status' => 'o.status',
      'location' => 'u.city'
    ];

    $dbSortField = isset($sortFieldMap[$sortField]) ? $sortFieldMap[$sortField] : 'o.order_date';
    $sql .= " ORDER BY $dbSortField " . ($sortDir === 'asc' ? 'ASC' : 'DESC');

    // Execute query
    $stmt = $pdo->prepare($sql);
    foreach ($params as $key => $value) {
      $stmt->bindValue($key, $value);
    }
    $stmt->execute();

    // Format results
    $orders = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
      // Format customer name
      $customerName = !empty($row['company_name']) ? $row['company_name'] : trim($row['first_name'] . ' ' . $row['last_name']);
      if (empty($customerName)) {
        $customerName = 'Customer #' . $row['owner_id'];
      }

      // Format payment status from invoice data (already fetched in the main query)
      $paymentStatus = !empty($row['invoice_status']) ? $row['invoice_status'] : 'Pending';

      // Format location - only use actual data
      $location = '';
      if (!empty($row['city'])) {
        $location = $row['city'];
        if (!empty($row['country'])) {
          $location .= ', ' . $row['country'];
        }
      }

      // Format amount
      $amount = '€' . number_format($row['initial_price'] ?: 0, 2, '.', ',');

      // Get items count from the query result
      $itemsCount = $row['items_count'] ?: 1; // At least 1 item

      // Add to orders array
      $orderData = [
        'id' => '#ORD-' . str_pad($row['id'], 4, '0', STR_PAD_LEFT),
        'rawId' => $row['id'],
        'customerName' => $customerName,
        'date' => date('Y-m-d', strtotime($row['order_date'])),
        'amount' => $amount,
        'status' => $row['status'] ?: 'Pending',
        'paymentStatus' => $paymentStatus,
        'items' => $itemsCount,
      ];

      // Only add fields that have actual data
      if (!empty($row['payment_method'])) {
        $orderData['paymentMethod'] = $row['payment_method'];
      }

      if (!empty($location)) {
        $orderData['location'] = $location;
      }

      if (!empty($row['label'])) {
        $orderData['label'] = $row['label'];
      }

      if (!empty($row['order_type'])) {
        $orderData['order_type'] = $row['order_type'];
      }

      if (!empty($row['expiration_date'])) {
        $orderData['expiration_date'] = $row['expiration_date'];
      }

      if (!empty($row['recurring_price'])) {
        $orderData['recurring_price'] = $row['recurring_price'];
      }

      if (!empty($row['owner_id'])) {
        $orderData['owner_id'] = $row['owner_id'];
      }

      $orders[] = $orderData;
    }

    return $orders;
  } catch (Exception $e) {
    error_log("Database error in getAllOrders: " . $e->getMessage());
    return [];
  }
}

/**
 * Get invoice for a specific order
 */
function getInvoiceForOrder($pdo, $orderId) {
  try {
    $stmt = $pdo->prepare("SELECT * FROM invoices WHERE order_id = :order_id ORDER BY id DESC LIMIT 1");
    $stmt->bindValue(':order_id', $orderId);
    $stmt->execute();
    return $stmt->fetch(PDO::FETCH_ASSOC);
  } catch (Exception $e) {
    error_log("Database error in getInvoiceForOrder: " . $e->getMessage());
    return null;
  }
}

/**
 * Get order statistics
 */
function getOrderStats($pdo) {
  try {
    // Get total orders
    $totalOrdersStmt = $pdo->query("SELECT COUNT(*) as count FROM orders");
    $totalOrders = $totalOrdersStmt->fetch(PDO::FETCH_ASSOC)['count'] ?: 0;

    // Get completed orders
    $completedStmt = $pdo->prepare("SELECT COUNT(*) as count FROM orders WHERE status = 'Completed'");
    $completedStmt->execute();
    $completedOrders = $completedStmt->fetch(PDO::FETCH_ASSOC)['count'] ?: 0;

    // Get pending orders
    $pendingStmt = $pdo->prepare("SELECT COUNT(*) as count FROM orders WHERE status = 'Pending'");
    $pendingStmt->execute();
    $pendingOrders = $pendingStmt->fetch(PDO::FETCH_ASSOC)['count'] ?: 0;

    // Get cancelled orders
    $cancelledStmt = $pdo->prepare("SELECT COUNT(*) as count FROM orders WHERE status = 'Cancelled'");
    $cancelledStmt->execute();
    $cancelledOrders = $cancelledStmt->fetch(PDO::FETCH_ASSOC)['count'] ?: 0;

    // Last month for comparison
    $lastMonth = date('Y-m-d', strtotime('-1 month'));

    // Last month counts for each status
    $lastMonthStmt = $pdo->prepare("SELECT COUNT(*) as count FROM orders WHERE order_date < :lastMonth");
    $lastMonthStmt->bindValue(':lastMonth', $lastMonth);
    $lastMonthStmt->execute();
    $lastMonthTotal = $lastMonthStmt->fetch(PDO::FETCH_ASSOC)['count'] ?: 1; // Avoid division by zero

    // Last month completed
    $lastMonthCompletedStmt = $pdo->prepare("SELECT COUNT(*) as count FROM orders WHERE status = 'Completed' AND order_date < :lastMonth");
    $lastMonthCompletedStmt->bindValue(':lastMonth', $lastMonth);
    $lastMonthCompletedStmt->execute();
    $lastMonthCompleted = $lastMonthCompletedStmt->fetch(PDO::FETCH_ASSOC)['count'] ?: 1;

    // Last month pending
    $lastMonthPendingStmt = $pdo->prepare("SELECT COUNT(*) as count FROM orders WHERE status = 'Pending' AND order_date < :lastMonth");
    $lastMonthPendingStmt->bindValue(':lastMonth', $lastMonth);
    $lastMonthPendingStmt->execute();
    $lastMonthPending = $lastMonthPendingStmt->fetch(PDO::FETCH_ASSOC)['count'] ?: 1;

    // Last month cancelled
    $lastMonthCancelledStmt = $pdo->prepare("SELECT COUNT(*) as count FROM orders WHERE status = 'Cancelled' AND order_date < :lastMonth");
    $lastMonthCancelledStmt->bindValue(':lastMonth', $lastMonth);
    $lastMonthCancelledStmt->execute();
    $lastMonthCancelled = $lastMonthCancelledStmt->fetch(PDO::FETCH_ASSOC)['count'] ?: 1;

    // Calculate changes
    $totalChange = round(($totalOrders - $lastMonthTotal) / $lastMonthTotal * 100, 1);
    $totalChange = ($totalChange >= 0 ? '+' : '') . $totalChange . '%';

    $completedChange = round(($completedOrders - $lastMonthCompleted) / $lastMonthCompleted * 100, 1);
    $completedChange = ($completedChange >= 0 ? '+' : '') . $completedChange . '%';

    $pendingChange = round(($pendingOrders - $lastMonthPending) / $lastMonthPending * 100, 1);
    $pendingChange = ($pendingChange >= 0 ? '+' : '') . $pendingChange . '%';

    $cancelledChange = round(($cancelledOrders - $lastMonthCancelled) / $lastMonthCancelled * 100, 1);
    $cancelledChange = ($cancelledChange >= 0 ? '+' : '') . $cancelledChange . '%';

    // Create stats array with actual calculated changes
    return [
      [
        'title' => 'Total Orders',
        'value' => (string)$totalOrders,
        'change' => $totalChange,
        'period' => 'vs last month',
        'icon_class' => 'text-indigo-700'
      ],
      [
        'title' => 'Completed',
        'value' => (string)$completedOrders,
        'change' => $completedChange,
        'period' => 'vs last month',
        'icon_class' => 'text-success'
      ],
      [
        'title' => 'Pending',
        'value' => (string)$pendingOrders,
        'change' => $pendingChange,
        'period' => 'vs last month',
        'icon_class' => 'text-warning'
      ],
      [
        'title' => 'Cancelled',
        'value' => (string)$cancelledOrders,
        'change' => $cancelledChange,
        'period' => 'vs last month',
        'icon_class' => 'text-danger'
      ]
    ];
  } catch (Exception $e) {
    error_log("Database error in getOrderStats: " . $e->getMessage());

    // Return empty stats array if DB query fails
    return [
      [
        'title' => 'Total Orders',
        'value' => '0',
        'change' => '0.0%',
        'period' => 'vs last month',
        'icon_class' => 'text-indigo-700'
      ],
      [
        'title' => 'Completed',
        'value' => '0',
        'change' => '0.0%',
        'period' => 'vs last month',
        'icon_class' => 'text-success'
      ],
      [
        'title' => 'Pending',
        'value' => '0',
        'change' => '0.0%',
        'period' => 'vs last month',
        'icon_class' => 'text-warning'
      ],
      [
        'title' => 'Cancelled',
        'value' => '0',
        'change' => '0.0%',
        'period' => 'vs last month',
        'icon_class' => 'text-danger'
      ]
    ];
  }
}

/**
 * Create a new order in the database
 */
function createOrder($pdo, $adminId, $data) {
  try {
    // Make sure there's no active transaction before starting a new one
    if ($pdo->inTransaction()) {
      error_log("Warning: There is already an active transaction. Rolling back before starting a new one.");
      $pdo->rollBack();
    }

    // Begin transaction
    $pdo->beginTransaction();
    error_log("Started transaction for order creation");

    // Validate required data
    if (empty($data['customerName']) && empty($data['customer_id'])) {
      throw new Exception("Customer information is required");
    }

    // Get customer ID
    $userId = !empty($data['customer_id']) ? $data['customer_id'] : null;
    error_log("Initial user ID from customer_id: " . ($userId ?? 'null'));

    // If no customer ID but we have a name, try to find or create the customer
    if (!$userId && !empty($data['customerName'])) {
      // Try to find user by name
      $findStmt = $pdo->prepare("SELECT id FROM users WHERE company_name = :name OR CONCAT(first_name, ' ', last_name) = :name LIMIT 1");
      $findStmt->bindValue(':name', $data['customerName']);
      $findStmt->execute();
      $user = $findStmt->fetch(PDO::FETCH_ASSOC);

      if ($user) {
        $userId = $user['id'];
      } else {
        // Create new user if not found
        $createStmt = $pdo->prepare("INSERT INTO users (company_name, email, created_at) VALUES (:company, :email, NOW())");
        $createStmt->bindValue(':company', $data['customerName']);
        $createStmt->bindValue(':email', $data['clientEmail'] ?? '');
        $createStmt->execute();
        $userId = $pdo->lastInsertId();
      }
    }

    if (!$userId) {
      throw new Exception("Could not identify or create customer");
    }

    error_log("Final user ID for order: $userId");

    // Save location data if provided
    if (!empty($data['location'])) {
      // Parse location (assuming format like "City, Country")
      $locationParts = explode(',', $data['location']);
      $city = trim($locationParts[0]);
      $country = isset($locationParts[1]) ? trim($locationParts[1]) : '';

      // Update user's location if needed
      if (!empty($city)) {
        $locationStmt = $pdo->prepare("
          UPDATE users
          SET city = :city, country = :country
          WHERE id = :user_id
        ");
        $locationStmt->bindValue(':city', $city);
        $locationStmt->bindValue(':country', $country);
        $locationStmt->bindValue(':user_id', $userId);
        $locationStmt->execute();
      }
    }

    // Generate a server ID and password
    $serverId = rand(1, 100); // In a real app, this would be selected from available servers
    $rootPassword = generateRandomPassword();

    // Insert the order
    $orderStmt = $pdo->prepare("
      INSERT INTO orders (
        label,
        owner_id,
        placed_by_id,
        order_type,
        order_date,
        expiration_date,
        initial_price,
        recurring_price,
        assigned_server_id,
        assigned_server_type,
        root_password,
        status
      ) VALUES (
        :label,
        :owner_id,
        :placed_by_id,
        :order_type,
        NOW(),
        DATE_ADD(NOW(), INTERVAL 1 YEAR),
        :initial_price,
        :recurring_price,
        :assigned_server_id,
        :assigned_server_type,
        :root_password,
        'Pending'
      )
    ");

    $orderStmt->bindValue(':label', $data['orderName'] ?? 'New Order');
    $orderStmt->bindValue(':owner_id', $userId);
    $orderStmt->bindValue(':placed_by_id', $adminId);
    $orderStmt->bindValue(':order_type', $data['orderType'] ?? 'Standard');
    $orderStmt->bindValue(':initial_price', $data['initialPrice'] ?? 0);
    $orderStmt->bindValue(':recurring_price', $data['recurringPrice'] ?? 0);
    $orderStmt->bindValue(':assigned_server_id', $serverId);
    $orderStmt->bindValue(':assigned_server_type', $data['serverType'] ?? 'VPS');
    $orderStmt->bindValue(':root_password', $rootPassword);

    $orderStmt->execute();
    $orderId = $pdo->lastInsertId();

    // Create invoice for this order (simplified)
    $invoiceStmt = $pdo->prepare("
      INSERT INTO invoices (
        user_id,
        order_id,
        type,
        value,
        subtotal,
        tax,
        date,
        due_date,
        payment_method,
        status,
        proforma_number,
        description
      ) VALUES (
        :user_id,
        :order_id,
        'proforma',
        :value,
        :subtotal,
        :tax,
        NOW(),
        NOW(),
        'Bank Transfer',
        'Pending',
        :proforma_number,
        :description
      )
    ");

    // Get the next available proforma number
    $proformaStmt = $pdo->prepare("SELECT MAX(CAST(proforma_number AS UNSIGNED)) as max_number FROM invoices");
    $proformaStmt->execute();
    $maxNumber = $proformaStmt->fetch(PDO::FETCH_ASSOC)['max_number'];
    error_log("Last proforma number: " . ($maxNumber ?? 'NULL'));
    $proformaNumber = $maxNumber ? intval($maxNumber) + 1 : 1000;
    error_log("Generated new proforma number: $proformaNumber");
    $subtotal = $data['initialPrice'] ?? 0;
    $tax = $subtotal * 0.19; // 19% VAT
    $total = $subtotal + $tax;

    // Create a description that includes the order ID and order note
    $invoiceDescription = "Order #$orderId";
    if (!empty($data['orderNote'])) {
      $invoiceDescription .= ": " . $data['orderNote'];
    }

    $invoiceStmt->bindValue(':user_id', $userId);
    $invoiceStmt->bindValue(':order_id', $orderId);
    $invoiceStmt->bindValue(':value', $total);
    $invoiceStmt->bindValue(':subtotal', $subtotal);
    $invoiceStmt->bindValue(':tax', $tax);
    $invoiceStmt->bindValue(':proforma_number', $proformaNumber);
    $invoiceStmt->bindValue(':description', $invoiceDescription);

    $invoiceStmt->execute();
    $invoiceId = $pdo->lastInsertId();

    // Add invoice items
    $itemStmt = $pdo->prepare("
      INSERT INTO invoice_items (
        invoice_id,
        description,
        quantity,
        unit_price,
        total
      ) VALUES (
        :invoice_id,
        :description,
        :quantity,
        :unit_price,
        :total
      )
    ");

    // Check if we have detailed items in the request
    if (!empty($data['items']) && is_array($data['items'])) {
      // Add each item from the request
      foreach ($data['items'] as $item) {
        $itemStmt->bindValue(':invoice_id', $invoiceId);
        $itemStmt->bindValue(':description', $item['description'] ?? $item['name'] ?? 'Server Order');
        $itemStmt->bindValue(':quantity', $item['quantity'] ?? 1);
        $itemStmt->bindValue(':unit_price', $item['price'] ?? 0);
        $itemStmt->bindValue(':total', ($item['price'] ?? 0) * ($item['quantity'] ?? 1));
        $itemStmt->execute();

        // Save detailed information in orders_items table
        $this_item_id = $pdo->lastInsertId();

        // Extract payment period from the item if available
        $paymentPeriod = '';
        if (!empty($item['period'])) {
          $paymentPeriod = $item['period'];
        } else if (stripos($item['description'] ?? '', 'monthly') !== false) {
          $paymentPeriod = 'monthly';
        } else if (stripos($item['description'] ?? '', 'quarterly') !== false) {
          $paymentPeriod = 'quarterly';
        } else if (stripos($item['description'] ?? '', 'yearly') !== false) {
          $paymentPeriod = 'yearly';
        }

        // Create orders_items table if it doesn't exist
        $pdo->exec("CREATE TABLE IF NOT EXISTS orders_items (
          id INT(11) NOT NULL AUTO_INCREMENT,
          order_id INT(11) NOT NULL,
          invoice_item_id INT(11) NOT NULL,
          user_id INT(11) NULL,
          server_id INT(11) NULL,
          cpu_id INT(11) NULL,
          bandwidth_id INT(11) NULL,
          location_id INT(11) NULL,
          storage_id INT(11) NULL,
          subnet_id INT(11) NULL,
          additional_ips_id INT(11) NULL,
          order_price DECIMAL(10,2) NULL,
          requirement_price DECIMAL(10,2) NULL,
          payment_period VARCHAR(50) NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (id),
          KEY order_id (order_id),
          KEY invoice_item_id (invoice_item_id),
          KEY user_id (user_id),
          KEY server_id (server_id),
          KEY additional_ips_id (additional_ips_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");

        // Check if additional_ips_id column exists
        $columnCheck = $pdo->query("SHOW COLUMNS FROM orders_items LIKE 'additional_ips_id'");
        if ($columnCheck->rowCount() == 0) {
          // Add the column if it doesn't exist
          $pdo->exec("ALTER TABLE orders_items ADD COLUMN additional_ips_id INT(11) DEFAULT NULL COMMENT 'ID of the additional IPs package' AFTER subnet_id");
          $pdo->exec("ALTER TABLE orders_items ADD INDEX idx_additional_ips_id (additional_ips_id)");
          error_log("Added additional_ips_id column to orders_items table");
        }

        // Check if status column exists
        $statusCheck = $pdo->query("SHOW COLUMNS FROM orders_items LIKE 'status'");
        if ($statusCheck->rowCount() == 0) {
          // Add the status column if it doesn't exist
          $pdo->exec("ALTER TABLE orders_items ADD COLUMN status VARCHAR(50) DEFAULT 'Pending' COMMENT 'Service status' AFTER due_date");
          error_log("Added status column to orders_items table");
        }

        // Insert into orders_items
        $orderItemStmt = $pdo->prepare("INSERT INTO orders_items (
          order_id,
          invoice_item_id,
          user_id,
          server_id,
          cpu_id,
          bandwidth_id,
          location_id,
          storage_id,
          subnet_id,
          additional_ips_id,
          order_price,
          requirement_price,
          payment_period,
          hostname,
          date_ordered,
          due_date,
          status
        ) VALUES (
          :order_id,
          :invoice_item_id,
          :user_id,
          :server_id,
          :cpu_id,
          :bandwidth_id,
          :location_id,
          :storage_id,
          :subnet_id,
          :additional_ips_id,
          :order_price,
          :requirement_price,
          :payment_period,
          :hostname,
          NOW(),
          NOW(),
          'Pending'
        )");

        // Extract IDs from the item description if available
        $cpuId = null;
        $bandwidthId = null;
        $locationId = null;
        $storageId = null;
        $subnetId = null;
        $additionalIpsId = null;

        // Try to extract IDs from the item description using regex
        $description = $item['description'] ?? '';

        // If we have the original form data, use it directly
        if (!empty($data['cpuModel'])) {
          $cpuId = $data['cpuModel'];
        }
        if (!empty($data['bandwidth'])) {
          $bandwidthId = $data['bandwidth'];
        }
        if (!empty($data['location'])) {
          $locationId = $data['location'];
        }
        if (!empty($data['storage'])) {
          $storageId = $data['storage'];
        }
        if (!empty($data['subnetSize'])) {
          $subnetId = $data['subnetSize'];
        }
        if (!empty($data['additionalIps'])) {
          $additionalIpsId = $data['additionalIps'];
        }

        // If the item has specific IDs, use those instead
        if (!empty($item['cpuId'])) {
          $cpuId = $item['cpuId'];
        }
        if (!empty($item['bandwidthId'])) {
          $bandwidthId = $item['bandwidthId'];
        }
        if (!empty($item['locationId'])) {
          $locationId = $item['locationId'];
        }
        if (!empty($item['storageId'])) {
          $storageId = $item['storageId'];
        }
        if (!empty($item['subnetId'])) {
          $subnetId = $item['subnetId'];
        }
        if (!empty($item['additionalIpsId'])) {
          $additionalIpsId = $item['additionalIpsId'];
        }
        // For IP Transit and Colocation specific fields
        if (!empty($item['transitAdditionalIpsId'])) {
          $additionalIpsId = $item['transitAdditionalIpsId'];
        }
        if (!empty($item['colocationAdditionalIpsId'])) {
          $additionalIpsId = $item['colocationAdditionalIpsId'];
        }

        // Log the values for debugging
        error_log("Creating order item with: userId=$userId, orderId=$orderId, cpuId=$cpuId, bandwidthId=$bandwidthId, locationId=$locationId, additionalIpsId=$additionalIpsId");

        $orderItemStmt->bindValue(':order_id', $orderId);
        $orderItemStmt->bindValue(':invoice_item_id', $this_item_id);
        // Make sure we use the owner_id from the order as the user_id
        $orderItemStmt->bindValue(':user_id', $data['userId'] ?? $userId);
        $orderItemStmt->bindValue(':server_id', null); // Leave server_id empty as requested
        $orderItemStmt->bindValue(':cpu_id', $cpuId);
        $orderItemStmt->bindValue(':bandwidth_id', $bandwidthId);
        $orderItemStmt->bindValue(':location_id', $locationId);
        $orderItemStmt->bindValue(':storage_id', $storageId);
        $orderItemStmt->bindValue(':subnet_id', $subnetId);
        $orderItemStmt->bindValue(':additional_ips_id', $additionalIpsId);
        $orderItemStmt->bindValue(':order_price', $item['price'] ?? 0);
        $orderItemStmt->bindValue(':requirement_price', $item['requirementPrice'] ?? ($data['recurringPrice'] ?? 0));
        $orderItemStmt->bindValue(':payment_period', $paymentPeriod);
        $orderItemStmt->bindValue(':hostname', 'srv-' . $orderId . '.example.com');
        $orderItemStmt->execute();
      }
    } else {
      // Fallback to a single item if no detailed items provided
      $itemStmt->bindValue(':invoice_id', $invoiceId);
      $itemStmt->bindValue(':description', $data['orderNote'] ?? 'Server Order');
      $itemStmt->bindValue(':quantity', 1);
      $itemStmt->bindValue(':unit_price', $subtotal);
      $itemStmt->bindValue(':total', $subtotal);
      $itemStmt->execute();

      // Save basic information in orders_items table for the fallback item
      $this_item_id = $pdo->lastInsertId();

      // Create orders_items table if it doesn't exist
      $pdo->exec("CREATE TABLE IF NOT EXISTS orders_items (
        id INT(11) NOT NULL AUTO_INCREMENT,
        order_id INT(11) NOT NULL,
        invoice_item_id INT(11) NOT NULL,
        user_id INT(11) NULL,
        server_id INT(11) NULL,
        cpu_id INT(11) NULL,
        bandwidth_id INT(11) NULL,
        location_id INT(11) NULL,
        storage_id INT(11) NULL,
        subnet_id INT(11) NULL,
        additional_ips_id INT(11) NULL,
        order_price DECIMAL(10,2) NULL,
        requirement_price DECIMAL(10,2) NULL,
        payment_period VARCHAR(50) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY order_id (order_id),
        KEY invoice_item_id (invoice_item_id),
        KEY user_id (user_id),
        KEY server_id (server_id),
        KEY additional_ips_id (additional_ips_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");

      // Check if additional_ips_id column exists
      $columnCheck = $pdo->query("SHOW COLUMNS FROM orders_items LIKE 'additional_ips_id'");
      if ($columnCheck->rowCount() == 0) {
        // Add the column if it doesn't exist
        $pdo->exec("ALTER TABLE orders_items ADD COLUMN additional_ips_id INT(11) DEFAULT NULL COMMENT 'ID of the additional IPs package' AFTER subnet_id");
        $pdo->exec("ALTER TABLE orders_items ADD INDEX idx_additional_ips_id (additional_ips_id)");
        error_log("Added additional_ips_id column to orders_items table");
      }

      // Check if status column exists
      $statusCheck = $pdo->query("SHOW COLUMNS FROM orders_items LIKE 'status'");
      if ($statusCheck->rowCount() == 0) {
        // Add the status column if it doesn't exist
        $pdo->exec("ALTER TABLE orders_items ADD COLUMN status VARCHAR(50) DEFAULT 'Pending' COMMENT 'Service status' AFTER due_date");
        error_log("Added status column to orders_items table");
      }

      // Extract payment period from form data if available
      $paymentPeriod = '';
      if (!empty($data['paymentPeriod'])) {
        $paymentPeriod = $data['paymentPeriod'];
      }

      // Insert into orders_items
      $orderItemStmt = $pdo->prepare("INSERT INTO orders_items (
        order_id,
        invoice_item_id,
        user_id,
        server_id,
        cpu_id,
        bandwidth_id,
        location_id,
        storage_id,
        subnet_id,
        additional_ips_id,
        order_price,
        requirement_price,
        payment_period,
        hostname,
        date_ordered,
        due_date,
        status
      ) VALUES (
        :order_id,
        :invoice_item_id,
        :user_id,
        :server_id,
        :cpu_id,
        :bandwidth_id,
        :location_id,
        :storage_id,
        :subnet_id,
        :additional_ips_id,
        :order_price,
        :requirement_price,
        :payment_period,
        :hostname,
        NOW(),
        NOW(),
        'Pending'
      )");

      // Extract IDs from the form data
      $cpuId = $data['cpuModel'] ?? null;
      $bandwidthId = $data['bandwidth'] ?? null;
      $locationId = $data['location'] ?? null;
      $storageId = $data['storage'] ?? null;
      $subnetId = $data['subnetSize'] ?? null;
      $additionalIpsId = $data['additionalIps'] ?? null;

      // Check for IP Transit and Colocation specific fields
      if (!empty($data['transitAdditionalIps'])) {
        $additionalIpsId = $data['transitAdditionalIps'];
      }
      if (!empty($data['colocationAdditionalIps'])) {
        $additionalIpsId = $data['colocationAdditionalIps'];
      }

      // Log the values for debugging
      error_log("Creating fallback order item with: userId=$userId, orderId=$orderId, cpuId=$cpuId, bandwidthId=$bandwidthId, locationId=$locationId, additionalIpsId=$additionalIpsId");

      $orderItemStmt->bindValue(':order_id', $orderId);
      $orderItemStmt->bindValue(':invoice_item_id', $this_item_id);
      // Make sure we use the owner_id from the order as the user_id
      $orderItemStmt->bindValue(':user_id', $data['userId'] ?? $userId);
      $orderItemStmt->bindValue(':server_id', null); // Leave server_id empty as requested
      $orderItemStmt->bindValue(':cpu_id', $cpuId);
      $orderItemStmt->bindValue(':bandwidth_id', $bandwidthId);
      $orderItemStmt->bindValue(':location_id', $locationId);
      $orderItemStmt->bindValue(':storage_id', $storageId);
      $orderItemStmt->bindValue(':subnet_id', $subnetId);
      $orderItemStmt->bindValue(':additional_ips_id', $additionalIpsId);
      $orderItemStmt->bindValue(':order_price', $subtotal);
      $orderItemStmt->bindValue(':requirement_price', $data['recurringPrice'] ?? 0);
      $orderItemStmt->bindValue(':payment_period', $paymentPeriod);
      $orderItemStmt->bindValue(':hostname', 'srv-' . $orderId . '.example.com');
      $orderItemStmt->execute();
    }

    // Commit transaction
    if ($pdo->inTransaction()) {
      $pdo->commit();
      error_log("Successfully committed transaction for order creation");
    } else {
      error_log("Warning: Attempted to commit but there was no active transaction");
    }

    // Send email notification for the invoice with additional error handling
    try {
      error_log("Attempting to send email notification for invoice #$invoiceId");

      // Make sure we have the PHPMailer library
      // Check multiple possible locations for PHPMailer
      $phpmailerPaths = [
        '/var/www/html/New/admin/vendor/phpmailer/phpmailer/src/PHPMailer.php',
        '/var/www/html/vendor/phpmailer/phpmailer/src/PHPMailer.php',
        dirname(dirname(__FILE__)) . '/vendor/phpmailer/phpmailer/src/PHPMailer.php'
      ];

      $phpmailerFound = false;
      foreach ($phpmailerPaths as $path) {
        if (file_exists($path)) {
          $phpmailerFound = true;
          break;
        }
      }

      if (!$phpmailerFound) {
        error_log("PHPMailer not found. Checked multiple locations.");
      } else {
        $emailResult = sendInvoiceGeneratedEmail($invoiceId);
        if ($emailResult) {
          error_log("Successfully sent email notification for invoice #$invoiceId");
        } else {
          error_log("Failed to send email notification for invoice #$invoiceId");
        }
      }
    } catch (Exception $emailError) {
      error_log("Error sending email notification: " . $emailError->getMessage());
      // Log the stack trace for better debugging
      error_log("Stack trace: " . $emailError->getTraceAsString());
      // Don't throw the exception - we don't want to fail the order creation if email fails
    }

    // Get customer name
    $nameStmt = $pdo->prepare("SELECT company_name, first_name, last_name FROM users WHERE id = :id");
    $nameStmt->bindValue(':id', $userId);
    $nameStmt->execute();
    $user = $nameStmt->fetch(PDO::FETCH_ASSOC);

    $customerName = $user['company_name'] ?? trim($user['first_name'] . ' ' . $user['last_name']);
    if (empty($customerName)) {
      $customerName = $data['customerName'] ?? 'New Customer';
    }

    // Get the user's location after update
    $locationQuery = $pdo->prepare("SELECT city, country FROM users WHERE id = :user_id");
    $locationQuery->bindValue(':user_id', $userId);
    $locationQuery->execute();
    $userLocation = $locationQuery->fetch(PDO::FETCH_ASSOC);

    // Format location string
    $locationStr = '';
    if (!empty($userLocation['city'])) {
      $locationStr = $userLocation['city'];
      if (!empty($userLocation['country'])) {
        $locationStr .= ', ' . $userLocation['country'];
      }
    } else if (!empty($data['location'])) {
      $locationStr = $data['location'];
    }

    // Prepare response with actual data
    $response = [
      'success' => true,
      'order_id' => $orderId,
      'id' => '#ORD-' . str_pad($orderId, 4, '0', STR_PAD_LEFT),
      'rawId' => $orderId,
      'customerName' => $customerName,
      'date' => date('Y-m-d'),
      'amount' => '€' . number_format($data['initialPrice'] ?? 0, 2, '.', ','),
      'status' => 'Pending',
      'paymentStatus' => 'Pending',
      'items' => 1,
      'message' => 'Order created successfully',
      'invoice' => [
        'id' => $invoiceId,
        'proformaNumber' => $proformaNumber,
        'emailSent' => isset($emailResult) ? $emailResult : false,
        'phpmailerFound' => isset($phpmailerFound) ? $phpmailerFound : false
      ]
    ];

    // Only add fields that have actual data
    if (!empty($locationStr)) {
      $response['location'] = $locationStr;
    }

    if (!empty($data['paymentMethod'])) {
      $response['paymentMethod'] = $data['paymentMethod'];
    } else {
      $response['paymentMethod'] = 'Bank Transfer';
    }

    return $response;
  } catch (Exception $e) {
    // Rollback transaction on error
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
      error_log("Rolled back transaction due to error");
    } else {
      error_log("No active transaction to roll back");
    }

    error_log("Database error in createOrder: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());

    return [
      'success' => false,
      'error' => 'Failed to create order: ' . $e->getMessage()
    ];
  }
}

/**
 * Update order status
 */
function updateOrderStatus($pdo, $adminId, $orderId, $newStatus) {
  try {
    // Make sure there's no active transaction before starting a new one
    if ($pdo->inTransaction()) {
      error_log("Warning: There is already an active transaction. Rolling back before starting a new one.");
      $pdo->rollBack();
    }

    // Begin transaction
    $pdo->beginTransaction();
    error_log("Started transaction for order status update");

    // Clean order ID (remove non-numeric characters)
    if (is_string($orderId)) {
      $orderId = preg_replace('/\D/', '', $orderId);
    }

    // Get current order status
    $getStmt = $pdo->prepare("SELECT status FROM orders WHERE id = :id");
    $getStmt->bindValue(':id', $orderId);
    $getStmt->execute();
    $order = $getStmt->fetch(PDO::FETCH_ASSOC);

    if (!$order) {
      throw new Exception("Order not found");
    }

    $oldStatus = $order['status'];

    // Update the order status
    $updateStmt = $pdo->prepare("UPDATE orders SET status = :status WHERE id = :id");
    $updateStmt->bindValue(':status', $newStatus);
    $updateStmt->bindValue(':id', $orderId);
    $updateStmt->execute();

    // If status is now completed, update any related invoice
    if ($newStatus === 'Completed') {
      $invoiceStmt = $pdo->prepare("UPDATE invoices SET status = 'Paid', paid_date = NOW() WHERE order_id = :order_id");
      $invoiceStmt->bindValue(':order_id', $orderId);
      $invoiceStmt->execute();

      // Get the invoice ID to send email notification
      $invoiceIdStmt = $pdo->prepare("SELECT id FROM invoices WHERE order_id = :order_id ORDER BY id DESC LIMIT 1");
      $invoiceIdStmt->bindValue(':order_id', $orderId);
      $invoiceIdStmt->execute();
      $invoiceRow = $invoiceIdStmt->fetch(PDO::FETCH_ASSOC);

      if ($invoiceRow && !empty($invoiceRow['id'])) {
        try {
          $invoiceId = $invoiceRow['id'];
          error_log("Attempting to send email notification for paid invoice #$invoiceId");

          // Make sure we have the PHPMailer library
          // Check multiple possible locations for PHPMailer
          $phpmailerPaths = [
            '/var/www/html/New/admin/vendor/phpmailer/phpmailer/src/PHPMailer.php',
            '/var/www/html/vendor/phpmailer/phpmailer/src/PHPMailer.php',
            dirname(dirname(__FILE__)) . '/vendor/phpmailer/phpmailer/src/PHPMailer.php'
          ];

          $phpmailerFound = false;
          foreach ($phpmailerPaths as $path) {
            if (file_exists($path)) {
              $phpmailerFound = true;
              break;
            }
          }

          if (!$phpmailerFound) {
            error_log("PHPMailer not found. Checked multiple locations.");
          } else {
            $emailResult = sendInvoiceGeneratedEmail($invoiceId);
            if ($emailResult) {
              error_log("Successfully sent email notification for paid invoice #$invoiceId");
            } else {
              error_log("Failed to send email notification for paid invoice #$invoiceId");
            }
          }
        } catch (Exception $emailError) {
          error_log("Error sending email notification for paid invoice: " . $emailError->getMessage());
          // Log the stack trace for better debugging
          error_log("Stack trace: " . $emailError->getTraceAsString());
          // Don't throw the exception - we don't want to fail the status update if email fails
        }
      }
    }

    // Log the status change
    $logStmt = $pdo->prepare("
      INSERT INTO activity_log (
        user_id,
        action,
        description,
        order_id,
        timestamp
      ) VALUES (
        :admin_id,
        'status_change',
        :description,
        :order_id,
        NOW()
      )
    ");

    $logStmt->bindValue(':admin_id', $adminId);
    $logStmt->bindValue(':description', "Order status updated from {$oldStatus} to {$newStatus}");
    $logStmt->bindValue(':order_id', $orderId);
    $logStmt->execute();

    // Commit transaction
    if ($pdo->inTransaction()) {
      $pdo->commit();
      error_log("Successfully committed transaction for order status update");
    } else {
      error_log("Warning: Attempted to commit but there was no active transaction");
    }

    $response = [
      'success' => true,
      'order_id' => $orderId,
      'old_status' => $oldStatus,
      'new_status' => $newStatus,
      'message' => "Order status updated successfully"
    ];

    // Add email sent status if applicable
    if ($newStatus === 'Completed') {
      if (isset($emailResult)) {
        $response['email_sent'] = $emailResult;
      }
      if (isset($phpmailerFound)) {
        $response['phpmailer_found'] = $phpmailerFound;
      }
    }

    return $response;
  } catch (Exception $e) {
    // Rollback transaction on error
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
      error_log("Rolled back transaction due to error");
    } else {
      error_log("No active transaction to roll back");
    }

    error_log("Database error in updateOrderStatus: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());

    return [
      'success' => false,
      'error' => 'Failed to update order status: ' . $e->getMessage()
    ];
  }
}

/**
 * Get detailed information for a specific order
 */
function getOrderDetails($pdo, $orderId) {
  try {
    // Clean order ID
    if (is_string($orderId)) {
      $orderId = preg_replace('/\D/', '', $orderId);
    }

    // Get order data with customer info
    $orderStmt = $pdo->prepare("
      SELECT o.*, u.first_name, u.last_name, u.company_name, u.email, u.city, u.country
      FROM orders o
      LEFT JOIN users u ON o.owner_id = u.id
      WHERE o.id = :id
    ");
    $orderStmt->bindValue(':id', $orderId);
    $orderStmt->execute();

    $order = $orderStmt->fetch(PDO::FETCH_ASSOC);

    if (!$order) {
      return [
        'success' => false,
        'error' => "Order not found with ID: $orderId"
      ];
    }

    // Get invoice data
    $invoiceStmt = $pdo->prepare("
      SELECT i.*
      FROM invoices i
      WHERE i.order_id = :order_id
      ORDER BY i.id DESC
      LIMIT 1
    ");
    $invoiceStmt->bindValue(':order_id', $orderId);
    $invoiceStmt->execute();

    $invoice = $invoiceStmt->fetch(PDO::FETCH_ASSOC);

    // Get invoice items if invoice exists
    $items = [];
    if ($invoice) {
      $itemsStmt = $pdo->prepare("
        SELECT * FROM invoice_items
        WHERE invoice_id = :invoice_id
      ");
      $itemsStmt->bindValue(':invoice_id', $invoice['id']);
      $itemsStmt->execute();

      $invoiceItems = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);

      foreach ($invoiceItems as $item) {
        // Get detailed item information from orders_items if available
        $orderItemStmt = $pdo->prepare("SELECT * FROM orders_items WHERE invoice_item_id = :invoice_item_id LIMIT 1");
        $orderItemStmt->bindValue(':invoice_item_id', $item['id']);
        $orderItemStmt->execute();
        $orderItem = $orderItemStmt->fetch(PDO::FETCH_ASSOC);

        $itemData = [
            'name' => $item['description'],
            'quantity' => $item['quantity'],
            'price' => '€' . number_format($item['unit_price'], 2, '.', ',')
        ];

        // Add detailed information if available
        if ($orderItem) {
            // Add the orders_items.id explicitly
            $itemData['item_id'] = $orderItem['id'];

            if (!empty($orderItem['user_id'])) {
                $itemData['userId'] = $orderItem['user_id'];
            }
          if (!empty($orderItem['server_id'])) {
            $itemData['serverId'] = $orderItem['server_id'];
          }
          if (!empty($orderItem['cpu_id'])) {
            $itemData['cpuId'] = $orderItem['cpu_id'];
          }
          if (!empty($orderItem['bandwidth_id'])) {
            $itemData['bandwidthId'] = $orderItem['bandwidth_id'];
          }
          if (!empty($orderItem['location_id'])) {
            $itemData['locationId'] = $orderItem['location_id'];
          }
          if (!empty($orderItem['storage_id'])) {
            $itemData['storageId'] = $orderItem['storage_id'];
          }
          if (!empty($orderItem['subnet_id'])) {
            $itemData['subnetId'] = $orderItem['subnet_id'];
          }
          if (!empty($orderItem['payment_period'])) {
            $itemData['paymentPeriod'] = $orderItem['payment_period'];
          }
        }

        $items[] = $itemData;
      }
    }

    // If no items found, create a placeholder item
    if (empty($items)) {
      // Try to get CPU info from the order label
      $itemName = 'Server Order';
      if (!empty($order['label'])) {
        // If label contains CPU info, use it
        $itemName = $order['label'];
      } else if (!empty($order['assigned_server_type'])) {
        // If we have server type, use it
        $itemName = $order['assigned_server_type'];
      }

      $items[] = [
        'name' => $itemName,
        'quantity' => 1,
        'price' => '€' . number_format($order['initial_price'] ?: 0, 2, '.', ',')
      ];
    }

    // Format customer name
    $customerName = !empty($order['company_name']) ? $order['company_name'] : trim($order['first_name'] . ' ' . $order['last_name']);
    if (empty($customerName)) {
      $customerName = 'Customer #' . $order['owner_id'];
    }

    // Format payment status from invoice
    $paymentStatus = $invoice ? ($invoice['status'] ?: 'Pending') : 'Pending';

    // Format payment method from invoice
    $paymentMethod = $invoice ? ($invoice['payment_method'] ?: 'Bank Transfer') : 'Bank Transfer';

    // Format amount
    $amount = '€' . number_format($order['initial_price'] ?: 0, 2, '.', ',');

    // Format location - only use actual data
    $location = '';
    if (!empty($order['city'])) {
      $location = $order['city'];
      if (!empty($order['country'])) {
        $location .= ', ' . $order['country'];
      }
    }

    // Prepare response with only actual data
    $response = [
      'success' => true,
      'id' => '#ORD-' . str_pad($order['id'], 4, '0', STR_PAD_LEFT),
      'rawId' => $order['id'],
      'customerName' => $customerName,
      'date' => date('Y-m-d', strtotime($order['order_date'])),
      'status' => $order['status'] ?: 'Pending',
    ];

    // Only add fields that have actual data
    if (!empty($order['email'])) {
      $response['customerEmail'] = $order['email'];
    }

    if (!empty($order['owner_id'])) {
      $response['customerId'] = $order['owner_id'];
    }

    if (!empty($order['expiration_date'])) {
      $response['expirationDate'] = date('Y-m-d', strtotime($order['expiration_date']));
    }

    if (!empty($order['initial_price'])) {
      $response['amount'] = $amount;
    }

    if ($paymentStatus) {
      $response['paymentStatus'] = $paymentStatus;
    }

    if ($paymentMethod) {
      $response['paymentMethod'] = $paymentMethod;
    }

    if (!empty($location)) {
      $response['location'] = $location;
    }

    if (!empty($items)) {
      $response['items'] = $items;
    }

    if (!empty($order['label'])) {
      $response['label'] = $order['label'];
    }

    if (!empty($order['order_type'])) {
      $response['orderType'] = $order['order_type'];
    }

    // Only add server details if they exist
    if (!empty($order['assigned_server_id']) || !empty($order['assigned_server_type'])) {
      $response['serverDetails'] = [];

      if (!empty($order['assigned_server_id'])) {
        $response['serverDetails']['serverId'] = $order['assigned_server_id'];
      }

      if (!empty($order['assigned_server_type'])) {
        $response['serverDetails']['serverType'] = $order['assigned_server_type'];
      }

      // Don't include password, just indicate it exists
      if (!empty($order['root_password'])) {
        $response['serverDetails']['rootPassword'] = '********';
      }
    }

    // Add invoice details if available
    if ($invoice) {
      $response['invoice'] = [];

      if (!empty($invoice['id'])) {
        $response['invoice']['id'] = $invoice['id'];
      }

      if (!empty($invoice['invoice_number'])) {
        $response['invoice']['invoiceNumber'] = $invoice['invoice_number'];
      }

      if (!empty($invoice['proforma_number'])) {
        $response['invoice']['proformaNumber'] = $invoice['proforma_number'];
      }

      if (!empty($invoice['type'])) {
        $response['invoice']['type'] = $invoice['type'];
      } else {
        $response['invoice']['type'] = 'proforma';
      }

      if (!empty($invoice['status'])) {
        $response['invoice']['status'] = $invoice['status'];
      } else {
        $response['invoice']['status'] = 'Pending';
      }

      if (!empty($invoice['value'])) {
        $response['invoice']['amount'] = '€' . number_format($invoice['value'], 2, '.', ',');
      }

      if (!empty($invoice['date'])) {
        $response['invoice']['date'] = date('Y-m-d', strtotime($invoice['date']));
      }

      if (!empty($invoice['due_date'])) {
        $response['invoice']['dueDate'] = date('Y-m-d', strtotime($invoice['due_date']));
      }

      // Include invoice description if available
      if (!empty($invoice['description'])) {
        $response['invoice']['description'] = $invoice['description'];
      }
    }

    return $response;
  } catch (Exception $e) {
    error_log("Database error in getOrderDetails: " . $e->getMessage());

    return [
      'success' => false,
      'error' => 'Failed to get order details: ' . $e->getMessage()
    ];
  }
}

/**
 * Generate a random password
 */
function generateRandomPassword($length = 12) {
  $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
  $password = '';

  for ($i = 0; $i < $length; $i++) {
    $index = rand(0, strlen($chars) - 1);
    $password .= $chars[$index];
  }

  return $password;
}

/**
 * Update server ID for a service - links service to physical server
 */
function update_service_server_id($pdo, $adminId, $data) {
  try {
    // Validate required fields
    if (empty($data['service_id'])) {
      return [
        'success' => false,
        'error' => 'Service ID is required'
      ];
    }

    if (empty($data['server_id'])) {
      return [
        'success' => false,
        'error' => 'Server ID is required'
      ];
    }

    // Get information about server type
    $serverType = !empty($data['server_type']) ? $data['server_type'] : 'dedicated';

    // Check if server exists in inventory
    $serverTableName = $serverType === 'blade' ? 'blade_server_inventory' : 'inventory_dedicated_servers';
    $checkServerStmt = $pdo->prepare("SELECT id FROM {$serverTableName} WHERE id = :server_id LIMIT 1");
    $checkServerStmt->bindValue(':server_id', $data['server_id']);
    $checkServerStmt->execute();

    if (!$checkServerStmt->fetch()) {
      return [
        'success' => false,
        'error' => "Server with ID {$data['server_id']} not found in {$serverTableName} table"
      ];
    }

    // Check if service exists
    $checkServiceStmt = $pdo->prepare("SELECT id FROM orders_items WHERE id = :service_id LIMIT 1");
    $checkServiceStmt->bindValue(':service_id', $data['service_id']);
    $checkServiceStmt->execute();

    if (!$checkServiceStmt->fetch()) {
      return [
        'success' => false,
        'error' => "Service with ID {$data['service_id']} not found"
      ];
    }

    // Update the server_id in orders_items table
    $updateStmt = $pdo->prepare("UPDATE orders_items SET server_id = :server_id WHERE id = :service_id");
    $updateStmt->bindValue(':server_id', $data['server_id']);
    $updateStmt->bindValue(':service_id', $data['service_id']);
    $updateStmt->execute();

    // Get admin name for logging
    $adminName = "Admin";
    try {
      $adminStmt = $pdo->prepare("SELECT CONCAT(first_name, ' ', last_name) as full_name FROM users WHERE id = :admin_id LIMIT 1");
      $adminStmt->bindValue(':admin_id', $adminId);
      $adminStmt->execute();
      $admin = $adminStmt->fetch(PDO::FETCH_ASSOC);
      if ($admin && !empty($admin['full_name'])) {
        $adminName = $admin['full_name'];
      }
    } catch (Exception $e) {
      // If we can't get the name, use the default
      error_log("Could not get admin name: " . $e->getMessage());
    }

    // Log this action
    $logStmt = $pdo->prepare("
      INSERT INTO activity_log (
        user_id,
        user_name,
        action,
        description,
        timestamp
      ) VALUES (
        :admin_id,
        :admin_name,
        'service_update',
        :description,
        NOW()
      )
    ");

    $logStmt->bindValue(':admin_id', $adminId);
    $logStmt->bindValue(':admin_name', $adminName);
    $logStmt->bindValue(':description', "Linked service ID {$data['service_id']} to {$serverType} server ID {$data['server_id']}");
    $logStmt->execute();

    return [
      'success' => true,
      'message' => "Service successfully linked to {$serverType} server ID {$data['server_id']}",
      'service_id' => $data['service_id'],
      'server_id' => $data['server_id'],
      'server_type' => $serverType
    ];
  } catch (Exception $e) {
    error_log("Database error in update_service_server_id: " . $e->getMessage());

    return [
      'success' => false,
      'error' => 'Failed to update server ID: ' . $e->getMessage()
    ];
  }
}

/**
 * Update service details including CPU, storage, bandwidth, price, etc.
 *
 * @param PDO $pdo Database connection
 * @param int $adminId ID of the admin making the change
 * @param array $data Service data to update
 * @return array Result of the operation
 */
/**
 * Get a single order item by ID with comprehensive data
 */
function getOrderItemById($pdo, $itemId) {
  try {
    // Clean item ID if it's a string with leading zeros
    if (is_string($itemId) && preg_match('/^0+\d+$/', $itemId)) {
      $itemId = ltrim($itemId, '0');
    }

    // Comprehensive query with LEFT JOINs to all related tables
    $stmt = $pdo->prepare("SELECT oi.*,
                          o.status as order_status, o.order_type, o.label as order_label,
                          o.order_date, o.expiration_date, o.assigned_server_type,
                          dc.id as cpu_id, dc.cpu as cpu_name, dc.description as cpu_description, dc.price as cpu_price,
                          ds.id as storage_id, ds.name as storage_name, ds.description as storage_description, ds.price as storage_price,
                          db.id as bandwidth_id, db.name as bandwidth_name, db.description as bandwidth_description, db.price as bandwidth_price,
                          c.id as location_id, c.name as location_name, c.city as location_city,
                          ct.name as country_name, c.datacenter as location_datacenter,
                          u.first_name, u.last_name, u.company_name, u.email
                          FROM orders_items oi
                          LEFT JOIN orders o ON oi.order_id = o.id
                          LEFT JOIN dedicated_cpu dc ON oi.cpu_id = dc.id
                          LEFT JOIN dedicated_storages ds ON oi.storage_id = ds.id
                          LEFT JOIN dedicated_bandwidth db ON oi.bandwidth_id = db.id
                          LEFT JOIN dedicated_cities c ON oi.location_id = c.id
                          LEFT JOIN cities ct ON c.city = ct.id
                          LEFT JOIN users u ON oi.user_id = u.id
                          WHERE oi.id = :item_id LIMIT 1");
    $stmt->bindValue(':item_id', $itemId);
    $stmt->execute();

    $item = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$item) {
      // If no item found with the exact ID, try to find by order_id
      $orderStmt = $pdo->prepare("SELECT oi.*,
                              o.status as order_status, o.order_type, o.label as order_label,
                              o.order_date, o.expiration_date, o.assigned_server_type,
                              dc.id as cpu_id, dc.cpu as cpu_name, dc.description as cpu_description, dc.price as cpu_price,
                              ds.id as storage_id, ds.name as storage_name, ds.description as storage_description, ds.price as storage_price,
                              db.id as bandwidth_id, db.name as bandwidth_name, db.description as bandwidth_description, db.price as bandwidth_price,
                              c.id as location_id, c.name as location_name, c.city as location_city,
                              ct.name as country_name, c.datacenter as location_datacenter,
                              u.first_name, u.last_name, u.company_name, u.email
                              FROM orders_items oi
                              LEFT JOIN orders o ON oi.order_id = o.id
                              LEFT JOIN dedicated_cpu dc ON oi.cpu_id = dc.id
                              LEFT JOIN dedicated_storages ds ON oi.storage_id = ds.id
                              LEFT JOIN dedicated_bandwidth db ON oi.bandwidth_id = db.id
                              LEFT JOIN dedicated_cities c ON oi.location_id = c.id
                              LEFT JOIN cities ct ON c.city = ct.id
                              LEFT JOIN users u ON oi.user_id = u.id
                              WHERE oi.order_id = :order_id LIMIT 1");
      $orderStmt->bindValue(':order_id', $itemId);
      $orderStmt->execute();
      $item = $orderStmt->fetch(PDO::FETCH_ASSOC);

      if (!$item) {
        return null;
      }
    }

    // Enhance the item with display-friendly values directly from database fields

    // CPU information - always use the database value
    if (!isset($item['cpu'])) {
      if (isset($item['cpu_name']) && !empty($item['cpu_name'])) {
        $item['cpu'] = $item['cpu_name'];
      } else if (isset($item['cpu_description']) && !empty($item['cpu_description'])) {
        $item['cpu'] = $item['cpu_description'];
      } else if (isset($item['cpu_id']) && !empty($item['cpu_id'])) {
        $item['cpu'] = "CPU #" . $item['cpu_id'];
      }
    }

    // Storage information - always use the database value
    if (!isset($item['storage'])) {
      if (isset($item['storage_name']) && !empty($item['storage_name'])) {
        $item['storage'] = $item['storage_name'];
      } else if (isset($item['storage_description']) && !empty($item['storage_description'])) {
        $item['storage'] = $item['storage_description'];
      } else if (isset($item['storage_id']) && !empty($item['storage_id'])) {
        $item['storage'] = "Storage #" . $item['storage_id'];
      }
    }

    // Bandwidth information - always use the database value
    if (!isset($item['bandwidth'])) {
      if (isset($item['bandwidth_name']) && !empty($item['bandwidth_name'])) {
        $item['bandwidth'] = $item['bandwidth_name'];
      } else if (isset($item['bandwidth_description']) && !empty($item['bandwidth_description'])) {
        $item['bandwidth'] = $item['bandwidth_description'];
      } else if (isset($item['bandwidth_id']) && !empty($item['bandwidth_id'])) {
        $item['bandwidth'] = "Bandwidth #" . $item['bandwidth_id'];
      }
    }

    // Location information - always use the database value
    if (!isset($item['location'])) {
      if (isset($item['location_name']) && !empty($item['location_name'])) {
        $item['location'] = $item['location_name'];
        if (isset($item['country_name']) && !empty($item['country_name'])) {
          $item['location'] .= ', ' . $item['country_name'];
        }
      } else if (isset($item['location_city']) && !empty($item['location_city'])) {
        $item['location'] = $item['location_city'];
        if (isset($item['country_name']) && !empty($item['country_name'])) {
          $item['location'] .= ', ' . $item['country_name'];
        }
      } else if (isset($item['location_id']) && !empty($item['location_id'])) {
        $item['location'] = "Location #" . $item['location_id'];
      }
    }

    // Type information
    if (!isset($item['type'])) {
      if (isset($item['assigned_server_type']) && !empty($item['assigned_server_type'])) {
        $item['type'] = $item['assigned_server_type'];
      } else if (isset($item['order_type']) && !empty($item['order_type'])) {
        $item['type'] = $item['order_type'];
      } else {
        $item['type'] = 'Dedicated Server'; // Default type
      }
    }

    // Status information
    if (!isset($item['status'])) {
      if (isset($item['order_status']) && !empty($item['order_status'])) {
        $item['status'] = $item['order_status'];
      } else {
        $item['status'] = 'Active'; // Default status
      }
    }

    // Format price fields
    if (isset($item['order_price'])) {
      $item['price'] = '€' . number_format($item['order_price'], 2, '.', ',');
    }

    if (isset($item['requirement_price'])) {
      $item['recurring_price'] = '€' . number_format($item['requirement_price'], 2, '.', ',');
    }

    // Set payment period if not present
    if (!isset($item['payment_period']) || empty($item['payment_period'])) {
      $item['payment_period'] = 'monthly';
    }

    // Add database_id for compatibility with frontend
    $item['database_id'] = $item['id'];

    // Add explicit fields to ensure frontend has all the data it needs
    $item['item_id'] = $item['id'];

    // Add a field to indicate this data comes directly from the database
    $item['data_source'] = 'database_with_comprehensive_joins';

    // Add a debug field with the IDs for troubleshooting
    $item['debug_ids'] = [
      'id' => $item['id'],
      'cpu_id' => $item['cpu_id'] ?? null,
      'storage_id' => $item['storage_id'] ?? null,
      'bandwidth_id' => $item['bandwidth_id'] ?? null,
      'location_id' => $item['location_id'] ?? null,
      'server_id' => $item['server_id'] ?? null,
      'order_id' => $item['order_id'] ?? null,
    ];

    return $item;
  } catch (Exception $e) {
    error_log("Error fetching order item by ID with enhanced data: " . $e->getMessage());

    // Try fallback query without all the joins
    try {
      $fallbackStmt = $pdo->prepare("SELECT * FROM orders_items WHERE id = :item_id LIMIT 1");
      $fallbackStmt->bindValue(':item_id', $itemId);
      $fallbackStmt->execute();

      $item = $fallbackStmt->fetch(PDO::FETCH_ASSOC);
      if ($item) {
        $item['data_source'] = 'database_basic_fallback';
        $item['database_id'] = $item['id'];
        $item['item_id'] = $item['id'];
      }

      return $item;
    } catch (Exception $fallbackError) {
      error_log("Error in fallback query: " . $fallbackError->getMessage());
      return null;
    }
  }
}

/**
 * Get a single service by ID
 */
function getServiceById($pdo, $serviceId) {
  try {
    // Main query to get service data with direct LEFT JOINs to related tables
    $stmt = $pdo->prepare("SELECT oi.*,
                          dc.cpu as cpu_name, dc.description as cpu_description,
                          ds.name as storage_name, ds.description as storage_description,
                          db.name as bandwidth_name, db.description as bandwidth_description,
                          c.city as location_city, c.name as location_name, ct.name as country_name
                          FROM orders_items oi
                          LEFT JOIN dedicated_cpu dc ON oi.cpu_id = dc.id
                          LEFT JOIN dedicated_storages ds ON oi.storage_id = ds.id
                          LEFT JOIN dedicated_bandwidth db ON oi.bandwidth_id = db.id
                          LEFT JOIN dedicated_cities c ON oi.location_id = c.id
                          LEFT JOIN cities ct ON c.city = ct.id
                          WHERE oi.id = :service_id LIMIT 1");
    $stmt->bindValue(':service_id', $serviceId);
    $stmt->execute();

    $service = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($service) {
      // Add display names for technical specifications - with robust fallbacks

      // CPU handling
      if (!isset($service['cpu'])) {
        if (isset($service['cpu_name']) && !empty($service['cpu_name'])) {
          $service['cpu'] = $service['cpu_name'];
        } else if (isset($service['cpu_description']) && !empty($service['cpu_description'])) {
          $service['cpu'] = $service['cpu_description'];
        } else if (isset($service['cpu_id']) && !empty($service['cpu_id'])) {
          $service['cpu'] = "CPU #" . $service['cpu_id'];
        }
      }

      // Storage handling
      if (!isset($service['storage'])) {
        if (isset($service['storage_name']) && !empty($service['storage_name'])) {
          $service['storage'] = $service['storage_name'];
        } else if (isset($service['storage_description']) && !empty($service['storage_description'])) {
          $service['storage'] = $service['storage_description'];
        } else if (isset($service['storage_id']) && !empty($service['storage_id'])) {
          $service['storage'] = "Storage #" . $service['storage_id'];
        }
      }

      // Bandwidth handling
      if (!isset($service['bandwidth'])) {
        if (isset($service['bandwidth_name']) && !empty($service['bandwidth_name'])) {
          $service['bandwidth'] = $service['bandwidth_name'];
        } else if (isset($service['bandwidth_description']) && !empty($service['bandwidth_description'])) {
          $service['bandwidth'] = $service['bandwidth_description'];
        } else if (isset($service['bandwidth_id']) && !empty($service['bandwidth_id'])) {
          $service['bandwidth'] = "Bandwidth #" . $service['bandwidth_id'];
        }
      }

      // Location handling
      if (!isset($service['location'])) {
        if (isset($service['location_name']) && !empty($service['location_name'])) {
          $service['location'] = $service['location_name'];

          // Add country if available
          if (isset($service['country_name']) && !empty($service['country_name'])) {
            $service['location'] .= ', ' . $service['country_name'];
          }
        } else if (isset($service['location_city']) && !empty($service['location_city'])) {
          $service['location'] = $service['location_city'];

          // Add country if available
          if (isset($service['country_name']) && !empty($service['country_name'])) {
            $service['location'] .= ', ' . $service['country_name'];
          }
        } else if (isset($service['location_id']) && !empty($service['location_id'])) {
          $service['location'] = "Location #" . $service['location_id'];
        }
      }

      // Add any additional fields needed for display
      if (!isset($service['type'])) {
        $service['type'] = 'Dedicated Server'; // Default type
      }

      if (!isset($service['status'])) {
        $service['status'] = 'Pending'; // Default status is now Pending instead of Active
      }

      // Add a data source indicator for debugging
      $service['data_source'] = 'database_with_joins';
    }

    return $service;
  } catch (Exception $e) {
    error_log("Error fetching service by ID with enhanced data: " . $e->getMessage());

    // Fallback to basic query without joins if the query fails
    try {
      $fallbackStmt = $pdo->prepare("SELECT * FROM orders_items WHERE id = :service_id LIMIT 1");
      $fallbackStmt->bindValue(':service_id', $serviceId);
      $fallbackStmt->execute();

      $service = $fallbackStmt->fetch(PDO::FETCH_ASSOC);
      if ($service) {
        $service['data_source'] = 'database_basic';
      }

      return $service;
    } catch (Exception $fallbackError) {
      error_log("Error in fallback query: " . $fallbackError->getMessage());
      return null;
    }
  }
}

function update_service_details($pdo, $adminId, $data) {
  try {
    // Validate required fields
    if (empty($data['service_id'])) {
      return [
        'success' => false,
        'error' => 'Service ID is required'
      ];
    }

    $serviceId = $data['service_id'];

    // Check if service exists
    $checkServiceStmt = $pdo->prepare("SELECT * FROM orders_items WHERE id = :service_id LIMIT 1");
    $checkServiceStmt->bindValue(':service_id', $serviceId);
    $checkServiceStmt->execute();

    $service = $checkServiceStmt->fetch(PDO::FETCH_ASSOC);
    if (!$service) {
      return [
        'success' => false,
        'error' => "Service with ID {$serviceId} not found"
      ];
    }

    // Prepare update fields
    $updateFields = [];
    $params = [':service_id' => $serviceId];

    // Fields that can be updated - ADDED STATUS FIELD HERE
    $allowedFields = [
      'cpu_id', 'storage_id', 'bandwidth_id', 'location_id', 'subnet_id', 'hostname',
      'order_price', 'requirement_price', 'payment_period', 'due_date', 'server_id', 'status'
    ];

    // Build the update query dynamically based on provided fields
    foreach ($allowedFields as $field) {
      if (isset($data[$field]) && $data[$field] !== '') {
        // For price fields, ensure we're storing numeric values without currency symbols
        if ($field === 'order_price' || $field === 'requirement_price') {
          // Remove currency symbol and trim
          $value = preg_replace('/[^0-9.,]/', '', $data[$field]);
          // Convert comma to dot for decimal separator if needed
          $value = str_replace(',', '.', $value);
          // Ensure it's a valid numeric value
          if (is_numeric($value)) {
            $updateFields[] = "$field = :$field";
            $params[":$field"] = $value;
          }
        } else if ($field === 'due_date' || $field === 'date_ordered') {
          // Ensure dates are in the correct format
          $date = date('Y-m-d', strtotime($data[$field]));
          if ($date) {
            $updateFields[] = "$field = :$field";
            $params[":$field"] = $date;
          }
        } else {
          $updateFields[] = "$field = :$field";
          $params[":$field"] = $data[$field];
        }
      }
    }

    // If no fields to update, return error
    if (empty($updateFields)) {
      return [
        'success' => false,
        'error' => 'No valid fields provided for update'
      ];
    }

    // Update the service details in the orders_items table
    $updateQuery = "UPDATE orders_items SET " . implode(", ", $updateFields) . " WHERE id = :service_id";
    $updateStmt = $pdo->prepare($updateQuery);

    foreach ($params as $key => $value) {
      $updateStmt->bindValue($key, $value);
    }

    $updateStmt->execute();

    // Log the update in the activity log
    $adminNameStmt = $pdo->prepare("SELECT first_name, last_name FROM admins WHERE id = :admin_id LIMIT 1");
    $adminNameStmt->bindValue(':admin_id', $adminId);
    $adminNameStmt->execute();
    $adminData = $adminNameStmt->fetch(PDO::FETCH_ASSOC);
    $adminName = $adminData ? $adminData['first_name'] . ' ' . $adminData['last_name'] : "Admin #$adminId";

    $logStmt = $pdo->prepare("
      INSERT INTO activity_log (
        user_id,
        action,
        description,
        user_name,
        timestamp
      ) VALUES (
        :admin_id,
        'update_service',
        :description,
        :admin_name,
        NOW()
      )
    ");

    $logStmt->bindValue(':admin_id', $adminId);
    $logStmt->bindValue(':admin_name', $adminName);
    $logStmt->bindValue(':description', "Updated service ID {$serviceId} details: " . implode(", ", array_keys($params)));
    $logStmt->execute();

    // Fetch the updated service details to return to the client
    // Use the getServiceById function to get complete service data with display names
    $updatedService = getServiceById($pdo, $serviceId);

    return [
      'success' => true,
      'message' => 'Service details updated successfully',
      'service' => $updatedService,
      'updated_fields' => array_keys($params)
    ];
  } catch (Exception $e) {
    error_log("Error in update_service_details: " . $e->getMessage());
    return [
      'success' => false,
      'error' => 'Failed to update service details: ' . $e->getMessage()
    ];
  }
}


function getUserServices($pdo, $userId) {
  try {
    // Validate user ID
    if (empty($userId)) {
      throw new Exception("User ID is required");
    }

    error_log("DEBUG: Starting getUserServices for user ID: $userId");

    // First check if the user exists
    $userCheck = $pdo->prepare("SELECT id FROM users WHERE id = :user_id");
    $userCheck->bindValue(':user_id', $userId);
    $userCheck->execute();
    if (!$userCheck->fetch()) {
      error_log("User ID $userId not found in users table");
      return [];
    }
    error_log("DEBUG: User exists check passed for user ID: $userId");

    // Check if the orders_items table has entries for this user
    $itemCheck = $pdo->prepare("SELECT COUNT(*) FROM orders_items WHERE user_id = :user_id");
    $itemCheck->bindValue(':user_id', $userId);
    $itemCheck->execute();
    $itemCount = $itemCheck->fetchColumn();
    error_log("DEBUG: Found $itemCount items for user ID: $userId");

    if ($itemCount == 0) {
      return [];
    }

    // SIMPLIFIED QUERY: First try with just the orders_items table (no JOINs)
    $sql = "SELECT * FROM orders_items WHERE user_id = :user_id";

    $stmt = $pdo->prepare($sql);
    $stmt->bindValue(':user_id', $userId);
    $stmt->execute();

    $rawResults = $stmt->fetchAll(PDO::FETCH_ASSOC);
    error_log("DEBUG: Raw query returned " . count($rawResults) . " rows");

    if (count($rawResults) === 0) {
      error_log("DEBUG: No results from basic query - this shouldn't happen");
      return [];
    }

    // Basic processing to return minimal service objects
    $services = [];
    foreach ($rawResults as $row) {
      error_log("DEBUG: Processing row with ID: " . $row['id']);

      $service = [
        'id' => $row['id'],
        'user_id' => $row['user_id'],
        'order_id' => $row['order_id'],
        'cpu_id' => $row['cpu_id'],
        'hostname' => $row['hostname'] ?? 'srv-' . $row['id'] . '.example.com',
        'status' => 'Pending', // Default status since we're not joining with orders
        'cpu' => 'CPU ID: ' . $row['cpu_id'],
        'storage' => 'Storage ID: ' . $row['storage_id'],
        'bandwidth' => 'Bandwidth ID: ' . $row['bandwidth_id'],
        'location' => 'Location ID: ' . $row['location_id'],
        'order_price' => $row['order_price'],
        'price' => '€' . number_format($row['order_price'] ?? 0, 2, '.', ','),
        'created' => $row['created_at'],
        'due_date' => $row['due_date'],
      ];

      $services[] = $service;
    }

    error_log("DEBUG: Returning " . count($services) . " services");
    return $services;

  } catch (Exception $e) {
    error_log("Database error in getUserServices: " . $e->getMessage());
    error_log("DEBUG: Exception trace: " . $e->getTraceAsString());
    return [];
  }
}


/**
 * Helper function to ensure orders_items table exists with all required columns
 */
function ensureOrdersItemsTable($pdo) {
  try {
    // Check if the table exists
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'orders_items'");
    if ($tableCheck->rowCount() == 0) {
      error_log("orders_items table does not exist, creating it");

      // Create the table with all necessary columns
      $pdo->exec("CREATE TABLE IF NOT EXISTS orders_items (
        id INT(11) NOT NULL AUTO_INCREMENT,
        order_id INT(11) NOT NULL,
        invoice_item_id INT(11),
        user_id INT(11) NULL,
        server_id INT(11) NULL,
        cpu_id INT(11) NULL,
        bandwidth_id INT(11) NULL,
        location_id INT(11) NULL,
        storage_id INT(11) NULL,
        subnet_id INT(11) NULL,
        additional_ips_id INT(11) NULL,
        order_price DECIMAL(10,2) NULL,
        requirement_price DECIMAL(10,2) NULL,
        payment_period VARCHAR(50) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        hostname VARCHAR(255) NULL COMMENT 'Server hostname',
        date_ordered DATETIME NULL COMMENT 'Date when the order was placed',
        due_date DATETIME NULL COMMENT 'Due date for the service',
        PRIMARY KEY (id),
        KEY order_id (order_id),
        KEY user_id (user_id),
        KEY server_id (server_id),
        KEY additional_ips_id (additional_ips_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");
    } else {
      // Ensure all columns exist
      $columns = [
        'hostname' => "VARCHAR(255) NULL COMMENT 'Server hostname'",
        'date_ordered' => "DATETIME NULL COMMENT 'Date when the order was placed'",
        'due_date' => "DATETIME NULL COMMENT 'Due date for the service'",
        'additional_ips_id' => "INT(11) NULL COMMENT 'ID of the additional IPs package'"
      ];

      foreach ($columns as $column => $definition) {
        $columnCheck = $pdo->query("SHOW COLUMNS FROM orders_items LIKE '$column'");
        if ($columnCheck->rowCount() == 0) {
          $pdo->exec("ALTER TABLE orders_items ADD COLUMN $column $definition");
          error_log("Added $column column to orders_items table");

          // Add index for additional_ips_id if we just created it
          if ($column === 'additional_ips_id') {
            $pdo->exec("ALTER TABLE orders_items ADD INDEX idx_additional_ips_id (additional_ips_id)");
          }
        }
      }
    }
  } catch (Exception $e) {
    error_log("Error ensuring orders_items table: " . $e->getMessage());
  }
}


/**
 * Helper function to migrate orders to orders_items for a specific user
 */
function migrateOrdersToItems($pdo, $userId) {
  try {
    error_log("Migrating orders to orders_items for user ID: $userId");

    // Get all orders for this user
    $ordersStmt = $pdo->prepare("SELECT * FROM orders WHERE owner_id = :user_id");
    $ordersStmt->bindValue(':user_id', $userId);
    $ordersStmt->execute();
    $orders = $ordersStmt->fetchAll(PDO::FETCH_ASSOC);

    error_log("Found " . count($orders) . " orders to migrate");

    foreach ($orders as $order) {
      // Look for invoice and invoice items
      $invoiceStmt = $pdo->prepare("SELECT * FROM invoices WHERE order_id = :order_id LIMIT 1");
      $invoiceStmt->bindValue(':order_id', $order['id']);
      $invoiceStmt->execute();
      $invoice = $invoiceStmt->fetch(PDO::FETCH_ASSOC);

      if ($invoice) {
        $itemsStmt = $pdo->prepare("SELECT * FROM invoice_items WHERE invoice_id = :invoice_id");
        $itemsStmt->bindValue(':invoice_id', $invoice['id']);
        $itemsStmt->execute();
        $items = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);

        if (count($items) > 0) {
          // Create an entry for each invoice item
          foreach ($items as $item) {
            createOrderItemFromInvoiceItem($pdo, $order, $item, $userId);
          }
        } else {
          // Create a placeholder entry if no invoice items found
          createPlaceholderOrderItem($pdo, $order, $userId);
        }
      } else {
        // Create a placeholder entry if no invoice found
        createPlaceholderOrderItem($pdo, $order, $userId);
      }
    }
  } catch (Exception $e) {
    error_log("Error migrating orders to items: " . $e->getMessage());
  }
}

/**
 * Create an order item from an invoice item
 */
function createOrderItemFromInvoiceItem($pdo, $order, $item, $userId) {
  try {
    $insertStmt = $pdo->prepare("INSERT INTO orders_items (
      order_id,
      invoice_item_id,
      user_id,
      order_price,
      requirement_price,
      payment_period,
      hostname,
      date_ordered,
      due_date
    ) VALUES (
      :order_id,
      :invoice_item_id,
      :user_id,
      :order_price,
      :requirement_price,
      :payment_period,
      :hostname,
      :date_ordered,
      :due_date
    )");

    $insertStmt->bindValue(':order_id', $order['id']);
    $insertStmt->bindValue(':invoice_item_id', $item['id']);
    $insertStmt->bindValue(':user_id', $userId);
    $insertStmt->bindValue(':order_price', $item['unit_price'] ?? $order['initial_price'] ?? 0);
    $insertStmt->bindValue(':requirement_price', $order['recurring_price'] ?? 0);
    $insertStmt->bindValue(':payment_period', 'monthly');
    $insertStmt->bindValue(':hostname', 'srv-' . $order['id'] . '.example.com');
    $insertStmt->bindValue(':date_ordered', $order['order_date'] ?? date('Y-m-d H:i:s'));
    $insertStmt->bindValue(':due_date', date('Y-m-d H:i:s'));

    $insertStmt->execute();
    error_log("Created orders_items entry from invoice item ID: " . $item['id']);
  } catch (Exception $e) {
    error_log("Error creating order item from invoice item: " . $e->getMessage());
  }
}

/**
 * Create a placeholder order item
 */
function createPlaceholderOrderItem($pdo, $order, $userId) {
  try {
    $insertStmt = $pdo->prepare("INSERT INTO orders_items (
      order_id,
      invoice_item_id,
      user_id,
      order_price,
      requirement_price,
      payment_period,
      hostname,
      date_ordered,
      due_date
    ) VALUES (
      :order_id,
      0,
      :user_id,
      :order_price,
      :requirement_price,
      :payment_period,
      :hostname,
      :date_ordered,
      :due_date
    )");

    $insertStmt->bindValue(':order_id', $order['id']);
    $insertStmt->bindValue(':user_id', $userId);
    $insertStmt->bindValue(':order_price', $order['initial_price'] ?? 0);
    $insertStmt->bindValue(':requirement_price', $order['recurring_price'] ?? 0);
    $insertStmt->bindValue(':payment_period', 'monthly');
    $insertStmt->bindValue(':hostname', 'srv-' . $order['id'] . '.example.com');
    $insertStmt->bindValue(':date_ordered', $order['order_date'] ?? date('Y-m-d H:i:s'));
    $insertStmt->bindValue(':due_date', date('Y-m-d H:i:s'));

    $insertStmt->execute();
    error_log("Created placeholder orders_items entry for order ID: " . $order['id']);
  } catch (Exception $e) {
    error_log("Error creating placeholder order item: " . $e->getMessage());
  }
}


function get_cpu_details($pdo, $cpuId) {
  try {
    if (empty($cpuId)) {
      return [
        'success' => false,
        'error' => 'CPU ID is required'
      ];
    }

    $stmt = $pdo->prepare("SELECT * FROM dedicated_cpu WHERE id = :id LIMIT 1");
    $stmt->bindValue(':id', $cpuId);
    $stmt->execute();

    $cpu = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$cpu) {
      return [
        'success' => false,
        'error' => "CPU with ID $cpuId not found"
      ];
    }

    return [
      'success' => true,
      'cpu' => $cpu
    ];
  } catch (Exception $e) {
    error_log("Error in get_cpu_details: " . $e->getMessage());
    return [
      'success' => false,
      'error' => 'Database error: ' . $e->getMessage()
    ];
  }
}

/**
 * Get storage details by ID
 */
function get_storage_details($pdo, $storageId) {
  try {
    if (empty($storageId)) {
      return [
        'success' => false,
        'error' => 'Storage ID is required'
      ];
    }

    $stmt = $pdo->prepare("SELECT * FROM dedicated_storages WHERE id = :id LIMIT 1");
    $stmt->bindValue(':id', $storageId);
    $stmt->execute();

    $storage = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$storage) {
      return [
        'success' => false,
        'error' => "Storage with ID $storageId not found"
      ];
    }

    return [
      'success' => true,
      'storage' => $storage
    ];
  } catch (Exception $e) {
    error_log("Error in get_storage_details: " . $e->getMessage());
    return [
      'success' => false,
      'error' => 'Database error: ' . $e->getMessage()
    ];
  }
}

/**
 * Get bandwidth details by ID
 */
function get_bandwidth_details($pdo, $bandwidthId) {
  try {
    if (empty($bandwidthId)) {
      return [
        'success' => false,
        'error' => 'Bandwidth ID is required'
      ];
    }

    $stmt = $pdo->prepare("SELECT * FROM dedicated_bandwidth WHERE id = :id LIMIT 1");
    $stmt->bindValue(':id', $bandwidthId);
    $stmt->execute();

    $bandwidth = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$bandwidth) {
      return [
        'success' => false,
        'error' => "Bandwidth with ID $bandwidthId not found"
      ];
    }

    return [
      'success' => true,
      'bandwidth' => $bandwidth
    ];
  } catch (Exception $e) {
    error_log("Error in get_bandwidth_details: " . $e->getMessage());
    return [
      'success' => false,
      'error' => 'Database error: ' . $e->getMessage()
    ];
  }
}

// Add this code to api_admin_inventory.php

/**
 * Get city details by ID
 */
function get_city_details($pdo, $cityId) {
  try {
    if (empty($cityId)) {
      return [
        'success' => false,
        'error' => 'City ID is required'
      ];
    }

    $stmt = $pdo->prepare("SELECT c.*, co.name AS country_name
                          FROM dedicated_cities c
                          LEFT JOIN cities co ON c.city = co.id
                          WHERE c.id = :id LIMIT 1");
    $stmt->bindValue(':id', $cityId);
    $stmt->execute();

    $city = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$city) {
      return [
        'success' => false,
        'error' => "City with ID $cityId not found"
      ];
    }

    return [
      'success' => true,
      'city' => $city
    ];
  } catch (Exception $e) {
    error_log("Error in get_city_details: " . $e->getMessage());
    return [
      'success' => false,
      'error' => 'Database error: ' . $e->getMessage()
    ];
  }
}




function fixNullUserIds($pdo, $userId) {
  try {
    $nullCheck = $pdo->prepare("SELECT oi.id, oi.order_id
      FROM orders_items oi
      JOIN orders o ON oi.order_id = o.id
      WHERE oi.user_id IS NULL AND o.owner_id = :user_id");
    $nullCheck->bindValue(':user_id', $userId);
    $nullCheck->execute();
    $nullRows = $nullCheck->fetchAll(PDO::FETCH_ASSOC);

    if (!empty($nullRows)) {
      error_log("Found " . count($nullRows) . " rows with NULL user_id that should be updated to $userId");

      foreach ($nullRows as $row) {
        $updateStmt = $pdo->prepare("UPDATE orders_items SET user_id = :user_id WHERE id = :id");
        $updateStmt->bindValue(':user_id', $userId);
        $updateStmt->bindValue(':id', $row['id']);
        $updateStmt->execute();
      }
    }
  } catch (Exception $e) {
    error_log("Error fixing NULL user IDs: " . $e->getMessage());
  }
}

/**
 * Fix orders without corresponding orders_items entries
 */
function fixMissingOrderItems($pdo, $userId) {
  try {
    $missingCheck = $pdo->prepare("SELECT o.id
      FROM orders o
      LEFT JOIN orders_items oi ON o.id = oi.order_id
      WHERE o.owner_id = :user_id AND oi.id IS NULL");
    $missingCheck->bindValue(':user_id', $userId);
    $missingCheck->execute();
    $missingRows = $missingCheck->fetchAll(PDO::FETCH_ASSOC);

    if (!empty($missingRows)) {
      error_log("Found " . count($missingRows) . " orders without corresponding orders_items entries");

      foreach ($missingRows as $row) {
        $orderId = $row['id'];

        // Try to get invoice item ID for this order
        $itemStmt = $pdo->prepare("SELECT ii.id
          FROM invoice_items ii
          JOIN invoices i ON ii.invoice_id = i.id
          WHERE i.order_id = :order_id
          LIMIT 1");
        $itemStmt->bindValue(':order_id', $orderId);
        $itemStmt->execute();
        $itemId = $itemStmt->fetchColumn();

        if ($itemId) {
          // Create orders_items entry with invoice item
          $insertStmt = $pdo->prepare("INSERT INTO orders_items
            (order_id, invoice_item_id, user_id, cpu_id, bandwidth_id, location_id, storage_id, subnet_id, order_price, requirement_price, payment_period, hostname, date_ordered, due_date)
            VALUES
            (:order_id, :invoice_item_id, :user_id, NULL, NULL, NULL, NULL, NULL, 50.00, 50.00, 'monthly', :hostname, NOW(), NOW())");
          $insertStmt->bindValue(':order_id', $orderId);
          $insertStmt->bindValue(':invoice_item_id', $itemId);
          $insertStmt->bindValue(':user_id', $userId);
          $insertStmt->bindValue(':hostname', 'srv-' . $orderId . '.example.com');
          $insertStmt->execute();
        } else {
          // Create placeholder orders_items entry
          $insertStmt = $pdo->prepare("INSERT INTO orders_items
            (order_id, invoice_item_id, user_id, cpu_id, bandwidth_id, location_id, storage_id, subnet_id, order_price, requirement_price, payment_period, hostname, date_ordered, due_date)
            VALUES
            (:order_id, 0, :user_id, NULL, NULL, NULL, NULL, NULL, 50.00, 50.00, 'monthly', :hostname, NOW(), NOW())");
          $insertStmt->bindValue(':order_id', $orderId);
          $insertStmt->bindValue(':user_id', $userId);
          $insertStmt->bindValue(':hostname', 'srv-' . $orderId . '.example.com');
          $insertStmt->execute();
        }
      }
    }
  } catch (Exception $e) {
    error_log("Error fixing missing order items: " . $e->getMessage());
  }
}

/**
 * Helper functions for service data
 *
 * Note: These functions are kept for backward compatibility with other parts of the codebase
 * that might still use them, but they are no longer used by the optimized getUserServices function.
 */
function getCpuInfo($pdo, $cpuId) {
  if (empty($cpuId)) return null;

  try {
    $stmt = $pdo->prepare("SELECT cpu FROM dedicated_cpu WHERE id = :id");
    $stmt->bindValue(':id', $cpuId);
    $stmt->execute();
    return $stmt->fetchColumn();
  } catch (Exception $e) {
    return null;
  }
}

function getBandwidthInfo($pdo, $bandwidthId) {
  if (empty($bandwidthId)) return null;

  try {
    $stmt = $pdo->prepare("SELECT name, speed FROM dedicated_bandwidth WHERE id = :id");
    $stmt->bindValue(':id', $bandwidthId);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($row) {
      return $row['name'] ?? ($row['speed'] . ' Mbps');
    }
    return null;
  } catch (Exception $e) {
    return null;
  }
}

function getLocationInfo($pdo, $locationId) {
  if (empty($locationId)) return null;

  try {
    $stmt = $pdo->prepare("SELECT name, city, country FROM dedicated_cities WHERE id = :id");
    $stmt->bindValue(':id', $locationId);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($row) {
      return !empty($row['name']) ? $row['name'] :
             (!empty($row['city']) ? $row['city'] .
             (!empty($row['country']) ? ', ' . $row['country'] : '') : null);
    }
    return null;
  } catch (Exception $e) {
    return null;
  }
}

function getStorageInfo($pdo, $storageId) {
  if (empty($storageId)) return null;

  try {
    $stmt = $pdo->prepare("SELECT name FROM dedicated_storages WHERE id = :id");
    $stmt->bindValue(':id', $storageId);
    $stmt->execute();
    return $stmt->fetchColumn();
  } catch (Exception $e) {
    return null;
  }
}

// Main API handler
try {
  // Get database connection from auth_functions.php
  global $pdo;

  // Check for endpoint
  if (!isset($_GET['f'])) {
    echo safeJsonEncode([]);
    exit;
  }

  // Try to authenticate admin (but don't abort if it fails)
  $admin_id = 1; // Default admin ID
  try {
    $admin_id = auth_admin();
  } catch (Exception $e) {
    error_log("Authentication warning: " . $e->getMessage());
  }

  // Get request data
  $requestBody = file_get_contents('php://input');
  $data = json_decode($requestBody, true) ?: [];

  // Handle endpoints
  switch ($_GET['f']) {
    case 'get_orders':
      $filters = [
        'status' => isset($data['status']) ? $data['status'] : 'All',
        'search' => isset($data['search']) ? $data['search'] : '',
        'sortField' => isset($data['sortField']) ? $data['sortField'] : 'date',
        'sortDirection' => isset($data['sortDirection']) ? $data['sortDirection'] : 'desc'
      ];

      $orders = getAllOrders($pdo, $filters);
      echo safeJsonEncode($orders);
      break;

    case 'get_upgrade_orders':
      $filters = [
        'status' => isset($data['status']) ? $data['status'] : 'All',
        'search' => isset($data['search']) ? $data['search'] : '',
        'sortField' => isset($data['sortField']) ? $data['sortField'] : 'date',
        'sortDirection' => isset($data['sortDirection']) ? $data['sortDirection'] : 'desc'
      ];

      $upgradeOrders = getUpgradeOrders($pdo, $filters);
      echo safeJsonEncode($upgradeOrders);
      break;

    case 'get_order_stats':
      $stats = getOrderStats($pdo);
      echo safeJsonEncode($stats);
      break;

    case 'get_user_services':
      $userId = isset($data['user_id']) ? $data['user_id'] : '';

      if (empty($userId)) {
        echo safeJsonEncode([
          'success' => false,
          'error' => 'User ID is required'
        ]);
        break;
      }

      $services = getUserServices($pdo, $userId);
      echo safeJsonEncode($services);
      break;

    case 'create_order':
      $result = createOrder($pdo, $admin_id, $data);
      echo safeJsonEncode($result);
      break;

    case 'update_order_status':
      $orderId = isset($data['id']) ? $data['id'] : '';
      $newStatus = isset($data['status']) ? $data['status'] : '';

      if (empty($orderId) || empty($newStatus)) {
        echo safeJsonEncode([
          'success' => false,
          'error' => 'Order ID and status are required'
        ]);
        break;
      }

      $result = updateOrderStatus($pdo, $admin_id, $orderId, $newStatus);
      echo safeJsonEncode($result);
      break;

    case 'get_order_details':
      $orderId = isset($_GET['id']) ? $_GET['id'] : (isset($data['id']) ? $data['id'] : '');

      if (empty($orderId)) {
        echo safeJsonEncode([
          'success' => false,
          'error' => 'Order ID is required'
        ]);
        break;
      }

      $result = getOrderDetails($pdo, $orderId);
      echo safeJsonEncode($result);
      break;

    case 'create_sample_upgrade_orders':
      // Create some sample upgrade orders for testing
      $result = createSampleUpgradeOrders($pdo);
      echo safeJsonEncode($result);
      break;

    case 'update_service_server_id':
      $result = update_service_server_id($pdo, $admin_id, $data);
      echo safeJsonEncode($result);
      break;

    case 'update_service_details':
      $result = update_service_details($pdo, $admin_id, $data);
      echo safeJsonEncode($result);
      break;



      case 'get_cpu_details':
        $cpuId = isset($data['cpu_id']) ? $data['cpu_id'] : null;
        $result = get_cpu_details($pdo, $cpuId);
        echo safeJsonEncode($result);
        break;

     case 'get_storage_details':
       $storageId = isset($data['storage_id']) ? $data['storage_id'] : null;
      $result = get_storage_details($pdo, $storageId);
         echo safeJsonEncode($result);
     break;

    case 'get_bandwidth_details':
        $bandwidthId = isset($data['bandwidth_id']) ? $data['bandwidth_id'] : null;
    $result = get_bandwidth_details($pdo, $bandwidthId);
   echo safeJsonEncode($result);
 break;





 case 'get_order_item_by_id':
  $itemId = isset($data['item_id']) ? $data['item_id'] : '';

  if (empty($itemId)) {
    echo safeJsonEncode([
      'success' => false,
      'error' => 'Item ID is required'
    ]);
    break;
  }

  $item = getOrderItemById($pdo, $itemId);

  if ($item) {
    // Format the response to match what the frontend expects
    echo safeJsonEncode([
      'success' => true,
      'item' => $item
    ]);
  } else {
    // Return error if item not found
    echo safeJsonEncode([
      'success' => false,
      'error' => 'Service item not found'
    ]);
  }
  break;

      case 'get_service_by_id':
        $serviceId = isset($data['service_id']) ? $data['service_id'] : '';

        if (empty($serviceId)) {
          echo safeJsonEncode([
            'success' => false,
            'error' => 'Service ID is required'
          ]);
          break;
        }

        $result = getServiceDetails($pdo, $serviceId);
        echo safeJsonEncode($result);
        break;

    default:
      // Unknown endpoint
      echo safeJsonEncode([]);
  }
} catch (Exception $e) {
  // Log error but don't expose details to client
  error_log("API Error: " . $e->getMessage());
  echo safeJsonEncode([
    'success' => false,
    'error' => 'An error occurred processing your request'
  ]);
}
?>