<?php

require_once("auth_functions.php");

/**
 * Get all IP transit configurations
 */
function getIpTransitConfigurations($pdo) {
  try {
    $stmt = $pdo->query("SELECT id, label, price FROM ip_transit_configuration ORDER BY price ASC");
    $configurations = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return $configurations;
  } catch (Exception $e) {
    error_log("Error fetching IP transit configurations: " . $e->getMessage());
    return [];
  }
}

/**
 * Get all additional IPs options
 */
function getAdditionalIps($pdo) {
  try {
    // Check if the table exists
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'additional_ips'");
    if ($tableCheck->rowCount() == 0) {
      // Create the table if it doesn't exist
      $pdo->exec("CREATE TABLE IF NOT EXISTS additional_ips (
        id INT NOT NULL AUTO_INCREMENT,
        label VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
        price DECIMAL(10,2) DEFAULT NULL,
        PRIMARY KEY (id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;");

      // Insert default values
      $pdo->exec("INSERT INTO additional_ips (label, price) VALUES
        ('1 Additional IP', 5.00),
        ('4 Additional IPs', 15.00),
        ('8 Additional IPs', 25.00)");

      error_log("Created additional_ips table with default values");
    }

    $stmt = $pdo->query("SELECT id, label, price FROM additional_ips ORDER BY price ASC");
    $additionalIps = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return $additionalIps;
  } catch (Exception $e) {
    error_log("Error fetching additional IPs: " . $e->getMessage());
    return [];
  }
}

/**
 * Get all cities
 */
function getCities($pdo) {
  try {
    $cityQuery = "SELECT id, city, datacenter FROM dedicated_cities";
    $cityStmt = $pdo->query($cityQuery);
    $cities = [];

    while ($row = $cityStmt->fetch(PDO::FETCH_ASSOC)) {
      $cities[] = [
        'id' => (int)$row['id'],
        'name' => $row['city'],
        'datacenter' => $row['datacenter']
      ];
    }

    return $cities;
  } catch (Exception $e) {
    error_log("Error fetching cities: " . $e->getMessage());
    return [];
  }
}

// API endpoint to get IP transit options
if ($_GET['f'] == 'get_ip_transit_options') {
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get database connection
    global $pdo;

    // Get all IP transit configurations
    $configurations = getIpTransitConfigurations($pdo);
    $cities = getCities($pdo);
    $additionalIps = getAdditionalIps($pdo);

    // Return the options
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'options' => [
        'configurations' => $configurations,
        'cities' => $cities,
        'additionalIps' => $additionalIps
      ]
    ]);

  } catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// API endpoint to get additional IPs
elseif ($_GET['f'] == 'get_additional_ips') {
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get database connection
    global $pdo;

    // Get additional IPs
    $additionalIps = getAdditionalIps($pdo);

    // Return the options
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'options' => [
        'additionalIps' => $additionalIps
      ]
    ]);

  } catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// API endpoint to add a new IP transit configuration
elseif ($_GET['f'] == 'add_ip_transit_configuration') {
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (empty($data['label']) || !isset($data['price'])) {
      throw new Exception('Label and price are required');
    }

    // Get database connection
    global $pdo;

    // Insert new configuration
    $stmt = $pdo->prepare("INSERT INTO ip_transit_configuration (label, price) VALUES (:label, :price)");
    $stmt->bindValue(':label', $data['label']);
    $stmt->bindValue(':price', $data['price']);
    $stmt->execute();

    $newId = $pdo->lastInsertId();

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'id' => $newId,
      'message' => 'IP transit configuration added successfully'
    ]);

  } catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// API endpoint to update an IP transit configuration
elseif ($_GET['f'] == 'update_ip_transit_configuration') {
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (empty($data['id']) || empty($data['label']) || !isset($data['price'])) {
      throw new Exception('ID, label, and price are required');
    }

    // Get database connection
    global $pdo;

    // Update configuration
    $stmt = $pdo->prepare("UPDATE ip_transit_configuration SET label = :label, price = :price WHERE id = :id");
    $stmt->bindValue(':id', $data['id']);
    $stmt->bindValue(':label', $data['label']);
    $stmt->bindValue(':price', $data['price']);
    $stmt->execute();

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'IP transit configuration updated successfully'
    ]);

  } catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// API endpoint to delete an IP transit configuration
elseif ($_GET['f'] == 'delete_ip_transit_configuration') {
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (empty($data['id'])) {
      throw new Exception('ID is required');
    }

    // Get database connection
    global $pdo;

    // Delete configuration
    $stmt = $pdo->prepare("DELETE FROM ip_transit_configuration WHERE id = :id");
    $stmt->bindValue(':id', $data['id']);
    $stmt->execute();

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'IP transit configuration deleted successfully'
    ]);

  } catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}
