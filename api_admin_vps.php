<?php

require_once("auth_functions.php");



/**
 * Get all VPS configurations
 */
function getVpsConfigurations($pdo) {
  try {
    $stmt = $pdo->query("SELECT id, label, price FROM vps_configurations ORDER BY price ASC");
    $configurations = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return $configurations;
  } catch (Exception $e) {
    error_log("Error fetching VPS configurations: " . $e->getMessage());
    return [];
  }
}

/**
 * Get all VPS IP prices
 */
function getVpsIpPrices($pdo) {
  try {
    $stmt = $pdo->query("SELECT id, label, price FROM additional_ips ORDER BY price ASC");
    $ipPrices = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return $ipPrices;
  } catch (Exception $e) {
    error_log("Error fetching VPS IP prices: " . $e->getMessage());
    return [];
  }
}

/**
 * Get all VPS applications
 */
function getVpsApplications($pdo) {
  try {
    // Note: Using the table name as provided in the schema
    $stmt = $pdo->query("SELECT id, label FROM vps_aplications ORDER BY label ASC");
    $applications = $stmt->fetchAll(PDO::FETCH_ASSOC);

    return $applications;
  } catch (Exception $e) {
    error_log("Error fetching VPS applications: " . $e->getMessage());
    return [];
  }
}

/**
 * Get all VPS options
 */
function getAllVpsOptions($pdo) {
  try {
    // Authenticate admin
    try {
      $admin_id = auth_admin();
    } catch (Exception $e) {
      error_log("Authentication error: " . $e->getMessage());
      return [
        'success' => false,
        'error' => 'Authentication failed'
      ];
    }

    // Get all VPS configurations, IP prices, and applications
    $configurations = getVpsConfigurations($pdo);
    $ipPrices = getVpsIpPrices($pdo);
    $applications = getVpsApplications($pdo);

    // Get cities (locations) from existing table
    $cities = [];
    try {
      $cityQuery = "SELECT id, city, datacenter FROM dedicated_cities";
      $cityStmt = $pdo->query($cityQuery);
      while ($row = $cityStmt->fetch(PDO::FETCH_ASSOC)) {
        $cities[] = [
          'id' => (int)$row['id'],
          'name' => $row['city'],
          'datacenter' => $row['datacenter']
        ];
      }
    } catch (Exception $e) {
      error_log("Error fetching cities: " . $e->getMessage());
      // Continue with empty cities array
    }

    return [
      'success' => true,
      'options' => [
        'configurations' => $configurations,
        'ipPrices' => $ipPrices,
        'applications' => $applications,
        'cities' => $cities
      ]
    ];
  } catch (Exception $e) {
    error_log("Error in getAllVpsOptions: " . $e->getMessage());
    return [
      'success' => false,
      'error' => 'Failed to retrieve VPS options: ' . $e->getMessage()
    ];
  }
}

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Handle API requests
if (isset($_GET['f'])) {
  // Get request data
  $requestBody = file_get_contents('php://input');
  $data = json_decode($requestBody, true) ?: [];

  // Handle endpoints
  switch ($_GET['f']) {
    case 'get_vps_options':
      try {
        $result = getAllVpsOptions($pdo);
        echo json_encode($result);
      } catch (Exception $e) {
        error_log("Error in get_vps_options endpoint: " . $e->getMessage());
        echo json_encode([
          'success' => false,
          'error' => 'Error processing request: ' . $e->getMessage(),
          'debug_info' => [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
          ]
        ]);
      }
      break;

    default:
      // Unknown endpoint
      echo json_encode(['success' => false, 'error' => 'Unknown endpoint']);
  }
} else {
  // No endpoint specified
  echo json_encode(['success' => false, 'error' => 'No endpoint specified']);
}
