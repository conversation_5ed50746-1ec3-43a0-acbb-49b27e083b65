<?php

// Include database connection and functions
require_once("mysql.php");
require_once("api_admin_inventory.php");

// Set execution time limit
set_time_limit(300); // 5 minutes max execution time

// Set error handling
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', '/var/log/switch_traffic_collection.log');

// Start collection time
$start_time = microtime(true);
error_log("Starting switch port traffic collection at " . date('Y-m-d H:i:s'));

try {
    // Call the collection function
    $stats = collectSwitchPortTraffic();
    
    // Calculate execution time
    $execution_time = microtime(true) - $start_time;
    
    // Log results
    error_log(sprintf(
        "Traffic collection completed in %.2f seconds. Processed %d switches (%d failed) and %d ports (%d with data).",
        $execution_time,
        $stats['switches_processed'],
        $stats['switches_failed'],
        $stats['ports_processed'],
        $stats['ports_with_data']
    ));
    
    // Exit with success
    exit(0);
} catch (Exception $e) {
    // Log error
    error_log("Error in traffic collection script: " . $e->getMessage());
    
    // Exit with error
    exit(1);
}