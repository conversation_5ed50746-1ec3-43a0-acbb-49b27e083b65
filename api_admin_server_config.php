<?php

require_once("auth_functions.php");

// Function to add a new operating system - simplified version
function addOperatingSystem() {
  global $pdo;

  try {
    // Basic validation
    $requestBody = file_get_contents('php://input');
    $data = json_decode($requestBody, true) ?: [];

    // Insert new OS with minimal validation
    $os_name = isset($data['os_name']) ? $data['os_name'] : '';
    $os_template = isset($data['os_template']) ? $data['os_template'] : '';
    $logo_url = isset($data['logo_url']) ? $data['logo_url'] : null;

    if (empty($os_name) || empty($os_template)) {
      return [
        'success' => false,
        'error' => 'OS name and template are required'
      ];
    }

    // Use very basic query to reduce chance of errors
    $query = "INSERT INTO dedicated_os (os_name, os_template, logo_url) VALUES (?, ?, ?)";
    $stmt = $pdo->prepare($query);
    $stmt->execute([$os_name, $os_template, $logo_url]);

    $newId = $pdo->lastInsertId();

    return [
      'success' => true,
      'id' => (int)$newId,
      'message' => 'Operating system added successfully'
    ];
  } catch (Exception $e) {
    error_log("Error adding operating system: " . $e->getMessage());
    return [
      'success' => false,
      'error' => 'Failed to add operating system: ' . $e->getMessage()
    ];
  }
}

// Get all server configuration options
function getAllServerOptions() {
  global $pdo;

  try {
    // Get CPU models
    $cpus = [];
    $cpuQuery = "SELECT id, cpu, price FROM dedicated_cpu";
    $cpuStmt = $pdo->query($cpuQuery);
    while ($row = $cpuStmt->fetch(PDO::FETCH_ASSOC)) {
      $cpus[] = [
        'id' => (int)$row['id'],
        'cpu' => $row['cpu'],
        'price' => (float)$row['price']
      ];
    }

    // Get cities
    $cities = [];
    $cityQuery = "SELECT id, city, datacenter FROM cities";
    $cityStmt = $pdo->query($cityQuery);
    while ($row = $cityStmt->fetch(PDO::FETCH_ASSOC)) {
      $cities[] = [
        'id' => (int)$row['id'],
        'name' => $row['city'],
        'datacenter' => $row['datacenter']
      ];
    }

    // Get bandwidths
    $bandwidths = [];
    $bwQuery = "SELECT id, name, speed, price FROM dedicated_bandwidth";
    $bwStmt = $pdo->query($bwQuery);
    while ($row = $bwStmt->fetch(PDO::FETCH_ASSOC)) {
      $bandwidths[] = [
        'id' => (int)$row['id'],
        'name' => $row['name'],
        'speed' => (int)$row['speed'],
        'price' => (float)$row['price']
      ];
    }

    // Get storage options from dedicated_storages table
    $storages = [];
    $storagesQuery = "SELECT id, name, price FROM dedicated_storages";
    $storagesStmt = $pdo->query($storagesQuery);
    while ($row = $storagesStmt->fetch(PDO::FETCH_ASSOC)) {
      $storages[] = [
        'id' => (int)$row['id'],
        'name' => $row['name'],
        'price' => (float)$row['price']
      ];
    }

    // Get subnet options
    $subnets = [];
    $subnetQuery = "SELECT id, name, price FROM dedicated_subnets";
    $subnetStmt = $pdo->query($subnetQuery);
    while ($row = $subnetStmt->fetch(PDO::FETCH_ASSOC)) {
      $subnets[] = [
        'id' => (int)$row['id'],
        'name' => $row['name'],
        'price' => (float)$row['price']
      ];
    }

    // Get operating system options from database
    $os_options = [];
    $osQuery = "SELECT id, os_name as name, os_template, logo_url FROM dedicated_os";
    $osStmt = $pdo->query($osQuery);
    while ($row = $osStmt->fetch(PDO::FETCH_ASSOC)) {
      $os_options[] = [
        'id' => (int)$row['id'],
        'name' => $row['name'],
        'template' => $row['os_template'],
        'logo' => $row['logo_url']
      ];
    }

    // Get configurations
    $configurations = [];
    $configQuery = "SELECT dc.id, dc.cpu_id, dc.cities, dc.bandwidths, dc.storages, dc.subnets
                   FROM dedicated_configurations dc";
    $configStmt = $pdo->query($configQuery);
    while ($row = $configStmt->fetch(PDO::FETCH_ASSOC)) {
      $configurations[] = [
        'id' => (int)$row['id'],
        'cpu_id' => (int)$row['cpu_id'],
        'cities' => $row['cities'],
        'bandwidths' => $row['bandwidths'],
        'storages' => $row['storages'],
        'subnets' => $row['subnets'],
      ];
    }


    $options = [
      'cpus' => $cpus,
      'cities' => $cities,
      'bandwidths' => $bandwidths,
      'storages' => $storages,
      'subnets' => $subnets,
      'operating_systems' => $os_options,
      'configurations' => $configurations
    ];

    return [
      'success' => true,
      'options' => $options
    ];
  } catch (Exception $e) {
    error_log("Error getting server options: " . $e->getMessage());
    return [
      'success' => false,
      'error' => 'Failed to get server options: ' . $e->getMessage()
    ];
  }
}

// Main API handler

try {
  // Check for endpoint
  if (!isset($_GET['f'])) {
    echo json_encode(['success' => false, 'error' => 'No endpoint specified']);
    exit;
  }

  // Get request data
  $requestBody = file_get_contents('php://input');
  $data = json_decode($requestBody, true) ?: [];

  // Handle endpoints
  if ($_GET['f'] == 'get_server_options') {
    $result = getAllServerOptions();
    echo json_encode($result);
  }
  else if ($_GET['f'] == 'add_operating_system') {
    $result = addOperatingSystem();
    echo json_encode($result);
  }
  else {
    // Unknown endpoint
    echo json_encode(['success' => false, 'error' => 'Unknown endpoint']);
  }
} catch (Exception $e) {
  // Log error but don't expose details to client
  error_log("API Error: " . $e->getMessage());
  echo json_encode([
    'success' => false,
    'error' => 'An error occurred processing your request'
  ]);
}
?>