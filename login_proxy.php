<?php
// Simple login proxy to bypass potential server restrictions
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Include the database connection
require_once("mysql.php");

// Get request data
$input = file_get_contents('php://input');
$data = json_decode($input, true) ?: [];

// Check if we have username and password
if (!isset($data['username']) || !isset($data['password'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Missing username or password',
        'error' => 1
    ]);
    exit;
}

$username = trim($data['username']);
$password = $data['password'];

// Validate email format
if (!filter_var($username, FILTER_VALIDATE_EMAIL)) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid email format',
        'error' => 1
    ]);
    exit;
}

try {
    // Get client IP
    $client_ip = $_SERVER['REMOTE_ADDR'];
    
    // Look up the admin
    $stmt = $pdo->prepare("
        SELECT id, email, password
        FROM `admins`
        WHERE LOWER(email) = LOWER(:username)
    ");
    
    $stmt->bindValue(':username', $username);
    $stmt->execute();
    
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Verify credentials
    if (!$admin || !password_verify($password, $admin['password'])) {
        // Log failed attempt if you want
        echo json_encode([
            'success' => false,
            'message' => 'Invalid credentials',
            'error' => 1
        ]);
        exit;
    }
    
    // Successful login - generate token
    $token = bin2hex(random_bytes(16)); // 32 character token
    
    // Update admin session
    $updateStmt = $pdo->prepare("
        UPDATE `admins`
        SET `last_session` = :token,
            `last_ip` = :last_ip,
            `last_login` = NOW()
        WHERE `id` = :admin_id
    ");
    
    $updateStmt->bindValue(':admin_id', $admin['id']);
    $updateStmt->bindValue(':last_ip', $client_ip);
    $updateStmt->bindValue(':token', $token);
    $updateStmt->execute();
    
    // Return success response
    echo json_encode([
        'success' => true,
        'token' => $token,
        'is_admin' => true
    ]);
    
} catch (Exception $e) {
    // Log the error
    error_log("Login proxy error: " . $e->getMessage());
    
    // Return error response
    echo json_encode([
        'success' => false,
        'message' => 'Server error during login',
        'error' => 4
    ]);
}
?>
